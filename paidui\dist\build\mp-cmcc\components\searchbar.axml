<view class="data-v-8be16658"><view class="{{('search-con') + ' ' + 'data-v-8be16658' + ' ' + (j && 'big') + ' ' + (k && 'small') + ' ' + (l && 'care-con')}}"><view a:if="{{a}}" class="toogle-city data-v-8be16658" onTap="{{c}}"><label class="data-v-8be16658">{{b}}</label><label class="icon-arrow-icon data-v-8be16658"></label></view><view class="search-bar data-v-8be16658" onTap="{{i}}"><input class="data-v-8be16658" placeholder="{{d}}" placeholder-style="color:rgba(0,0,0,0.6)" disabled="{{e}}" onFocus="{{f}}" value="{{g}}" onInput="{{h}}"/><label class="iconfont icon-search-icon data-v-8be16658"></label></view></view><province-con class="data-v-8be16658" ref="__r" u-r="provinceCon" onToggleCity="{{m}}" u-i="8be16658-0" onVI="__l"/></view>