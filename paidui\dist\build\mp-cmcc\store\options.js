"use strict";const common_vendor=require("../common/vendor.js");const const_index=require("../const/index.js");const verType=common_vendor.index.getStorageSync("verType")||"cn";function setLocale(verType2){const $pages=getCurrentPages();const $page=$pages[$pages.length-1];if($page){const $vm=$page.$vm;$vm.$set($vm.$i18n,"locale",verType2)}}const useOptionsStore=common_vendor.defineStore("options",{state:()=>({verType:verType}),actions:{setVerType(options={}){if(!options.verType){setLocale(this.verType);return false}this.verType=options.verType;if(this.verType=="care"){this.careClass="care-con"}if(!const_index.ENUM_VERTYPE[this.verType]){this.verType="cn"}setLocale(this.verType);common_vendor.index.setStorage({key:"verType",data:this.verType,success:()=>{}})}}});exports.useOptionsStore=useOptionsStore;
