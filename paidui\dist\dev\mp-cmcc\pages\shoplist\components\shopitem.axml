<view a:if="{{a}}" class="{{('shop-item') + ' ' + 'data-v-4a0ac329' + ' ' + A}}"><view class="shop-info data-v-4a0ac329" onTap="{{l}}"><view class="img-con data-v-4a0ac329"><image class="data-v-4a0ac329" src="{{b}}" alt=""></image></view><view class="shop-info-con data-v-4a0ac329"><view class="shop-title data-v-4a0ac329"><label class="span span-name data-v-4a0ac329">{{c}}</label><view a:if="{{d}}" class="span span-distance data-v-4a0ac329" catchTap="{{f}}">{{e}}</view><view a:if="{{g}}" class="span span-map data-v-4a0ac329" catchTap="{{h}}"><view class="iconfont icon-daohang data-v-4a0ac329"></view></view></view><view class="shop-address data-v-4a0ac329">{{i}}</view><view class="shop-busi-hour data-v-4a0ac329"><label class="span data-v-4a0ac329">营业时间：{{j}}</label><label class="span data-v-4a0ac329">{{k}}</label></view></view></view><view a:if="{{m}}" class="shop-busi-scope data-v-4a0ac329" onTap="{{r}}"><view class="label data-v-4a0ac329">业务范围</view><view class="scope-list data-v-4a0ac329" catchTap="{{o}}"><view a:for="{{n}}" a:for-item="service" a:key="b" class="scope-item data-v-4a0ac329">{{service.a}}</view></view><view hidden="{{!p}}" class="iconfont icon-arrow1 data-v-4a0ac329" catchTap="{{q}}"></view></view><view class="shop-btn data-v-4a0ac329" onTap="{{y}}"><view class="btn-item-label data-v-4a0ac329"> 当前排队{{s}}人 </view><view class="{{('btn-item') + ' ' + 'btn-quhao' + ' ' + 'data-v-4a0ac329' + ' ' + (t && 'disabled')}}" catchTap="{{v}}"> 预约取号 </view><view class="{{('btn-item') + ' ' + 'btn-yuyue' + ' ' + 'data-v-4a0ac329' + ' ' + (w && 'disabled')}}" catchTap="{{x}}"> 在线预约 </view></view><servicepopup class="data-v-4a0ac329" ref="__r" u-r="serPopup" u-i="4a0ac329-0" onVI="__l" u-p="{{z}}"/></view>