{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:custom": "uni -p", "dev:h5": "uni", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-cmcc": "cross-env UNI_OUTPUT_DIR=dist/dev/mp-cmcc uni -p mp-cmcc", "dev:mp-cmcc-local": "cross-env UNI_OUTPUT_DIR=dist/dev/mp-cmcc uni -p mp-cmcc-local", "build:custom": "uni build -p", "build:h5": "uni build", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-cmcc": "cross-env UNI_OUTPUT_DIR=dist/build/mp-cmcc uni build -p mp-cmcc", "build:mp-cmcc-local": "cross-env UNI_OUTPUT_DIR=dist/build/mp-cmcc uni build -p mp-cmcc-local"}, "uni-app": {"scripts": {"mp-cmcc": {"title": "在线取号", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-CMCC": true, "MP-ALIPAY": false}}, "mp-cmcc-local": {"title": "在线取号", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-CMCC": true, "MP-CMCC-LOCAL": true, "MP-ALIPAY": false}}}}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020920240930001", "@dcloudio/uni-components": "3.0.0-4020920240930001", "@dcloudio/uni-h5": "3.0.0-4020920240930001", "@dcloudio/uni-mp-alipay": "3.0.0-4020920240930001", "@dcloudio/uni-mp-weixin": "3.0.0-4020920240930001", "@dcloudio/uni-ui": "^1.5.6", "@vue/reactivity": "^3.5.10", "coordtransform": "^2.1.2", "crypto-js": "^4.2.0", "gcoord": "^1.0.6", "gio-miniprogram-sdk-cdp": "^3.8.19", "gio-uniapp": "file://../sdc/node_modules/gio-uniapp", "pinia": "^2.2.2", "vue": "3.4.21", "vue-i18n": "^9.14.0"}, "devDependencies": {"@dcloudio/types": "^3.4.12", "@dcloudio/uni-automator": "3.0.0-4020920240930001", "@dcloudio/uni-cli-shared": "3.0.0-4020920240930001", "@dcloudio/uni-stacktracey": "3.0.0-4020920240930001", "@dcloudio/vite-plugin-uni": "3.0.0-4020920240930001", "@vue/runtime-core": "3.4.21", "cross-env": "^7.0.3", "postcss": "^8.4.49", "postcss-px-to-viewport-8-plugin": "^1.2.5", "sass": "^1.78.0", "vite": "5.2.8"}}