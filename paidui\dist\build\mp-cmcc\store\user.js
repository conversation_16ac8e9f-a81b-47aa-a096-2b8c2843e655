"use strict";const common_vendor=require("../common/vendor.js");const utils_transitEncrypt=require("../utils/transit-encrypt.js");const{msisdn:msisdn="",msisdnmask:msisdnmask="",passid:passid="",usessionid:usessionid="",gdpUserId:gdpUserId=""}=common_vendor.index.getStorageSync("user")||{};const{province:province,city:city,cityName:cityName,latitude:latitude,longitude:longitude}=common_vendor.index.getStorageSync("geo")||{};const useUserStore=common_vendor.defineStore("user",{state:()=>{const data={isLogined:!!msisdn,info:{msisdn:msisdn?utils_transitEncrypt.decryptAes(msisdn):"",msisdnmask:msisdnmask,passid:passid,usessionid:usessionid,gdpUserId:gdpUserId,province:province||""},geo:{province:province,city:city,cityName:cityName,latitude:latitude,longitude:longitude}};return data},actions:{setUser(user){this.info=Object.assign({},user||{});if(user&&user.msisdn){user.msisdn=utils_transitEncrypt.encryptAes(user.msisdn)}common_vendor.index.setStorageSync("user",user);this.isLogined=user&&user.msisdn?true:false},setGeo(geo){Object.assign(this.geo,geo);common_vendor.index.setStorageSync("geo",this.geo)}}});exports.useUserStore=useUserStore;
