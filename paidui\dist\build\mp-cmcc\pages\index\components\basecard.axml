<view a:if="{{a}}" class="{{('base-card') + ' ' + 'data-v-8eafca19' + ' ' + T}}"><view class="header data-v-8eafca19"><view class="after data-v-8eafca19"></view></view><view class="content data-v-8eafca19"><block a:if="{{b}}"><view class="appoint-time-con data-v-8eafca19" onTap="{{f}}"><view class="label data-v-8eafca19">{{c}}</view><view class="value-con data-v-8eafca19"><view class="value data-v-8eafca19">{{d}}</view><view class="value time data-v-8eafca19">{{e}}</view></view><view class="iconfont icon-arrow-icon data-v-8eafca19"></view></view><view class="tips-title data-v-8eafca19">{{g}}</view><view class="tips-content font30 data-v-8eafca19">{{h}}</view><base-info class="data-v-8eafca19" u-i="8eafca19-0" onVI="__l" u-p="{{i}}"/><view class="btn-con btn-con-center data-v-8eafca19"><view class="{{('btn') + ' ' + 'btn-confirm' + ' ' + 'data-v-8eafca19' + ' ' + (k && 'disabled')}}" onTap="{{l}}">{{j}}</view></view></block><block a:if="{{m}}"><view class="pickupnumber-title data-v-8eafca19">{{n}}</view><view class="pickupnumber-number data-v-8eafca19">{{o}}</view><base-info class="data-v-8eafca19" onGetWaitCount="{{p}}" u-i="8eafca19-1" onVI="__l" u-p="{{q}}"/><view class="btn-con appoint-btn data-v-8eafca19"><view class="{{('btn') + ' ' + 'btn-cancle' + ' ' + 'data-v-8eafca19' + ' ' + (s && 'disabled')}}" onTap="{{t}}">{{r}}</view><view class="{{('btn') + ' ' + 'btn-confirm' + ' ' + 'data-v-8eafca19' + ' ' + (w && 'disabled')}}" onTap="{{x}}">{{v}}</view></view></block><view class="card-footer data-v-8eafca19"></view></view><view class="tips-con data-v-8eafca19"><view class="tips-records data-v-8eafca19"><view class="data-v-8eafca19" onTap="{{z}}">{{y}}</view><view class="iconfont icon-arrow1-icon data-v-8eafca19" onTap="{{A}}"></view></view><block a:if="{{B}}"><view class="tips-title data-v-8eafca19">{{C}}</view><view class="tips-content data-v-8eafca19">{{D}}</view></block></view><uni-popup u-s="{{['d']}}" ref="__r" u-r="appTimePopup" class="{{('data-v-8eafca19') + ' ' + R}}" u-i="8eafca19-2" onVI="__l" u-p="{{S}}"><view class="apptime-popup-title data-v-8eafca19">{{E}}</view><picker-view value="{{J}}" onChange="{{K}}" class="my-picker data-v-8eafca19" indicator-style="{{L}}"><picker-view-column class="data-v-8eafca19"><view a:for="{{F}}" a:for-item="item" class="data-v-8eafca19" style="{{G}}">{{item.a}}</view></picker-view-column><picker-view-column class="data-v-8eafca19"><view a:for="{{H}}" a:for-item="item" class="data-v-8eafca19" style="{{I}}">{{item.a}}</view></picker-view-column></picker-view><view class="btn-con app-btn-con data-v-8eafca19"><view class="btn btn-cancle data-v-8eafca19" onTap="{{N}}">{{M}}</view><view class="btn btn-confirm data-v-8eafca19" onTap="{{P}}">{{O}}</view></view><view class="close data-v-8eafca19" onTap="{{Q}}"></view></uni-popup></view>