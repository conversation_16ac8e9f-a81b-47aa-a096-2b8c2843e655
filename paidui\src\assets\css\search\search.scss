.search-con{
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 690px;
  margin: 0 auto;
  .toogle-city{
    font-size: 28px;
    color:#000;
    .icon-arrow1{
      transform:rotate(90deg);
      margin-right: 21px;
    }
  }
  .search-bar{
    height: 60px;
    background: #f2f2f2;
    border-radius: 30px;
    align-items: center;
    position: relative;
    flex:1;
    .icon-search-icon{
      width:33px;
      height:33px;
      background: url("@/static/home/<USER>") center no-repeat;
      background-size: contain;
      position: absolute;
      left:18px;
      top:14px;
    }
    input{
      background: transparent;
      font-size: 24px;
      color:#000;
      padding-left: 60px;
      height: 60px;
      box-sizing: border-box;
      width:95%;
    }
  }
}
.care-con input{
  font-size: 36px!important;
  font-weight: bold!important;
}
