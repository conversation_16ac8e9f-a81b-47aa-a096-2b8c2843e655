/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.card.data-v-b8440f9a {
  border-top-left-radius: 18rpx;
  border-top-right-radius: 18rpx;
  background: #fff;
  position: relative;
}
.card .title.data-v-b8440f9a {
  height: 72rpx;
  background: #F5F7FB;
  display: flex;
  align-items: center;
  padding: 0 10rpx 0 30rpx;
  border-radius: 18rpx 18rpx 0px 0px;
}
.card .title .title-name.data-v-b8440f9a {
  font-size: 32rpx;
  color: #3D3D3D;
  flex: 1;
}
.card .title .title-btn.data-v-b8440f9a, .card .title .icon-arrow1.data-v-b8440f9a {
  font-size: 24rpx;
  color: #007EFF;
}
.card .content.data-v-b8440f9a {
  padding: 0 30rpx 30rpx;
}
.card .content.data-v-b8440f9a::before {
  content: "";
  display: table;
}
.card-footer.data-v-b8440f9a {
  width: 690rpx;
  height: 13rpx;
  background: url("../../../static/home/<USER>") no-repeat 0 0;
  background-size: cover;
  position: absolute;
  bottom: -13rpx;
  left: 0;
}
.tips-title.data-v-b8440f9a {
  font-size: 28rpx;
  color: #000000;
  line-height: 40rpx;
  margin-bottom: 12rpx;
  font-weight: bold;
}
.tips-content.data-v-b8440f9a {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.6);
  line-height: 38rpx;
}
.btn-con.data-v-b8440f9a {
  display: flex;
  justify-content: space-between;
  padding: 0 39rpx;
}
.btn-con .btn.data-v-b8440f9a {
  width: 259rpx;
  height: 80rpx;
  color: #999;
  text-align: center;
  line-height: 80rpx;
  font-size: 32rpx;
  border-radius: 90rpx;
  font-weight: bold;
}
.btn-con .btn-cancle.data-v-b8440f9a {
  color: #007EFF;
  border: 2rpx solid #007EFF;
}
.btn-con .btn-confirm.data-v-b8440f9a {
  color: #fff;
  background: linear-gradient(262deg, #07B7FF 0%, #0380FF 100%);
}
.btn-con .btn.disabled.data-v-b8440f9a {
  opacity: 0.5;
  cursor: not-allowed;
}
.btn-con-center.data-v-b8440f9a {
  justify-content: center;
}
.btn-con-center .btn-confirm.data-v-b8440f9a {
  width: 363rpx;
}
.content-more.data-v-b8440f9a {
  margin: 60rpx auto 54rpx;
  height: 2rpx;
  border-top: 2rpx dashed #CDCDCD;
  position: relative;
}
.content-more .content-more-con.data-v-b8440f9a {
  width: 149rpx;
  height: 47rpx;
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  font-size: 24rpx;
}
.icon-arrow-blue.data-v-b8440f9a {
  width: 40rpx;
  height: 40rpx;
  display: block;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.icon-arrow-blue-little.data-v-b8440f9a {
  width: 30rpx;
  height: 30rpx;
  display: block;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.care-con .tips-title.data-v-b8440f9a, .care-con .tips-content.data-v-b8440f9a {
  font-size: 32rpx !important;
  font-weight: bold !important;
  line-height: 48rpx;
}
.care-con .tips-title.font30.data-v-b8440f9a, .care-con .tips-content.font30.data-v-b8440f9a {
  font-size: 30rpx !important;
}
.care-con .title-btn.data-v-b8440f9a {
  transform: translate(0, 1rpx) !important;
}
.tips.data-v-b8440f9a {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.4);
  line-height: 32rpx;
  margin-bottom: 30rpx;
}
.tips-p.data-v-b8440f9a {
  display: flex;
  align-items: center;
  justify-content: center;
}
.tips-p .icon-tips.data-v-b8440f9a {
  margin-right: 10rpx;
  color: #FF922A;
  font-size: 22rpx;
}
.tips-title.data-v-b8440f9a {
  margin-top: 30rpx;
}
.care-con.data-v-b8440f9a {
  margin-bottom: 50rpx;
}
.care-con .title.data-v-b8440f9a {
  height: 80rpx;
}
.care-con .title .title-name.data-v-b8440f9a {
  font-size: 36rpx;
  font-weight: bold;
}
.care-con .title .title-btn.data-v-b8440f9a {
  font-size: 32rpx;
  font-weight: bold;
}
.care-con .tips-p.data-v-b8440f9a {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 48rpx;
  text-align: center;
}
.care-con .tips-p .icon-tips.data-v-b8440f9a {
  font-size: 28rpx !important;
}
.care-con .btn-con .btn.data-v-b8440f9a {
  width: 258rpx;
  height: 80rpx;
  font-size: 40rpx;
  line-height: 78rpx;
}
.en-con .btn-con.data-v-b8440f9a {
  display: block;
}
.en-con .btn-con .btn.data-v-b8440f9a {
  width: 559rpx;
  font-size: 28rpx;
}
.en-con .btn-con .btn-confirm.data-v-b8440f9a {
  margin-top: 28rpx;
}
.en-con .tips.data-v-b8440f9a {
  text-align: center;
}
.en-con .tips-p.data-v-b8440f9a {
  display: inline !important;
}
.en-con .tips-p .icon-tips.data-v-b8440f9a {
  font-size: 28rpx !important;
  display: inline-block;
}