<template>
  <div class="empty-con" :class="[careClass]">
    <div class="empty-img"></div>
    <div class="empty-text">
      {{ $t('emptyTip') }}
    </div>
    <div class="empty-btn" @click="refresh">
      {{ $t('emptyBtnTip') }}
    </div>
  </div>
</template>
<script>
import { useOptionsStore } from '@/store/options'
import { mapState } from 'pinia'
export default {
  data() {
    return {
      setTime:10000,
      canRefresh:true
    }
  },
  computed: {
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='care'){
        return 'care-con'
      }
      if(this.verType=='en'){
        return 'en-con'
      }
      return  ''
    }
  },
  methods: {
    refresh() {
      if(!this.canRefresh){
        uni.showToast({
          title:this.$t('refreshTip'),
          duration:2000
        })
        return false
      }
      this.$emit('refresh')
      this.canRefresh = false
      setTimeout(()=>{
        this.canRefresh = true
        clearTimeout()
      },this.setTime)
    }
  }
}
</script>
<style lang="scss" scoped>
.empty-con{
  padding-top:250px;
}
.empty-img{
  width:612px;
  height:318px;
  background: url("@/static/home/<USER>") no-repeat center;
  background-size: contain;
  margin:0 auto;
}
.empty-text{
  font-size: 36px;
  font-weight: bold;
  width: 420px;
  text-align: center;
  color: #000000;
  margin: 24px auto 30px;
  line-height: 54px;
}
.empty-btn{
  display:flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  width: 438px;
  font-size: 32px;
  font-weight: bold;
  border: 2px solid #007EFF;
  color: #007EFF;
  border-radius: 90px;
  margin:0 auto;
  box-sizing: border-box;
}
.care-con .empty-btn{
  width: 258px;
  font-size: 36px;
}
.en-con .empty-text{
  width: 548px;
}
.care-con .empty-text{
  width: 496px;
  line-height: 60px;
  padding:0 32px;
  box-sizing: border-box;
}
</style>
