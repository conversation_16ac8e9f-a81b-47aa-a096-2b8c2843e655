"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_login = require("./utils/login.js");
const store_user = require("./store/user.js");
const utils_gioTrack = require("./utils/gio-track.js");
const locales_index = require("./locales/index.js");
const utils_setupPageStackListener = require("./utils/setup-page-stack-listener.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/shoplist/index.js";
  "./pages/recordlist/index.js";
  "./pages/map/index.js";
}
const _sfc_main = {
  onLaunch: async function() {
    console.log("App Launch");
  },
  onShow: async function() {
    console.log("App Show");
    const user = store_user.useUserStore();
    if (!user.isLogined) {
      await utils_login.login.forceLogin();
    }
    return true;
  },
  onHide: function() {
    console.log("App Hide");
  }
};
const { pageStackListenerMixin } = utils_setupPageStackListener.setupPageStackListener();
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  const i18n = common_vendor.createI18n({
    locale: "zh",
    // 设置默认语言
    fallbackLocale: "zh",
    messages: {
      zh: locales_index.zh,
      en: locales_index.en
    },
    silentFallbackWarn: true,
    missingWarn: false,
    silentTranslationWarn: true,
    fallbackWarn: false
  });
  app.use(i18n);
  app.use(common_vendor.createPinia());
  utils_gioTrack.gdpInit(app);
  app.mixin(pageStackListenerMixin);
  return {
    app,
    Pinia: common_vendor.Pinia
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
