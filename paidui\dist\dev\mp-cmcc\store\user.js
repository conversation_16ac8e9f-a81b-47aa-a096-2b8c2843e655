"use strict";
const common_vendor = require("../common/vendor.js");
const utils_transitEncrypt = require("../utils/transit-encrypt.js");
const {
  msisdn = "",
  msisdnmask = "",
  passid = "",
  usessionid = "",
  gdpUserId = ""
} = common_vendor.index.getStorageSync("user") || {};
const {
  province,
  city,
  cityName,
  latitude,
  longitude
} = common_vendor.index.getStorageSync("geo") || {};
const useUserStore = common_vendor.defineStore("user", {
  state: () => {
    const data = {
      isLogined: !!msisdn,
      info: {
        msisdn: msisdn ? utils_transitEncrypt.decryptAes(msisdn) : "",
        msisdnmask,
        passid,
        usessionid,
        gdpUserId,
        province: province || ""
      },
      geo: {
        province,
        city,
        cityName,
        latitude,
        longitude
      }
    };
    return data;
  },
  actions: {
    setUser(user) {
      this.info = Object.assign({}, user || {});
      if (user && user.msisdn) {
        user.msisdn = utils_transitEncrypt.encryptAes(user.msisdn);
      }
      common_vendor.index.setStorageSync("user", user);
      this.isLogined = user && user.msisdn ? true : false;
    },
    setGeo(geo) {
      Object.assign(this.geo, geo);
      common_vendor.index.setStorageSync("geo", this.geo);
    }
  }
});
exports.useUserStore = useUserStore;
