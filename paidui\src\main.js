import {
	createSSRApp
} from "vue";
import * as <PERSON><PERSON> from 'pinia';
import App from "./App.vue";
import "@/assets/iconfont/iconfont.css";
import {gdpInit} from "@/utils/gio-track.js"
import { createI18n } from 'vue-i18n'
import { zh, en } from "@/locales/index.js"
import { setupPageStackListener } from "@/utils/setup-page-stack-listener.js"
// 在 main.js 中使用
const { pageStackListenerMixin } = setupPageStackListener();
export function createApp() {
	const app = createSSRApp(App);
  const i18n = createI18n({
    locale: 'zh', // 设置默认语言
    fallbackLocale: 'zh',
    messages: {
      zh,
      en
    },
    silentFallbackWarn:true,
    missingWarn:false,
    silentTranslationWarn:true,
    fallbackWarn:false
  })
  app.use(i18n)
  app.use(Pinia.createPinia());
  gdpInit(app)
  app.mixin(pageStackListenerMixin)
	return {
		app,
    Pinia
	};
}
