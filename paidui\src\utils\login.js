import { useUserStore } from "@/store/user.js"
import cmcc from "@/sdk/cmcc.js"
import paiduiApi from "@/api/paidui.js"

export async function checkLogin() {
  const user = useUserStore()
  let msisdn = ''
  // #ifdef MP-CMCC-LOCAL
  msisdn = user.info.msisdn
  // #else
  const appUser = await cmcc.callGetUserInfo()
  msisdn = appUser.phoneNumber
  // #endif
  return !!(user.isLogined && user.info.msisdn == msisdn) ? msisdn : false
}

export async function forceLogin() {
  let islogin = await checkLogin()
  if(!islogin) {
    uni.showLoading()
    let loginRes
    const user = useUserStore()
    // #ifdef MP-CMCC-LOCAL
    const appUser = await cmcc.callGetUserInfo()

    loginRes = await paiduiApi.validateToken({ token: 'testYiCe', loginType : '2' , userInformation: '', mobile: appUser.phoneNumber || appUser.loginId})
    this.userInfo = loginRes.resultData
    user.setUser({...loginRes.resultData,province:100})
    // #else
    const tokenRes = await cmcc.getSsoToken()
    loginRes = await paiduiApi.validateToken({ token: tokenRes.token, loginType : '2' , userInformation: '' })
    this.userInfo = loginRes.resultData
    user.setUser({...loginRes.resultData,province:100})
    // #endif
    uni.hideLoading()
  }
  return true
}


export default {
  checkLogin,
  forceLogin
}
