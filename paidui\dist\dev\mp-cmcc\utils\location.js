"use strict";
const api_paidui = require("../api/paidui.js");
const store_user = require("../store/user.js");
async function getOriginLocation() {
  return new Promise((resolve, reject) => {
    my.getLocation({
      type: 1
    }).then((location) => {
      resolve(location);
    }).catch(() => {
      resolve({});
    });
  });
}
const adCodeMap = {
  11e4: { provinceCode: 100, provinceName: "北京" },
  44e4: { provinceCode: 200, provinceName: "广东" },
  31e4: { provinceCode: 210, provinceName: "上海" },
  12e4: { provinceCode: 220, provinceName: "天津" },
  5e5: { provinceCode: 230, provinceName: "重庆" },
  21e4: { provinceCode: 240, provinceName: "辽宁" },
  32e4: { provinceCode: 250, provinceName: "江苏" },
  42e4: { provinceCode: 270, provinceName: "湖北" },
  51e4: { provinceCode: 280, provinceName: "四川" },
  61e4: { provinceCode: 290, provinceName: "陕西" },
  13e4: { provinceCode: 311, provinceName: "河北" },
  14e4: { provinceCode: 351, provinceName: "山西" },
  41e4: { provinceCode: 371, provinceName: "河南" },
  22e4: { provinceCode: 431, provinceName: "吉林" },
  23e4: { provinceCode: 451, provinceName: "黑龙江" },
  15e4: { provinceCode: 471, provinceName: "内蒙古" },
  37e4: { provinceCode: 531, provinceName: "山东" },
  34e4: { provinceCode: 551, provinceName: "安徽" },
  33e4: { provinceCode: 571, provinceName: "浙江" },
  35e4: { provinceCode: 591, provinceName: "福建" },
  43e4: { provinceCode: 731, provinceName: "湖南" },
  45e4: { provinceCode: 771, provinceName: "广西" },
  36e4: { provinceCode: 791, provinceName: "江西" },
  52e4: { provinceCode: 851, provinceName: "贵州" },
  53e4: { provinceCode: 871, provinceName: "云南" },
  54e4: { provinceCode: 891, provinceName: "西藏" },
  46e4: { provinceCode: 898, provinceName: "海南" },
  62e4: { provinceCode: 931, provinceName: "甘肃" },
  64e4: { provinceCode: 951, provinceName: "宁夏" },
  63e4: { provinceCode: 971, provinceName: "青海" },
  65e4: { provinceCode: 991, provinceName: "新疆" }
};
function getAdProvince(adCode) {
  let adCodeStr = adCode.slice(0, 2);
  return adCodeMap[adCodeStr + "0000"];
}
async function getLocation(optionProvince, optionCity) {
  const user = store_user.useUserStore();
  const locationRes = await getOriginLocation();
  console.log(locationRes, "app:locationRes");
  const {
    latitude = "",
    longitude = "",
    city = "北京",
    adcode = "",
    cityAdcode
  } = locationRes;
  console.log(user.info, "user.info");
  let myCode = cityAdcode || adcode;
  const provinceCode = myCode ? getAdProvince(myCode).provinceCode : user.info.province;
  const cityRes = await api_paidui.paiduiApi.getCityInfoByCityName({
    provinceCode,
    cityName: city.replace("市", "")
  }, provinceCode);
  const cityCode = cityRes && cityRes.resultData && cityRes.resultData.cityCode ? cityRes.resultData.cityCode : "";
  user.setGeo({
    longitude: longitude || "",
    latitude: latitude || "",
    province: optionProvince || provinceCode || user.info.province,
    city: optionCity || cityCode
  });
}
async function getLocationAndProv({ provinceCode, cityCode }) {
  if (provinceCode) {
    await getIndexLocation(provinceCode);
    const user = store_user.useUserStore();
    const cityRes = await api_paidui.paiduiApi.getCityInfoByCode({
      provinceCode,
      cityCode
    });
    let cityName = cityRes.resultData && cityRes.resultData.cityName ? cityRes.resultData.cityName : "";
    user.setGeo({
      ...user.geo,
      province: provinceCode,
      city: cityCode,
      cityName
    });
  } else {
    getLocation();
  }
}
async function getIndexLocation(province) {
  const user = store_user.useUserStore();
  const locationRes = await getOriginLocation();
  const {
    latitude = "",
    longitude = ""
  } = locationRes;
  user.setGeo({
    longitude: longitude || "",
    latitude: latitude || "",
    province: province || user.info.province
  });
}
exports.getLocation = getLocation;
exports.getLocationAndProv = getLocationAndProv;
