"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_user = require("../../../store/user.js");
const common_assets = require("../../../common/assets.js");
const Servicepopup = () => "../../../components/servicepopup.js";
const _sfc_main = {
  props: {
    item: {
      type: Object,
      default() {
        return null;
      }
    },
    isMap: {
      //map页面的店铺列表不展示一些功能
      type: Boolean,
      default: false
    }
  },
  components: {
    Servicepopup
  },
  computed: {
    ...common_vendor.mapState(store_user.useUserStore, ["geo"])
  },
  data() {
    return {
      careClass: "care-con"
    };
  },
  methods: {
    goPickupnumber(item, isDisabled) {
      if (this.isMap) {
        common_vendor.index.navigateTo({ url: `/pages/index/index?stationCode=${item.stationCode}&provinceCode=${item.provinceCode}&cityCode=${item.cityCode}` });
        return false;
      }
      if (!isDisabled) {
        this.goToShop(item);
      }
    },
    showServiceModel(item) {
      if (item.newServiceInfo && item.newServiceInfo.length > 4) {
        this.$refs.serPopup.showServiceModel();
      } else {
        this.goToShop(item);
      }
    },
    goToMap(item) {
      common_vendor.index.navigateTo({ url: `/pages/map/index?stationCode=${item.stationCode}&latitude=${item.latitude}&longitude=${item.longitude}` });
    },
    goToShop(item) {
      if (this.isMap) {
        return false;
      }
      common_vendor.index.navigateTo({ url: `/pages/index/index?stationCode=${item.stationCode}&provinceCode=${item.provinceCode}&cityCode=${item.cityCode}` });
    },
    goNavi(item) {
      this.$emit("goNavi", item);
    }
  }
};
if (!Array) {
  const _component_Servicepopup = common_vendor.resolveComponent("Servicepopup");
  _component_Servicepopup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.item && $props.item.stationCode
  }, $props.item && $props.item.stationCode ? common_vendor.e({
    b: common_assets._imports_0,
    c: common_vendor.t($props.item.stationName),
    d: !$props.isMap && _ctx.geo.latitude
  }, !$props.isMap && _ctx.geo.latitude ? {
    e: common_vendor.t($props.item.distance),
    f: common_vendor.o(($event) => $options.goToMap($props.item))
  } : {}, {
    g: $props.isMap
  }, $props.isMap ? {
    h: common_vendor.o(($event) => $options.goNavi($props.item))
  } : {}, {
    i: common_vendor.t($props.item.stationAddress),
    j: common_vendor.t($props.item.workPeriod),
    k: common_vendor.t($props.item.isBusiness ? "营业中" : "暂停营业"),
    l: common_vendor.o(($event) => $options.goToShop($props.item)),
    m: !$props.isMap
  }, !$props.isMap ? {
    n: common_vendor.f($props.item.serviceInfoShort, (service, k0, i0) => {
      return {
        a: common_vendor.t(service.serviceName),
        b: service.serviceCode
      };
    }),
    o: common_vendor.o(($event) => $options.showServiceModel($props.item)),
    p: $props.item.newServiceInfo && $props.item.newServiceInfo.length > 4,
    q: common_vendor.o(($event) => $options.showServiceModel($props.item)),
    r: common_vendor.o(($event) => $options.goToShop($props.item))
  } : {}, {
    s: common_vendor.t($props.item.totalWaitCount ? $props.item.totalWaitCount : 0),
    t: $props.item.isBusiness == 0 ? 1 : "",
    v: common_vendor.o(($event) => $options.goPickupnumber($props.item, $props.item.isBusiness == 0)),
    w: $props.item.isAppointment == 0 ? 1 : "",
    x: common_vendor.o(($event) => $options.goPickupnumber($props.item, $props.item.isAppointment == 0)),
    y: common_vendor.o(($event) => $options.goToShop($props.item)),
    z: common_vendor.p({
      ["service-info"]: $props.item.newServiceInfo
    }),
    A: common_vendor.n($data.careClass)
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4a0ac329"]]);
my.createComponent(Component);
