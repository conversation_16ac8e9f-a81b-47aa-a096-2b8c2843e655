import gcoord from 'gcoord'
export function callLogin() {
  my.call("showLogin", {
    debug: false
  },
    (ret) => {
      let errCode = ret.errCode //错误码，参见附录
      let isSuccess = ret.isSuccess //1为成功；0为失败
    }
  )
}
/**
 * 查看登录状态
 * @returns
 */
export function callUserStatus() {
  return new Promise((resolve, reject) => {
    my.call('userStatus', {
      debug: false,
    },
      (res) => {
        const status = res.status                //本网登录状态：0未登录；1服务密码登录；2短信验证码登录
        if (status == 0) {
          reject(res)
        } else {
          console.log(res, 'App:UserStatus')
          resolve(res)
        }
      }
    )
  })
}
/**
 * 获取用户基本信息
 * @returns
 */
export function callGetUserInfo() {
  return new Promise((resolve, reject) => {
    my.call('getUserInfo', {
      debug: false,
    },
      (res) => {
        console.log(res, 'App:UserInfo')
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      }
    )
  })
}
/**
 * 检查uid是否有效
 * @returns
 */
export function checkSessionIdIsvalid() {
  return new Promise((resolve, reject) => {
    my.call('checkSessionIsvalid', {
      debug: false,
    },
      (res) => {
        console.log(res, 'App:checkSessionIsvalid')
        if (res) {
          resolve(res)
        } else {
          reject(res)
        }
      }
    )
  })
}
/**
 * 获取移动认证token
 * @returns
 */
export function callGetYDRZToken(sourceId = "019018") {
  return new Promise((resolve, reject) => {
    my.call('getYDRZToken', {
      debug: false,
      sourceId, //目标业务平台sourceId
    },
      (res) => {
        console.log(res, 'App:YDRZToken')
        //是否有凭证
        if (res.token) {
          resolve(res)
        } else {
          reject(res)
        }
      }
    )
  })
}
/**
 * 客户端信息采集
 * @returns
 */
export function callGetCatchInfo({isShowLocationAlert=false}) {
  return new Promise((resolve, reject) => {
    my.call('getCatchInfo', {
      debug: false,
      isShowLocationAlert: isShowLocationAlert,
      isGetLocation: isShowLocationAlert,
    },
      (res) => {
        console.log(res, 'App:getCatchInfo')
        //是否有凭证
        resolve(res)

      }
    )
  })
}
/**
 * 获取地理坐标
 * @returns
 */
export function callGetLocation() {
  return new Promise((resolve, reject) => {
    my.call('getLocation', {
      debug: false,
      type: 1
    },
      (res) => {
        console.log(res, 'App:callGetLocation')
        //是否有凭证
        resolve(res)

      }
    )
  })
}

export function getSsoToken(sourceId) {
  return new Promise((resolve, reject) => {
    callUserStatus().then(() => {
      checkSessionIdIsvalid().then(() => {
        callGetYDRZToken(sourceId).then((res) => {
          resolve(res)
        }).catch((err) => {
          reject(err)
        })
      }).catch(() => {
        callOverTime()
      })
    }).catch(() => {
      callLogin()
    })
  })
}
/**
 *
 * @param {object} param
 * @param {boolean} [param.debug] 调试状态
 * @param {'0'|'1'} [param.type] 0-其他app,1-weibo
 * @param {string} param.packageName app包名
 * @param {string} param.schemeString 跳转scheme
 * @returns
 */
export function openOtherApp({
  debug = false,
  type = '0',
  packageName,
  schemeString
}) {
  return new Promise((resolve,reject) => {
    my.call('openOtherAPP', {
      debug,
      type,
      packageName,
      schemeString
    },
      (res) => {
        if(res.resCode==0 || res=='success'){
          resolve(res)
        }else{
          reject(res)
        }
      }
    )
  })
}
/**
 * 打开导航app
 * @param {object} param
 * @param {'amap'|'baidu'} [param.app] 高德|百度
 * @param {string|number} param.latitude 经度
 * @param {string|number} param.longitude 纬度
 * @param {string} param.address 地址名称
 * @returns
 */
export async function openNaviApp({
  latitude,
  longitude,
  app,
  address,
  name
}) {
  const platform = uni.getSystemInfoSync().platform.toLowerCase()
  let result = gcoord.transform(
    [longitude,latitude],    // 经纬度坐标[116.403988, 39.914266]
    gcoord.BD09,               // 当前坐标系:坐标系：WGS84、BD09-百度坐标、GCJ02-高德坐标
    gcoord.GCJ02                 // 目标坐标系
  );
  if ( app == 'amap' ) {
    // 跳转高德地图
    const queryStr = `?sourceApplication=${encodeURIComponent('中国移动')}&poiname=${encodeURIComponent(address)}&lat=${result[1]||latitude}&lon=${result[0]||longitude}&dev=0`
    return openOtherApp({
      packageName: platform == 'ios' ? 'iosamap' : 'com.autonavi.minimap',
      schemeString: platform == 'ios' ? `iosamap://viewMap${queryStr}` : `androidamap://viewMap${queryStr}`,
    })

  } else if ( app == 'baidu')  {
    // 跳转百度地图
    const queryStr = `?location=${latitude},${longitude}&title=${name}&content=${address}&output=html&src=webapp.baidu.openAPIdemo&coord_type=bd09ll`
    return openOtherApp({
      packageName: platform == 'ios' ? '' : 'com.baidu.BaiduMap',
      schemeString: `baidumap://map/marker${queryStr}`,
    })
  } else if ( app == 'tencent')  {
    // 跳转腾讯地图
    const queryStr = `?marker=coord:${result[1]||latitude},${result[0]||longitude};title:${name};addr:${address}&referer=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77`
    console.log('queryStr',queryStr)
    return openOtherApp({
      packageName: platform == 'ios' ? '' : 'com.tencent.map',
      schemeString: `qqmap://map/marker${queryStr}`,
    })
  }else if ( app == 'apple')  {
    // 跳转苹果地图
    const queryStr = `http://maps.apple.com/?q=${name}&ll=${result[1]||latitude},${result[0]||longitude}&address=${address}&z=10&t=m`
    console.log('queryStr',queryStr)
    return openOtherApp({
      packageName:  "",
      schemeString: queryStr,
    })
  } else {
    return false
  }
}

export default {
  callGetYDRZToken,
  callGetUserInfo,
  callUserStatus,
  callLogin,
  callGetLocation,
  callGetCatchInfo,
  checkSessionIdIsvalid,
  getSsoToken,
  openOtherApp,
  openNaviApp
}
