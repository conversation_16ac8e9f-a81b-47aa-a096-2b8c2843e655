//const BASEURL = 'https://paidui.coc.10086.cn:31012/dr/paidui'
export function request(config) {

  return new Promise((resolve) => {
    uni.request(config)
      .then((res) => {
        resolve(res.data)
      })
      .catch((res) => {
        resolve(res)
      })
  })
}

export default {
  get(url, data) {
    return request({
      url: BASEURL + url,
      method: 'GET',
      data: data
    })
  },
  post(url, data) {
    return request({
      url: BASEURL + url,
      method: 'POST',
      data: data
    })
  }
}
