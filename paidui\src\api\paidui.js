import { request } from '@/utils/request.js'
import { getUUID, parseTime } from '@/utils/utils.js'
import login from '@/utils/login.js'
import transitEncrypt from "@/utils/transit-encrypt.js"
import { useUserStore } from '@/store/user.js'

let ORIGIN , BASEURL
// #ifdef MP-CMCC-LOCAL
ORIGIN = 'https://aibizhallts.it.10086.cn:31017'
// #else
ORIGIN = 'https://paidui.coc.10086.cn'
// #endif
// #ifdef MP-CMCC-LOCAL
BASEURL ='https://aibizhallts.it.10086.cn:31017/paidui'
// #else
BASEURL ='https://paidui.coc.10086.cn:31012/dr/paidui'
// #endif
function handleData(apiCode, bizData, provinceCode) {
  const user = useUserStore()
  const params = {
    encryptData: {
      basicData: {
        "provinceCode": provinceCode || user.geo.province || '', // store.state.commonData.provinceCode ||
        "timeStamp": parseTime(new Date(), '{y}{m}{d}{h}{i}{s}'),
        "signFlag": "0",
        "apiCode": apiCode,
        "messageId": getUUID(),
        "sysCode": "01"
      },
      bizData
    }
  }
  return params
}
async function _request(url, data, method = 'GET', config = {}) {
  const aeskey = transitEncrypt.getPaiduiKey()
  let _data = data
  if( method === 'POST' &&  data?.encryptData) {
    _data = {
      auth: await transitEncrypt.encryptRsa(aeskey),
      encryptData: transitEncrypt.encryptAes(JSON.stringify(data.encryptData), aeskey),
    }
  }
  const res = await request({
    url: BASEURL + url,
    method,
    data: _data,
    // enableCookie:true,
    header: {
      Origin: ORIGIN,
      // cookie: "JSESSIONID=F79DBE0A08214A885701D7BF030BD29Casfsa; accesstoken=F79DBE0A08214A885701D7BF030BD29Casf"
    },
    ...config
  })
  if(res.statusCode === 401 ) {
    const user = useUserStore()
    user.setUser({}) // 重新登录
    login.forceLogin()
    return {bizCode:'401'}
  }
  if(res?.encryptData ) {
    res.data = JSON.parse(transitEncrypt.decryptAes(res.encryptData, aeskey))
    if(res.data.resultData &&  typeof res.data.resultData === 'string'){
      res.data.resultData = JSON.parse(res.data.resultData)
    }
  }
  console.log(url,data, res)
  return res.data
}
const $http = {
  get(url, data, config) {
    return _request(url, data, 'GET', config)
  },
  async post(url, data, config) {
    return _request(url, data, 'POST', config)
  }
}

/**
* (new)获取营业厅列表接口queryHallList
* @param {object} bizData
* @param {string?} bizData.searchCriteria 搜索条件
* @param {string} bizData.mobile 手机号码
* @param {string} bizData.latitude 纬度
* @param {string} bizData.longitude 经度
* @param {number} bizData.currentPage 当前页码
* @param {number} bizData.pageSize 每页条数
*/
export function queryHallList(bizData) {
  return $http.post('/web/v1/queryHallList', handleData("P00001", bizData))
}

/**
* (new)预约取号-营业厅详情接口queryHallDetail
* @param {object} bizData
* @param {string} bizData.stationCode 营业厅编码19位统一编码
*/
export function queryHallDetail(bizData) {
  return $http.post('/web/v1/queryHallDetail', handleData("P00002", bizData))
}

/**
* (new)预约取号-用户排队信息queryUserQueueInfo
* @param {object} bizData
* @param {string?} bizData.stationCode 营业厅编码19位统一编码
* @param {string} bizData.mobile 11位手机号码
*
*/
export function queryUserQueueInfo(bizData) {
  return $http.post('/web/v1/queryUserQueueInfo', handleData("P00003", bizData))
}

/**
* (new)预约取号-用户预约记录queryUserAppRecords {"mobile":"","currentPage":1,"pageSize":1}
* @param {object} bizData
* @param {string} bizData.mobile 11位手机号码
* @param {number} bizData.currentPage 当前页码
* @param {number} bizData.pageSize 每页显示条数
*/
export function queryUserAppRecords(bizData) {
  return $http.post('/web/v1/queryUserAppRecords', handleData("P00004", bizData))
}

/**
* (new)预约取号-用户取号记录queryUserTakeRecords
* @param {object} bizData
* @param {string} bizData.mobile 11位手机号码
*
*/
export function queryUserTakeRecords(bizData) {
  return $http.post('/web/v1/queryUserTakeRecords', handleData("P00005", bizData))
}

/**
* (new)2.6预约取号-可预约时间段queryAppTime
* @param {object} bizData
* @param {string} bizData.stationCode 营业厅编码19位统一编码
* @param {string} bizData.appDate 查询日期 该日期为用户需要进行预约办理业务的时间，例如用户想预约2020年7月10日办理，此处传入2020-07-10即可,格式yyyy-MM-dd
*/
export function queryAppTime(bizData) {
  return $http.post('/web/v1/queryAppTime', handleData("P00006", bizData))
}

/**
* (new)2.7预约取号-预约userAppointment
* @param {object} bizData
* @param {string} bizData.stationCode 营业厅编码19位统一编码
* @param {string} bizData.appDate 查询日期 该日期为用户需要进行预约办理业务的时间，例如用户想预约2020年7月10日办理，此处传入2020-07-10即可,格式yyyy-MM-dd
* @param {string} bizData.startTime 开始时间
* @param {string} bizData.endTime 结束时间
* @param {string} bizData.mobile 手机号
* @param {string} bizData.serviceCode 业务编码
* @param {'001'|'002'|'003'} bizData.carrierOperator 运营商编码 电信：001，移动：002，联通：003
* @param {1|2|3|4|5} bizData.takeChannel 预约渠道 预约渠道：1为一级手厅,2为一级微厅（公众号）,3为二级手厅,4为二级微厅（公众号）,5为小程序渠道
*/
export function userAppointment(bizData) {
  return $http.post('/web/v1/userAppointment', handleData("P00007", bizData))
}

/**
* (new)2.8预约取号-取消预约cancelAppointment
* @param {object} bizData
* @param {string} bizData.mobile 手机号码
* @param {string} bizData.appNo  预约序列号
*/
export function cancelAppointment(bizData) {
  return $http.post('/web/v1/cancelAppointment', handleData("P00008", bizData))
}

/**
* 取消排队 cancelTakeNumber
* @param {object} bizData
* @param {string} bizData.mobile 手机号码
* @param {string} bizData.stationCode  预约序列号
*/
export function cancelTakeNumber(bizData) {
  return $http.post('/web/v1/cancelTakeNumber', handleData("P00008", bizData))
}

/**
* (new)2.9预约取号-普通用户取号（签到）userTakeNumber
* @param {object} bizData
* @param {string} bizData.stationCode 营业厅编码19位统一编码
* @param {1|2|3} bizData.takeType 取号类型：1代表现场扫码取号；2代表在线实时取号，3代表签到取号
* @param {string} bizData.mobile 手机号
* @param {string} bizData.serviceCode 业务编码
* @param {'001'|'002'|'003'} bizData.carrierOperator 运营商编码 电信：001，移动：002，联通：003
* @param {1|2|3|4|5} bizData.takeChannel 预约渠道 预约渠道：1为一级手厅,2为一级微厅（公众号）,3为二级手厅,4为二级微厅（公众号）,5为小程序渠道
*/
export function userTakeNumber(bizData,province) {
  return $http.post('/web/v1/userTakeNumber', handleData("P00009", bizData, province), { timeout: 45000 })
}

/**
* (new)2.10预约取号-营业员代客取号（签到）agentTakeNumber
* @param {object} bizData
* @param {string} bizData.stationCode 营业厅编码19位统一编码
* @param {1|2|3} bizData.takeType 取号类型：1代表现场扫码取号；2代表在线实时取号，3代表签到取号
* @param {string} bizData.mobile 手机号
* @param {string} bizData.serviceCode 业务编码
* @param {string} bizData.queueCode 队列编码 A、V等
* @param {'001'|'002'|'003'} bizData.carrierOperator 运营商编码 电信：001，移动：002，联通：003
* @param {1|2|3|4|5} bizData.takeChannel 预约渠道 预约渠道：1为一级手厅,2为一级微厅（公众号）,3为二级手厅,4为二级微厅（公众号）,5为小程序渠道
*/
export function agentTakeNumber(bizData) {
  return $http.post('/web/v1/agentTakeNumber', handleData("P00010", bizData))
}

/**
* (new)2.11预约取号-营业厅排队信息queryHallQueueInfo
* @param {object} bizData
* @param {string} bizData.stationCode 营业厅编码19位统一编码
*
*/
export function queryHallQueueInfo(bizData) {
  return $http.post('/web/v1/queryHallQueueInfo', handleData("P00011", bizData))
}


/**
  * (new)2.16预约取号-白名单判断 checkWhite
  * @param {object} bizData
  * @param {string} bizData.mobile 手机号
  * @param {string} bizData.stationCode 营业厅编码
*/
export function checkWhite(bizData) {
  return $http.post('/web/v1/checkWhite', handleData("P00016", bizData))
}

/**
  * (new)2.38 公共接口-广告接口 getAdvertisementInfo
  * @param {object} bizData
  * @param {string} bizData.stationCode 营业厅编码
  * @param {1|2} bizData.advRegion 广告类型 大屏1，预约取号2
  * @param {0|1|2|3} bizData.advPosition 广告位 （0：大屏；1:厅店详情页面;2取号成功页面;3:预约成功页面
*/
export function getAdvertisementInfo(bizData) {
  return $http.post('/web/v1/getAdvertisementInfo', handleData("P00038", bizData))
}

/**
  * (new)2.39 预约取号-获取省市 getCityInfo
  * @param {object} bizData
*/
export function getCityInfo(bizData) {
  return $http.post('/web/v1/getCityInfo', handleData("P00039", bizData))
}


/**
  * (new)2.40 预约取号-获取省编码，城市名，城市编码 getCityInfoByCityName
  * @param {object} bizData 城市名称
  * @param {string} bizData.cityName 城市名称
  * @param {string} bizData.provinceCode 省份编码
*/
export function getCityInfoByCityName(bizData,provinceCode) {
  return $http.post('/web/v1/getCityInfoByCityName', handleData("P00062", bizData, provinceCode))
}


/**
 * 2.61 预约取号--互联网校验token
 * @param {object} bizData
 * @param {string} bizData.token 身份标识
 * @param {string} bizData.mobile 手机号
 * @param {string} bizData.userInformation 浏览器指纹
 * @param {string} bizData.loginType 登录类型
 */
export function validateToken(bizData) {
  return $http.post('/web/v1/uni/sso/validateToken', handleData("P00061", bizData))
}
/**
 * 2.45 预约取号-获取精准营销内容接口
 * @param {object} bizData
 * @param {string} bizData.stationCode 19位营业厅编码"
 * @param {string} bizData.mobile 用户手机号码
 * @param {string} bizData.cityCode 市编码
 */
export function getEmpowermentInfo(bizData) {
  return $http.post('/web/v1/queryEmpInfo', handleData("P00045", bizData))
}
// 排队获取用户登录会员管理凭证接口
export function getUserVoucher(bizData) {
  return $http.post('/web/v1/member/user/voucher/get', handleData("P00046", bizData))
}
// 排队获取店员登录会员管理凭证接口
export function getClerkVoucher(bizData) {
  return $http.post('/web/v1/member/clerk/voucher/get', handleData("P00047", bizData))
}
// 营业厅7天营业时段查询
export function queryWorkPeriodInfo(bizData) {
  return $http.post('/web/v1/queryWorkPeriodInfo', handleData("P00055", bizData))
}
// web
// export function web(bizData) {
//   bizData.type = 1;
//   var href = window.location.href;
//   if (href.indexOf("?") > 0) {
//     href = href.substring(0, href.indexOf("?"))
//   }
//   bizData.operPath = href
//   bizData.provinceCode = store.state.commonData.provinceCode || ""
//   console.log(bizData)
//   return $http.post('/web/v1/web', handleData("P00063", bizData))
// }


// export function placesearch(bizData) {
//   return $http.post('/web/v1/place/v2/search', handleData("P00070", bizData))
// }

// 获取首页，最近厅店，预约信息及厅店详情，取号信息及厅店详情接口
export function queryHomeData(bizData) {
  return $http.post('/web/v1/queryHallHome', handleData("P00071", bizData))
}

// 获取首页，最近厅店排队人数，预约信息及厅店详情，取号信息及厅店详情接口，先展示最近厅店，排队人数默认为0
export function queryHomeDataWithQueueNumber(bizData) {
  return $http.post('/web/v1/queryHallHomeSc', handleData("P00072", bizData))
}
/**
 * 获取城市名称的接口
 * @param {object} bizData
 * @param {string} bizData.provinceCode 省编码
 * @param {string} bizData.cityCode 市编码
 */
export function getCityInfoByCode(bizData){
  return $http.post('/web/v1/getCityInfoByCityCode', handleData("P00062", bizData))
}

// new
export default {
  queryHomeData,
  queryHomeDataWithQueueNumber,
  queryHallList,
  queryHallDetail,
  queryUserQueueInfo,
  queryUserAppRecords,
  queryUserTakeRecords,
  queryAppTime,
  userAppointment,
  cancelAppointment,
  cancelTakeNumber,
  userTakeNumber,
  agentTakeNumber,
  queryHallQueueInfo,
  checkWhite,
  getAdvertisementInfo,
  getCityInfo,
  validateToken,
  getEmpowermentInfo,
  getUserVoucher,
  getClerkVoucher,
  queryWorkPeriodInfo,
  getCityInfoByCode,
  getCityInfoByCityName,
  // placesearch
}


