<view class="{{('content') + ' ' + 'data-v-433313ba' + ' ' + b}}"><view a:for="{{a}}" a:for-item="item" class="content-item data-v-433313ba"><view class="label-con data-v-433313ba"><view class="left-box data-v-433313ba"></view><view class="label data-v-433313ba">{{item.a}}</view></view><view class="value data-v-433313ba">{{item.b}}</view></view></view><view class="{{('content-more') + ' ' + 'data-v-433313ba' + ' ' + f}}"><view class="content-more-con data-v-433313ba" onTap="{{e}}"><view class="content-more-text data-v-433313ba">{{c}}   </view><view class="{{('iconfont') + ' ' + 'icon-cc-arrow-down-circle' + ' ' + 'data-v-433313ba' + ' ' + (d && 'arrow-up')}}"></view></view></view><view a:if="{{g}}" class="{{('wait-detail') + ' ' + 'data-v-433313ba' + ' ' + l}}"><view class="wait-detail-title data-v-433313ba"><view class="left-box data-v-433313ba"></view><view class="wait-detail-title-label data-v-433313ba">{{h}}</view><view class="{{('iconfont') + ' ' + 'icon-refresh' + ' ' + 'data-v-433313ba' + ' ' + (i && 'refresh-again')}}" onTap="{{j}}"></view></view><view a:for="{{k}}" a:for-item="item" class="wait-detail-item data-v-433313ba"><view class="label data-v-433313ba">{{item.a}}</view><view class="value data-v-433313ba">{{item.b}}</view></view></view>