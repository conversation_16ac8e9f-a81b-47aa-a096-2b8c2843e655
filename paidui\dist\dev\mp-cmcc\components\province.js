"use strict";
const store_region = require("../store/region.js");
const store_user = require("../store/user.js");
const store_options = require("../store/options.js");
const common_vendor = require("../common/vendor.js");
const region = store_region.useRegionStore();
const _sfc_main = {
  data() {
    return {
      provinceIndex: 0,
      proValue: {}
    };
  },
  props: {
    serviceInfo: {
      type: Object,
      default() {
        return null;
      }
    }
  },
  computed: {
    ...common_vendor.mapState(store_user.useUserStore, ["geo"]),
    ...common_vendor.mapState(store_options.useOptionsStore, ["verType"]),
    provinceList: function() {
      return region.info;
    },
    cityList: function() {
      return this.provinceList && this.provinceList[this.provinceIndex] ? this.provinceList[this.provinceIndex].children : [];
    },
    careClass: function() {
      return this.verType == "care" ? "care-con" : "";
    }
  },
  methods: {
    open() {
      this.$refs.popup.open("left");
      this.provinceIndex = this.provinceList.findIndex((item) => item.provinceCode == this.geo.province);
    },
    closePopup() {
      this.$refs.popup.close();
    },
    getProvince(index, provinceInfo) {
      this.provinceIndex = index;
      this.proValue = {
        province: provinceInfo.provinceCode
      };
    },
    getCity(cityInfo) {
      this.proValue = {
        ...this.proValue,
        city: cityInfo.cityCode,
        cityName: cityInfo.cityName
      };
      this.toogle();
      this.closePopup();
    },
    toogle() {
      this.$emit("toggleCity", this.proValue);
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../node-modules/npm-scope-dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.closePopup && $options.closePopup(...args)),
    b: common_vendor.f($options.provinceList, (provinceInfo, index, i0) => {
      return {
        a: common_vendor.t(provinceInfo.provinceName),
        b: provinceInfo.provinceCode,
        c: $data.provinceIndex == index ? 1 : "",
        d: common_vendor.o(($event) => $options.getProvince(index, provinceInfo))
      };
    }),
    c: common_vendor.f($options.cityList, (cityInfo, k0, i0) => {
      return {
        a: common_vendor.t(cityInfo.cityName),
        b: cityInfo.cityCode,
        c: common_vendor.o(($event) => $options.getCity(cityInfo))
      };
    }),
    d: common_vendor.n($options.careClass),
    e: common_vendor.p({
      ["background-color"]: "#fff"
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3ba217aa"]]);
my.createComponent(Component);
