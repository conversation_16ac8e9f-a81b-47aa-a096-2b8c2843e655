/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.hr.data-v-51b01e1a {
  height: 0;
  border-bottom: 1rpx solid #CCCCCC;
  margin: 0 30rpx;
}
.shop-item.data-v-51b01e1a {
  box-sizing: border-box;
  padding: 30rpx 30rpx 36rpx;
}
.shop-item .shop-info.data-v-51b01e1a {
  display: flex;
}
.shop-item .shop-info .shop-info-con.data-v-51b01e1a {
  flex: 1;
}
.shop-item .shop-info .shop-title.data-v-51b01e1a {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.shop-item .shop-info .shop-title .span-name.data-v-51b01e1a {
  font-size: 36rpx;
  color: #000000;
  line-height: 40rpx;
}
.shop-item .shop-info .icon-daohang-icon.data-v-51b01e1a {
  width: 72rpx;
  height: 72rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.shop-item .shop-address.data-v-51b01e1a, .shop-item .shop-busi-hour.data-v-51b01e1a {
  font-size: 36rpx;
}
.shop-item .shop-address.data-v-51b01e1a {
  display: flex;
  align-items: flex-start;
  color: #0256FF;
  margin-top: 30rpx;
  line-height: 54rpx;
}
.shop-item .shop-address .span-address.data-v-51b01e1a {
  flex: 1;
}
.shop-item .shop-address .icon-address-icon.data-v-51b01e1a {
  width: 25rpx;
  height: 33rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
  margin-right: 8rpx;
  margin-top: 9rpx;
}
.shop-item .shop-busi-hour.data-v-51b01e1a {
  display: flex;
  align-items: center;
  line-height: 50rpx;
  margin-top: 6rpx;
  color: #000;
}
.shop-item .shop-btn.data-v-51b01e1a {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
  justify-content: right;
  flex-direction: row-reverse;
}
.shop-item .shop-btn .btn-item-label.data-v-51b01e1a {
  font-size: 36rpx;
  color: #3D3D3D;
  flex: 1;
  font-weight: bold;
  display: flex;
}
.shop-item .shop-btn .btn-item-label .btn-item-label-item.data-v-51b01e1a:nth-child(2) {
  color: #007EFF;
}
.shop-item .shop-btn .btn-item.data-v-51b01e1a {
  font-weight: 400;
  font-size: 36rpx;
  width: 198rpx;
  box-sizing: border-box;
  border-radius: 90rpx 90rpx 90rpx 90rpx;
  height: 62rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.shop-item .shop-btn .btn-item.btn-quhao.data-v-51b01e1a {
  border: 2rpx solid #007EFF;
  color: #007EFF;
}
.shop-item .shop-btn .btn-item.btn-yuyue.data-v-51b01e1a {
  background: linear-gradient(262deg, #07B7FF 0%, #0380FF 100%);
  color: #fff;
  margin-left: 19rpx;
}
.shop-item .shop-btn .btn-item.disabled.data-v-51b01e1a {
  opacity: 0.5;
  cursor: not-allowed;
}
.en-con .span-name.data-v-51b01e1a, .en-con .shop-address.data-v-51b01e1a, .en-con .shop-busi-hour.data-v-51b01e1a {
  font-size: 28rpx !important;
  line-height: 36rpx !important;
}
.en-con .shop-busi-hour.data-v-51b01e1a, .en-con .shop-btn.data-v-51b01e1a {
  margin-top: 30rpx !important;
}
.en-con .shop-btn .btn-item.data-v-51b01e1a {
  width: 100% !important;
  margin-left: 0px !important;
  font-size: 28rpx !important;
}
.en-con .icon-address-icon.data-v-51b01e1a {
  width: 18rpx !important;
  height: 24rpx !important;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
  margin-right: 8rpx;
  margin-top: 9rpx;
}