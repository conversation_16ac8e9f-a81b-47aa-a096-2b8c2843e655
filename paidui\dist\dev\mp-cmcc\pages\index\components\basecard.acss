/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.card.data-v-6674fa3e {
  border-top-left-radius: 18rpx;
  border-top-right-radius: 18rpx;
  background: #fff;
  position: relative;
}
.card .title.data-v-6674fa3e {
  height: 72rpx;
  background: #F5F7FB;
  display: flex;
  align-items: center;
  padding: 0 10rpx 0 30rpx;
  border-radius: 18rpx 18rpx 0px 0px;
}
.card .title .title-name.data-v-6674fa3e {
  font-size: 32rpx;
  color: #3D3D3D;
  flex: 1;
}
.card .title .title-btn.data-v-6674fa3e, .card .title .icon-arrow1.data-v-6674fa3e {
  font-size: 24rpx;
  color: #007EFF;
}
.card .content.data-v-6674fa3e {
  padding: 0 30rpx 30rpx;
}
.card .content.data-v-6674fa3e::before {
  content: "";
  display: table;
}
.card-footer.data-v-6674fa3e {
  width: 690rpx;
  height: 13rpx;
  background: url("../../../static/home/<USER>") no-repeat 0 0;
  background-size: cover;
  position: absolute;
  bottom: -13rpx;
  left: 0;
}
.tips-title.data-v-6674fa3e {
  font-size: 28rpx;
  color: #000000;
  line-height: 40rpx;
  margin-bottom: 12rpx;
  font-weight: bold;
}
.tips-content.data-v-6674fa3e {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.6);
  line-height: 38rpx;
}
.btn-con.data-v-6674fa3e {
  display: flex;
  justify-content: space-between;
  padding: 0 39rpx;
}
.btn-con .btn.data-v-6674fa3e {
  width: 259rpx;
  height: 80rpx;
  color: #999;
  text-align: center;
  line-height: 80rpx;
  font-size: 32rpx;
  border-radius: 90rpx;
  font-weight: bold;
}
.btn-con .btn-cancle.data-v-6674fa3e {
  color: #007EFF;
  border: 2rpx solid #007EFF;
}
.btn-con .btn-confirm.data-v-6674fa3e {
  color: #fff;
  background: linear-gradient(262deg, #07B7FF 0%, #0380FF 100%);
}
.btn-con .btn.disabled.data-v-6674fa3e {
  opacity: 0.5;
  cursor: not-allowed;
}
.btn-con-center.data-v-6674fa3e {
  justify-content: center;
}
.btn-con-center .btn-confirm.data-v-6674fa3e {
  width: 363rpx;
}
.content-more.data-v-6674fa3e {
  margin: 60rpx auto 54rpx;
  height: 2rpx;
  border-top: 2rpx dashed #CDCDCD;
  position: relative;
}
.content-more .content-more-con.data-v-6674fa3e {
  width: 149rpx;
  height: 47rpx;
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  font-size: 24rpx;
}
.icon-arrow-blue.data-v-6674fa3e {
  width: 40rpx;
  height: 40rpx;
  display: block;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.icon-arrow-blue-little.data-v-6674fa3e {
  width: 30rpx;
  height: 30rpx;
  display: block;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.care-con .tips-title.data-v-6674fa3e, .care-con .tips-content.data-v-6674fa3e {
  font-size: 32rpx !important;
  font-weight: bold !important;
  line-height: 48rpx;
}
.care-con .tips-title.font30.data-v-6674fa3e, .care-con .tips-content.font30.data-v-6674fa3e {
  font-size: 30rpx !important;
}
.care-con .title-btn.data-v-6674fa3e {
  transform: translate(0, 1rpx) !important;
}
.base-card.data-v-6674fa3e {
  position: relative;
}
.base-card .header.data-v-6674fa3e {
  width: 690rpx;
  height: 68rpx;
  background: url("../../../static/home/<USER>") no-repeat;
  background-size: contain;
  padding-top: 32rpx;
  box-sizing: border-box;
}
.base-card .header .after.data-v-6674fa3e {
  content: "";
  display: block;
  width: 630rpx;
  height: 36rpx;
  margin: 0 auto;
  background: linear-gradient(180deg, #D4E4FF 0%, #FFFFFF 26%, #FFFFFF 100%);
}
.base-card .content.data-v-6674fa3e {
  width: 630rpx;
  background: #fff;
  position: relative;
  margin: 0 auto;
  padding: 0px 36rpx 30rpx;
  box-sizing: border-box;
}
.base-card .content .appoint-time-con.data-v-6674fa3e {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  line-height: 38rpx;
  background: #F5F7FB;
  border-radius: 12rpx;
  padding: 0 18rpx;
  box-sizing: border-box;
  margin: 18rpx auto 36rpx;
}
.base-card .content .appoint-time-con .label.data-v-6674fa3e {
  color: rgba(0, 0, 0, 0.6);
  margin-right: 80rpx;
  width: 160rpx;
}
.base-card .content .appoint-time-con .icon-arrow-icon.data-v-6674fa3e {
  width: 24rpx;
  height: 24rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
  margin-left: 18rpx;
}
.base-card .content .appoint-time-con .value-con.data-v-6674fa3e {
  display: flex;
  justify-content: space-between;
  flex: 1;
  align-items: center;
}
.base-card .content .appoint-time-con .value.data-v-6674fa3e {
  color: #000000;
  font-weight: bold;
}
.base-card .content .appoint-time-con .value.time.data-v-6674fa3e {
  display: flex;
  font-size: 26rpx;
}
.base-card .content .pickupnumber-title.data-v-6674fa3e {
  font-size: 28rpx;
  color: #000000;
  line-height: 40rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 18rpx;
}
.base-card .content .pickupnumber-number.data-v-6674fa3e {
  font-size: 180rpx;
  color: #007EFF;
  font-weight: bold;
  line-height: 180rpx;
  text-align: center;
}
.base-card .content.data-v-6674fa3e::before {
  content: "";
  display: table;
}
.base-card .card-footer.data-v-6674fa3e {
  width: 630rpx;
  background-size: contain;
}
.base-card .btn-con.data-v-6674fa3e {
  padding: 0;
}
.tips-con.data-v-6674fa3e {
  width: 100%;
  margin-top: 53rpx;
  padding: 0 30rpx 90rpx;
  box-sizing: border-box;
}
.tips-con .tips-records.data-v-6674fa3e {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  line-height: 48rpx;
  margin-bottom: 36rpx;
}
.apptime-popup-title.data-v-6674fa3e {
  font-size: 36rpx;
  color: #000000;
  line-height: 50rpx;
  font-weight: bold;
  text-align: center;
  margin-top: 30rpx;
}
.my-picker.data-v-6674fa3e {
  margin: 0 30rpx;
  height: 350rpx;
  box-sizing: border-box;
}
.close.data-v-6674fa3e {
  width: 40rpx;
  height: 40rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  position: absolute;
  right: 30rpx;
  top: 0;
  background-size: contain;
}
.app-btn-con.data-v-6674fa3e {
  padding: 0 30rpx 30rpx !important;
}
.app-btn-con .btn.data-v-6674fa3e {
  width: 330rpx;
}
.icon-arrow1-icon.data-v-6674fa3e {
  width: 30rpx;
  height: 30rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.care-con .pickupnumber-title.data-v-6674fa3e {
  font-size: 32rpx !important;
}
.care-con .pickupnumber-number.data-v-6674fa3e {
  font-size: 186rpx !important;
  line-height: 186rpx !important;
}
.care-con .tips-records.data-v-6674fa3e {
  font-size: 32rpx;
  font-weight: bold;
}
.care-con .tips-records .icon-arrow1-icon.data-v-6674fa3e {
  transform: translate(0, 0);
}
.care-con .tips-title.data-v-6674fa3e, .care-con .tips-content.data-v-6674fa3e {
  font-size: 32rpx !important;
  font-weight: bold !important;
  line-height: 48rpx;
}
.care-con .tips-title.font30.data-v-6674fa3e, .care-con .tips-content.font30.data-v-6674fa3e {
  font-size: 30rpx !important;
}
.care-con .tips-con .tips-content.data-v-6674fa3e {
  width: 640rpx;
  margin-left: -5rpx;
}
.care-con .card-footer.data-v-6674fa3e {
  height: 20rpx;
}
.care-con .appoint-time-con.data-v-6674fa3e {
  font-size: 32rpx !important;
}
.care-con .appoint-time-con .time.data-v-6674fa3e {
  font-size: 32rpx !important;
}
.care-con .appoint-time-con .icon-arrow-icon.data-v-6674fa3e {
  width: 38rpx !important;
  height: 38rpx !important;
  margin-left: 6rpx !important;
  transform: translate(0, -1rpx) !important;
}
.care-con .appoint-time-con .label.data-v-6674fa3e {
  width: auto !important;
  margin-right: 19rpx !important;
}
.care-con .apptime-popup-title.data-v-6674fa3e {
  font-size: 40rpx;
}
.care-con .close.data-v-6674fa3e {
  width: 38rpx;
  height: 38rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
  top: 6rpx;
  right: 32rpx;
}
.care-con .app-btn-con .btn.data-v-6674fa3e, .care-con .appoint-btn .btn.data-v-6674fa3e, .care-con .btn-con-center .btn.data-v-6674fa3e {
  height: 80rpx;
  font-size: 40rpx;
}
.en-con .appoint-btn.data-v-6674fa3e {
  width: 600rpx;
  margin-left: -21rpx;
}
.en-con .appoint-btn .btn.data-v-6674fa3e {
  width: 290rpx;
  font-size: 26rpx;
  font-weight: normal;
}
.en-con .apptime-popup-title.data-v-6674fa3e {
  margin: 30rpx 80rpx 0 30rpx;
}
.en-con .btn-con-center .btn-confirm.data-v-6674fa3e {
  width: 512rpx;
}
.en-con .appoint-time-con .label.data-v-6674fa3e {
  margin-right: 0 !important;
  width: 260rpx !important;
}
.en-con .appoint-time-con .value-con.data-v-6674fa3e {
  display: block !important;
  justify-content: end;
}
.en-con .appoint-time-con .value-con .value.data-v-6674fa3e {
  text-align: right;
}
.en-con .appoint-time-con .value-con .time.data-v-6674fa3e {
  justify-content: end;
}
.en-con .appoint-time-con .icon-arrow-icon.data-v-6674fa3e {
  transform: translate(0, 0) !important;
  margin-left: 30rpx !important;
}