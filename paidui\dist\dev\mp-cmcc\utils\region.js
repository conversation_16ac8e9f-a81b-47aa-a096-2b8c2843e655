"use strict";
const api_paidui = require("../api/paidui.js");
const store_region = require("../store/region.js");
function initRegion() {
  const region = store_region.useRegionStore();
  if (region.info && region.info.length > 0) {
    if ((/* @__PURE__ */ new Date()).valueOf() - region.timestamp < 1e3 * 60 * 60 * 24) {
      return region.info;
    }
  }
  api_paidui.getCityInfo().then((res) => {
    if (res.bizCode === "0000") {
      region.setRegion(res.resultData);
      return region.info;
    } else {
      return [];
    }
  }).catch(() => {
    console.log("请求异常", "getCityInfo");
    return [];
  });
}
exports.initRegion = initRegion;
