import gdp from "gio-uniapp"
import gioCompress from 'gio-miniprogram-sdk-cdp/plugins/gioCompress.js'
import { callGetUserInfo } from "@/sdk/cmcc.js"

let appid = ''

function getAppId() {
  if (!appid) {
    appid = my.getAppIdSync().appId.toString();
  }
  return appid
}

export async function gdpInit(Vue) {
  const env = await my.getRunScene()
  const isEnvProd = !!(env && env.envVersion === 'release')
  if ( isEnvProd) {
    gdp('registerPlugins', [gioCompress]);
  }
  gdp('init', '9e4e5fa7244c6b6e', '90be4403373b6463', getAppId(), {
    host: 'jiguang.coc.10086.cn', // 数据发送地址。
    uniVue: Vue,
    dataCollect: true,
    requestTimeout: 500,
    scheme: 'https',
    debug: false,
    autotrack: false, // 关闭无埋点事件发送，生产环境设置为关闭
    compress: true//isEnvProd //压缩代码
  })
}

export async function gdpSetGeneralProps(option = {}) {
  let {cid,clientID,userBrand,loginProvince,loginCity,province,city,token = '',version='',osType=''} = await callGetUserInfo()
  let userInfo = {
    WT_cid:cid||'',
    WT_clientID:clientID||'',
    WT_userBrand:userBrand||'',
    WT_loginProvince:loginProvince||'',
    WT_loginCity:loginCity||'',
    WT_prov: province||'',
    WT_city: city||'',
    WT_token_id: token.replace(/JSESSIONID=([^;]+);.*/,'$1')||''
  }
  let setGeneralPropsOption = {
    WT_appId: getAppId(),
    WT_appqry: getPageInfo(0).split('?')[1] || '',
    WT_page_type: '排队取号',
    WT_plat: '30011',
    WT_aav: version,
    WT_av: `APP_${osType}_${version}`,
    ...option,
    ...userInfo
  }
  // console.log('setGeneralPropsOption',setGeneralPropsOption)
  gdp('setGeneralProps',setGeneralPropsOption)
}
export function gdpSetUserId(userId) {
  gdp('setUserId', userId)
}
export function gdpDcsMultiTrack(type, params) {
  params['WT_es'] = getPageInfo()
  gdp('track', type, params)
}
/**
 * 获取页面信息
 * @param {number} pageIndex 页面历史序号，0为第一页 不传为最后一页
 * @returns
 */
export function getPageInfo(pageIndex) {
  // eslint-disable-next-line no-undef
  const pages = getCurrentPages() // 获取栈实例
  const index = !isNaN(pageIndex) ? pageIndex : pages.length - 1
  const currentPage = pages.length > 0 ? pages[index]['$page']['fullPath'] : '' // 当前页面路径(带参数)
  return currentPage
}

export default {
  gdpInit,
  gdpSetGeneralProps,
  gdpDcsMultiTrack,
  gdpSetUserId
}
