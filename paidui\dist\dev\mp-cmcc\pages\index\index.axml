<view hidden="{{!s}}" class="{{('home') + ' ' + 'data-v-83a5a03c' + ' ' + t}}"><searchbar a:if="{{a}}" class="data-v-83a5a03c" u-i="83a5a03c-0" onVI="__l" u-p="{{b}}"/><view class="container data-v-83a5a03c"><shop-info class="data-v-83a5a03c" onGoStoreList="{{c}}" onGdpTrack="{{d}}" u-i="83a5a03c-1" onVI="__l" u-p="{{e}}"/><view class="card-con data-v-83a5a03c"><base-card a:if="{{f}}" class="data-v-83a5a03c" onChagneTab="{{g}}" onGdpTrack="{{h}}" u-i="83a5a03c-2" onVI="__l" u-p="{{i}}"/><pickupnumber a:if="{{j}}" class="data-v-83a5a03c" onChagneTab="{{k}}" u-i="83a5a03c-3" onVI="__l" u-p="{{l}}"/><appointment a:if="{{m}}" class="data-v-83a5a03c" onChagneTab="{{n}}" u-i="83a5a03c-4" onVI="__l" u-p="{{o}}"/></view></view><view a:if="{{p}}" class="result-con data-v-83a5a03c"><result-com class="data-v-83a5a03c" onChagneTab="{{q}}" u-i="83a5a03c-5" onVI="__l" u-p="{{r}}"/></view></view><e-m-p-t-y a:if="{{v}}" class="data-v-83a5a03c" onRefresh="{{w}}" u-i="83a5a03c-6" onVI="__l"/>