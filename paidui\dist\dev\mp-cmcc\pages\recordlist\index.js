"use strict";
const api_paidui = require("../../api/paidui.js");
const store_user = require("../../store/user.js");
const common_vendor = require("../../common/vendor.js");
const store_shopdetail = require("../../store/shopdetail.js");
const const_index = require("../../const/index.js");
const store_options = require("../../store/options.js");
const utils_gioTrack = require("../../utils/gio-track.js");
const api_yundian = require("../../api/yundian.js");
const shopInfo = store_shopdetail.useShopInfoStore();
const _sfc_main = {
  data() {
    return {
      basetyle: "appointment",
      timeLabel: "",
      appointmentContent: [
        {
          label: "appointTime",
          value: "appointmentTime"
        },
        {
          label: "serviceName",
          value: "serviceName"
        },
        {
          label: "stationCode",
          value: "stationName"
        }
      ],
      appointmentContentEn: [
        {
          label: "appointTime",
          value: "appointmentTime"
        },
        {
          label: "stationCode",
          value: "stationName",
          getEn: true
        },
        {
          label: "stationAddress",
          value: "stationAddress",
          getEn: true
        }
      ],
      pickupnumberContent: [
        {
          label: "takeNumber",
          value: "ticketNo"
        },
        {
          label: "stationCode",
          value: "stationName",
          getEn: true
        },
        {
          label: "stationAddress",
          value: "stationAddress",
          getEn: true
        }
      ],
      items: [
        {
          title: "takeRecord",
          basetyle: "picknumber"
        },
        {
          title: "appointRecord",
          basetyle: "appointment"
        }
      ],
      recordSendData: {
        mobile: null,
        currentPage: 1,
        pageSize: 10
      },
      currentContentInfo: {},
      currentTips: "",
      finished: false,
      showEmpty: false,
      stationEnInfo: {}
    };
  },
  computed: {
    ...common_vendor.mapState(store_user.useUserStore, ["info", "isLogined"]),
    ...common_vendor.mapState(store_options.useOptionsStore, ["verType"]),
    careClass: function() {
      if (this.verType == "care") {
        return "care-con";
      }
      if (this.verType == "en") {
        return "en-con";
      }
      return "";
    },
    currentContent: function() {
      if (this.basetyle == "appointment") {
        this.timeLabel = this.$t("reservedTime");
        if (this.verType === "en") {
          return this.appointmentContentEn;
        } else {
          return this.appointmentContent;
        }
      }
      if (this.basetyle == "picknumber") {
        this.timeLabel = this.$t("takeTime");
        return this.pickupnumberContent;
      }
      return [];
    }
  },
  onLoad(options) {
    this.basetyle = options.basetyle || "picknumber";
    const optionsStore = store_options.useOptionsStore();
    optionsStore.setVerType(options);
    my.setNavigationBar({
      reset: true,
      title: this.$t("mpappTitle")
    });
    this.getData();
  },
  methods: {
    handleChange(current2) {
      this.recordSendData.currentPage = 1;
      this.finished = false;
      this.showEmpty = false;
      this.basetyle = current2.basetyle;
      this.getData();
    },
    getData() {
      if (this.basetyle == "appointment") {
        this.queryUserAppRecords();
      }
      if (this.basetyle == "picknumber") {
        this.queryUserTakeRecords();
      }
      this.multiTrack();
    },
    /**用户预约记录 */
    async queryUserAppRecords() {
      this.recordSendData.mobile = this.info.msisdn;
      api_paidui.paiduiApi.queryUserAppRecords(this.recordSendData).then(async (res) => {
        console.log("用户预约记录", res);
        if (res.bizCode == "0000" && res.resultData.appList && res.resultData.appList.list) {
          if (this.recordSendData.currentPage === 1) {
            this.currentContentInfo = [];
          }
          let textStr = this.verType == "en" ? "TEXTEN" : "TEXT";
          let stationCodes = [], recordList = res.resultData.appList.list;
          recordList.forEach((item) => {
            switch (parseInt(item.appointmentStatus)) {
              case const_index.APPOINTMENTSTATUS.SUCCESS.CODE:
                item.statusText = const_index.APPOINTMENTSTATUS.SUCCESS[textStr];
                break;
              case const_index.APPOINTMENTSTATUS.FAIL.CODE:
                item.statusText = const_index.APPOINTMENTSTATUS.FAIL[textStr];
                break;
              case const_index.APPOINTMENTSTATUS.CANCELED.CODE:
                item.statusText = const_index.APPOINTMENTSTATUS.CANCELED[textStr];
                break;
              case const_index.APPOINTMENTSTATUS.TICKETED.CODE:
                item.statusText = const_index.APPOINTMENTSTATUS.TICKETED[textStr];
                break;
              case const_index.APPOINTMENTSTATUS.ABANDONED.CODE:
                item.statusText = const_index.APPOINTMENTSTATUS.ABANDONED[textStr];
                break;
            }
            item.statusTextColor = item.appointmentStatus == const_index.APPOINTMENTSTATUS.SUCCESS.CODE ? "blue" : "grey";
            item.appointmentTime = item.appDate + " " + item.startTime + "-" + item.endTime;
            item.address = shopInfo.info.stationAddress;
            if (!this.stationEnInfo[item.stationCode]) {
              stationCodes.push(item.stationCode);
            }
          });
          if (this.verType === "en" && stationCodes.length > 0) {
            let enList = await this.queryShopInfo({
              stationCodeList: stationCodes
            });
            enList.forEach((item) => {
              this.stationEnInfo[item.stationCode] = item;
            });
          }
          this.currentContentInfo = this.currentContentInfo.concat(recordList);
          if (this.currentContentInfo.length == 0) {
            this.showEmpty = true;
          }
          if (res.resultData.total > res.resultData.pageSize * res.resultData.pageNo) {
            this.recordSendData.currentPage++;
          } else {
            this.finished = true;
          }
        }
      });
    },
    /**用户取号记录 */
    queryUserTakeRecords() {
      this.recordSendData.mobile = this.info.msisdn;
      api_paidui.paiduiApi.queryUserTakeRecords(this.recordSendData).then(async (res) => {
        console.log("用户取号记录", res);
        if (res.bizCode == "0000" && res.resultData && res.resultData.list) {
          if (this.recordSendData.currentPage === 1) {
            this.currentContentInfo = [];
          }
          let stationCodes = [], recordList = res.resultData.list;
          let textStr = this.verType == "en" ? "TEXTEN" : "TEXT";
          recordList.forEach((item) => {
            if (parseInt(item.status) === const_index.QUEUESTATUS.CANCELED.CODE) {
              item.statusText = const_index.QUEUESTATUS.CANCELED[textStr];
              item.statusTextColor = "grey";
            }
            if (!this.stationEnInfo[item.stationCode]) {
              stationCodes.push(item.stationCode);
            }
          });
          if (this.verType === "en" && stationCodes.length > 0) {
            let enList = await this.queryShopInfo({
              stationCodeList: stationCodes
            });
            enList.forEach((item) => {
              this.stationEnInfo[item.stationCode] = item;
            });
          }
          this.currentContentInfo = this.currentContentInfo.concat(recordList);
          if (this.currentContentInfo.length == 0) {
            this.showEmpty = true;
          }
          if (res.resultData.total > res.resultData.pageSize * res.resultData.pageNo) {
            this.recordSendData.currentPage++;
          } else {
            this.finished = true;
          }
        }
      });
    },
    /**插码 */
    async multiTrack() {
      await utils_gioTrack.gdpSetGeneralProps({
        WT_page_type: "排队取号_" + const_index.ENUM_VERTYPE[this.verType]
      });
      utils_gioTrack.gdpDcsMultiTrack("pageView", {
        "WT_et": "pageview",
        "WT_event": "MPXPageShow",
        "WT_ti": "我的记录"
      });
    },
    queryShopInfo({ stationCodeList }) {
      return new Promise((resolve) => {
        api_yundian.yundianApi.queryShopInfo({ stationCodeList }).then((res) => {
          if (res && res.data && res.data.shopEnInfoList && res.data.shopEnInfoList.length > 0 && this.verType === "en") {
            resolve(res.data.shopEnInfoList);
          }
          resolve([]);
        }).catch(() => {
          resolve([]);
        });
      });
    }
  },
  // 页面处理函数--监听用户上拉触底
  onReachBottom() {
    if (!this.finished) {
      if (current.basetyle == "picknumber") {
        this.queryUserTakeRecords();
      }
      if (current.basetyle == "appointment") {
        this.queryUserAppRecords();
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.items, (item, k0, i0) => {
      return {
        a: common_vendor.t(_ctx.$t(item.title)),
        b: item.basetyle == $data.basetyle ? 1 : "",
        c: common_vendor.o(($event) => $options.handleChange(item))
      };
    }),
    b: common_vendor.n($options.careClass),
    c: common_vendor.f($data.currentContentInfo, (items, k0, i0) => {
      return {
        a: common_vendor.t(items.appTime || items.ticketTime),
        b: common_vendor.t(items.statusText),
        c: common_vendor.n(items.statusTextColor),
        d: common_vendor.f($options.currentContent, (item, k1, i1) => {
          return {
            a: common_vendor.t(_ctx.$t(item.label)),
            b: common_vendor.t(item.getEn && $data.stationEnInfo[items.stationCode] ? $data.stationEnInfo[items.stationCode][item.value] : items[item.value])
          };
        })
      };
    }),
    d: common_vendor.t($data.timeLabel),
    e: $data.showEmpty
  }, $data.showEmpty ? {
    f: common_vendor.t(_ctx.$t("noRecord"))
  } : {}, {
    g: common_vendor.n($options.careClass)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b6853b6d"]]);
my.createPage(MiniProgramPage);
