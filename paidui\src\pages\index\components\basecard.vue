<template>
  <div v-if="hallDetail" class="base-card" :class="[careClass]">
    <view class="header">
      <view class="after"></view>
    </view>
    <view class="content">
      <template v-if="baseCardTyle=='baseAppointCard'">
        <view class="appoint-time-con" @click="openAppTime">
          <view class="label">
            {{ $t('appointTime') }}
          </view>
          <view class="value-con">
            <view class="value">
              {{ days[confirmValue[0]] ? days[confirmValue[0]].label : "" }}
            </view>
            <view class="value time">
              {{this.appointmentTime && this.appointmentTime.startTime ? this.appointmentTime.startTime + '-' + this.appointmentTime.endTime : $t('selectText')}}
            </view>
          </view>
          <view class="iconfont icon-arrow-icon"></view>
        </view>
        <view class="tips-title">
          {{ $t('wxTip') }}
        </view>
        <view class="tips-content font30">
          {{ $t('wxAppointTimeTip') }}
        </view>
        <BaseInfo :hall-detail="hallDetail" />
        <view class="btn-con btn-con-center">
          <view class="btn btn-confirm" :class="{'disabled':!(this.appointmentTime && this.appointmentTime.startTime)}" @click="userAppointment()">
            {{ $t('appointConfirm') }}
          </view>
        </view>
      </template>
      <template v-if="baseCardTyle=='baseCard'">
        <view class="pickupnumber-title">
          {{ $t('currentQueue') }}
        </view>
        <view class="pickupnumber-number">
          {{ totalWaitCount }}
        </view>
        <BaseInfo :hall-detail="hallDetail" @getWaitCount="getWaitCount" />
        <view class="btn-con appoint-btn">
          <view class="btn btn-cancle" :class="{'disabled':!isBusiness}" @click="chagneTab('pickupnumberSec',!isBusiness)">
            {{ $t('takeNow') }}
          </view>
          <view class="btn btn-confirm" :class="{'disabled':!isAppointment}"  @click="chagneTab('appointment',!isAppointment)">
            {{ $t('appointOnline') }}
          </view>
        </view>
      </template>
      <view class="card-footer"></view>
    </view>
    <view class="tips-con">
      <view class="tips-records">
        <view @click="goRecord">
          {{ $t('historyRecord') }}
        </view>
        <view class="iconfont icon-arrow1-icon" @click="goRecord"></view>
      </view>
      <template v-if="baseCardTyle=='baseCard'">
        <view class="tips-title">
          {{ $t('wxTip') }}
        </view>
        <view class="tips-content">
          {{ $t('wxCardTip') }}
        </view>
      </template>
    </view>
    <uni-popup ref="appTimePopup" border-radius="10px" background-color="#fff" :class="[careClass]">
      <view class="apptime-popup-title">
        {{ $t('pleaseSelectTitle') }}
      </view>
      <picker-view :value="value" @change="onChange" class="my-picker" :indicator-style="indicatorStyle">
        <picker-view-column>
          <view v-for="item in days" :style="careStyle">{{item.label}}</view>
        </picker-view-column>
        <picker-view-column>
          <view v-for="item in appointmentTimeList" :style="careStyle">
            {{item.startTime ? item.startTime + '~' + item.endTime : item}}
          </view>
        </picker-view-column>
      </picker-view>
      <view class="btn-con app-btn-con">
        <view class="btn btn-cancle" @click="closePopup">
          {{ $t('cancelBtn') }}
        </view>
        <view class="btn btn-confirm" @click="onConfirm">
          {{ $t('confirmBtn') }}
        </view>
      </view>
      <view class="close" @click="closePopup"></view>
    </uni-popup>
  </div>
</template>
<script>
import BaseInfo from "./baseinfo.vue"
import paiduiApi from "@/api/paidui.js"
import {parseTime} from '@/utils/utils.js'
import { useUserStore } from '@/store/user'
import { useOptionsStore } from '@/store/options.js'
import { mapState } from 'pinia'
import { ERRORINFO } from "@/const/index.js"
const user = useUserStore()
export default{
  components:{
    BaseInfo
  },
  props:{
    hallDetail:{
      type:Object,
      default:()=>({})
    },
    baseCardTyle:{
      type:String,
    }
  },
  data(){
    return {
      basicVisible: false,
      indicatorStyle: `height: 50px`,
      days: [],
      value:[0,0],
      appDate:"",
      appointmentTimeList:[],
      appointmentTime:{},
      waitCountData:null,
      confirmValue:[],
      careClass:null,
      careStyle:`height:50px;line-height:50px`
    }
  },
  computed:{
    totalWaitCount:function(){
      if(this.waitCountData && this.waitCountData.isNew){
        return this.waitCountData.waitCount
      }
      if(this.hallDetail.totalWaitCount){
        return this.hallDetail.totalWaitCount
      }
      return 0
    },
    isBusiness:function(){
      return this.hallDetail.takeNumber==1 && this.hallDetail.isBusiness==1
    },
    isAppointment:function(){
      return this.hallDetail.takeNumber==1 && this.hallDetail.isAppointment==1
    },
    ...mapState(useOptionsStore,['verType']),
  },
  created(){
    if(this.verType=='care'){
      this.careClass =  'care-con'
      this.careStyle = 'font-size:20px;height:50px;line-height:50px'
    }
    if(this.verType=='en'){
      this.careClass =  'en-con'
    }
  },
  watch:{
    appDate:function(newVal,oldVal){
      if(oldVal && newVal!=oldVal){
        this.queryAppTime()
      }
    }
  },
  methods:{
    chagneTab(btnType,disabled){
      if(disabled){
        return false
      }
      if(btnType == 'pickupnumberSec'){
        this.userTakeNumber(btnType)
        return false
      }
      this.$emit('gdpTrack','clk','在线预约')
      this.$emit('chagneTab',btnType)
    },
    /**立即取号 */
    userTakeNumber(btnType){
      let data = {
        stationCode: this.hallDetail.stationCode, //  营业厅编码
        takeType: 2, // 1代表现场扫码取号；2代表在线实时取号，3代表签到取号
        mobile: user.info.msisdn, // 手机号
        serviceCode: 'H', // 业务类型编码
        carrierOperator: "002", // 异网标识 001中国电信，002中国移动，003中国联通
        takeChannel: 1, // 预约渠道 预约渠道：1为一级手厅,2为一级微厅（公众号）,3为二级手厅,4为二级微厅（公众号）,5为小程序渠道
      };
      if(this.verType=='en'){
        data.queueVersion='en' // 英文版本
        data.enStationName=this.hallDetail.stationName // 英文名称
        data.enStationAddress=this.hallDetail.stationAddress // 英文名称
      }
      this.$emit('gdpTrack','clk','立即取号')
      let  _this = this
      paiduiApi.userTakeNumber(data).then(res=>{
        console.log(res)
        if(res.bizCode == "0000"){
          _this.$emit('chagneTab',btnType,res)
        }else{
          uni.showToast({title:this.getErrorMsg(res.bizDesc),duration:3000})
        }
      })
      // this.$emit('chagneTab',btnType)
    },
    getAppTime(){
      let date = new Date()
      this.days = [
        {
          label:this.$t('appointToday'),
          value:parseTime(new Date(date.getTime()), '{y}-{m}-{d}')
        },
        {
          label:this.$t('appointTomorrow'),
          value:parseTime(new Date(date.getTime()+1000*60*60*24), '{y}-{m}-{d}')
        }
      ]
      if(this.confirmValue && this.confirmValue.length==0){
        this.appDate = this.days[0].value
      }else{
        this.value = [this.confirmValue[0],this.confirmValue[1]]
        this.appDate = this.days[this.confirmValue[0]].value
      }
      this.queryAppTime()
    },
    /**可预约时间段queryAppTime */
    queryAppTime(){
      let data = {
        appDate:this.appDate,//此处传入2020-07-10即可,格式yyyy-MM-dd
        stationCode:this.hallDetail.stationCode,
      }
      paiduiApi.queryAppTime(data).then(res=>{
        console.log("可预约时间段",res)
        if(res.bizCode == "0000"){
          this.appointmentTimeList = res.resultData.appResources  //没有就是[]
          if(this.appointmentTimeList.length == 0){
            this.appointmentTime = {}
            this.appointmentTimeList = [this.$t('appointNoTime')]
          }
        }else{
          uni.showToast({title:this.getErrorMsg(res.bizDesc),duration:3000})
        }
      })
    },
    userAppointment(){
      if(!(this.appointmentTime && this.appointmentTime.startTime)){
        return false
      }
      this.$emit('gdpTrack','clk','确定预约')
      let data = {
        bookingPeriodId:this.appointmentTime.bookingPeriodId, // 预约时段ID
        stationCode: this.hallDetail.stationCode, // 营业厅编码
        appDate: this.appDate, // 预约办理日期
        mobile: user.info.msisdn, // 预约手机号
        appChannel: 1, // 预约渠道：1为一级手厅,2为一级微厅（公众号）,3为二级手厅,4为二级微厅（公众号）,5为小程序渠道
        serviceCode: 'H', // 业务类型编码
        carrierOperator: '002', // 异网标识 001中国电信 002中国移动 003中国联通
        startTime: this.appointmentTime.startTime, // 预约开始时间点
        endTime: this.appointmentTime.endTime, // 预约结束时间点
      };
      if(this.verType=='en'){
        data.queueVersion='en' // 英文版本
        data.enStationName=this.hallDetail.stationName // 英文名称
        data.enStationAddress=this.hallDetail.stationAddress // 英文名称
      }
      let _this = this
      paiduiApi.userAppointment(data).then(res=>{
        console.log("预约结果",res)
        if(res.bizCode=="0000"){
          _this.$emit('chagneTab',"appointmentSec")
        } else{
          uni.showToast({title:this.getErrorMsg(res.bizDesc),duration:3000})
        }

      })
      // this.$emit('chagneTab',"appointmentSec")
    },
    getErrorMsg(msg){
      if(this.verType=='en' && msg.includes('当天最大预约取号操作次数')){
        const numbers = msg.replace(/(.*[^\d])(\d+)(.*)/g,"You've hit the daily cap of $2 appointments!")
        console.log(numbers)
        return numbers
      }
      if(this.verType=='en'){
        return ERRORINFO[msg] || "The system is busy. Please try again later"
      }
      return msg
    },
    openAppTime(){
      this.getAppTime()
      this.$refs.appTimePopup.open('bottom')
    },
    onChange(e){
      this.value = e.value || e.detail.value
      this.appDate = this.days[this.value[0]].value
    },
    onConfirm(){
      this.confirmValue = Object.assign({},this.value)
      this.appDate = this.days[this.value[0]].value
      this.appointmentTime = this.appointmentTimeList[this.value[1]]
      this.closePopup()
    },
    closePopup(){
      this.$refs.appTimePopup.close()
    },
    goRecord(){
      this.$emit('gdpTrack','clk','历史记录')
      let url = "/pages/recordlist/index?basetyle=picknumber"
      uni.navigateTo({url: url})
    },
    getWaitCount(res){
      this.waitCountData = res
    }

  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/home/<USER>";
.base-card{
  position: relative;
  .header{
    width:690px;
    height:68px;
    background: url("@/static/home/<USER>") no-repeat;
    background-size: contain;
    padding-top:32px;
    box-sizing: border-box;
    .after{
      content:"";
      display: block;
      width:630px;
      height:36px;
      margin:0 auto;
      background: linear-gradient( 180deg, #D4E4FF 0%,#FFFFFF 26%, #FFFFFF 100%);
    }
  }
  .content{
    width: 630px;
    background: #fff;
    position: relative;
    margin:0 auto;
    padding:0px 36px 30px;
    box-sizing: border-box;
    .appoint-time-con{
      width:100%;
      height:80px;
      display: flex;
      align-items: center;
      justify-content:space-between;
      font-size: 26px;
      line-height: 38px;
      background: #F5F7FB;
      border-radius: 12px;
      padding:0 18px;
      box-sizing: border-box;
      margin:18px auto 36px;
      .label{
        color: rgba(0,0,0,0.6);
        margin-right: 80px;
        width: 160px;
      }
      .icon-arrow-icon{
        width:24px;
        height:24px;
        background: url("@/static/home/<USER>") center no-repeat;
        background-size: contain;
        margin-left: 18px;
      }
      .value-con{
        display: flex;
        justify-content: space-between;
        flex:1;
        align-items: center;
      }
      .value{
        color: #000000;
        font-weight: bold;
        &.time{
          display: flex;
          font-size: 26px;
        }
      }
    }
    .pickupnumber-title{
      font-size: 28px;
      color: #000000;
      line-height: 40px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 18px;
    }
    .pickupnumber-number{
      font-size: 180px;
      color: #007EFF;
      font-weight: bold;
      line-height: 180px;
      text-align: center;
    }
    &::before{
      content: '';
      display:table;
    }
  }
  .card-footer{
    width:630px;
    background-size: contain;
  }
  .btn-con{
    padding:0;
  }
}
.tips-con{
  width: 100%;
  margin-top:53px;
  padding:0 30px 90px;
  box-sizing: border-box;
  .tips-records{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    line-height: 48px;
    margin-bottom: 36px;
  }
}
.apptime-popup-title{
  font-size: 36px;
  color: #000000;
  line-height: 50px;
  font-weight: bold;
  text-align: center;
  margin-top:30px;
}
.my-picker{
  margin:0 30px;
  height:350px;
  box-sizing: border-box;
}
.close{
  width:40px;
  height:40px;
  background: url("@/static/home/<USER>") center no-repeat;
  position: absolute;
  right:30px;
  top:0;
  background-size: contain;
}
.app-btn-con{
  padding:0 30px 30px !important;
  .btn{
    width: 330px;
  }
}
.icon-arrow1-icon{
  width:30px;
  height:30px;
  background: url("@/static/home/<USER>") center no-repeat;
  background-size: contain;
}
.care-con{
  .pickupnumber-title{
    font-size: 32px!important;
  }
  .pickupnumber-number{
    font-size: 186px!important;
    line-height: 186px!important;
  }
  .tips-records{
    font-size: 32px;
    font-weight: bold;
    .icon-arrow1-icon{
      transform: translate(0,0);
    }
  }
  .tips-title,.tips-content{
    font-size: 32px!important;
    font-weight: bold!important;
    line-height: 48px;
    &.font30{
      font-size: 30px!important;
    }
  }
  .tips-con .tips-content{
    width: 640px;
    margin-left: -5px;
  }
  .card-footer{
    height: 20px;
  }
  .appoint-time-con{
    font-size: 32px!important;
    .time{
      font-size: 32px!important;
    }
    .icon-arrow-icon{
      width: 38px!important;
      height: 38px!important;
      margin-left: 6px!important;
      transform: translate(0,-1px)!important;
    }
    .label{
      width: auto!important;
      margin-right: 19px!important;
    }
  }
  .apptime-popup-title{
    font-size: 40px;
  }
  .close{
    width:38px;
    height:38px;
    background: url("@/static/home/<USER>") center no-repeat;
    background-size: contain;
    top:6px;
    right: 32px;
  }
  .app-btn-con .btn,.appoint-btn .btn,.btn-con-center .btn{
    height: 80px;
    font-size: 40px;
  }
}
.en-con{
  .appoint-btn{
    width:600px;
    margin-left:-21px;
    .btn{
      width:290px;
      font-size: 26px;
      font-weight: normal;
    }
  }
  .apptime-popup-title{
    margin:30px 80px 0 30px;
  }
  .btn-con-center .btn-confirm{
    width: 512px;
  }
  .appoint-time-con{
    .label{
      margin-right: 0!important;
      width:260px!important;
    }
    .value-con{
      display: block!important;
      justify-content:end;
      .value{
        text-align: right;
      }
      .time{
        justify-content: end;
      }
    }
    .icon-arrow-icon{
      transform: translate(0,0)!important;
      margin-left: 30px!important;
    }
  }
}
</style>
