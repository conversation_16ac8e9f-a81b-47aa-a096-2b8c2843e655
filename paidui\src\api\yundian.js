import { request } from '@/utils/request.js'
import transitEncrypt from "@/utils/transit-encrypt.js"
const key = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDTy220ZMFhFZPtAbyQKGaIYzv4S0jWjMn5/xuK2dczarHfEVaWJhtM+FQENcwMApevVwVaUjHb+JloNNBH1uYqDudKl/da/jgGwKnEuX/5YhlDfZ7lu4narQTSvCSb7pdXKksgJL+I0nNLI2VjxOkOC5RQvsr2UN3vWSwhuZ/eOQIDAQAB'
let ORIGIN , BASEURL
// #ifdef MP-CMCC-LOCAL
ORIGIN = 'https://grey.touch.10086.cn'
// #else
ORIGIN = 'https://touch.10086.cn'
// #endif
// #ifdef MP-CMCC-LOCAL
BASEURL ='https://grey.touch.10086.cn/yundian/netShop'
// #else
BASEURL ='https://touch.10086.cn/yundian/netShop'
// #endif
async function _request(config) {
  const aeskey = transitEncrypt.getPaiduiKey()
  let _data = config.data
  config.header = {
    Origin: ORIGIN,
    ...config.header||{}
  }
  if( config.method === 'POST' &&  _data && config.needEncrypt === 1) {
    config.header.leaf = await transitEncrypt.encryptRsa(aeskey,key)
    config.data = {encrypt:transitEncrypt.encryptAes(JSON.stringify(_data), aeskey)}
  }
  const res = await request(config)
  if(res?.data && config.needEncrypt === 1 ) {
    res.data = JSON.parse(transitEncrypt.decryptAes(res.data, aeskey))
  }
  return res
}
/**
 * 新编码标识换取店铺旧编码标识
 * @param {object} sendData
 * @param {string} sendData.channelCode 19位渠道编码数组['32454','34532']
 */
export function queryChannelCode(channelCode) {
  return _request({
    url: `${BASEURL}/channelCode/query`,
    method: 'POST',
    data: { channelCode }
  })
}
/**
 * 获取店铺英文数据
 * @param {object} sendData
 * @param {string} sendData.unifiedChannelIdList 19位渠道编码数组['32454','34532']
 * @param {string} sendData.stationCodeList 店铺id数组
 */
export function queryShopInfo({unifiedChannelIdList,stationCodeList}) {
  let data = {}
  if(unifiedChannelIdList){
    data.unifiedChannelIdList = unifiedChannelIdList
  }
  if(stationCodeList){
    data.stationCodeList = stationCodeList
  }
  console.log("sendData",data)
  return _request({
    url: `${BASEURL}/app/shopInfo/query`,
    method: 'POST',
    data: data,
    needEncrypt:1
  })
}

export default {
  queryChannelCode,
  queryShopInfo
}
