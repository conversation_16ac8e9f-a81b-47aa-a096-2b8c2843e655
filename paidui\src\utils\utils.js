/**
 * 优化 try-catch 的错误处理
 * @param {Function} asyncFun 异步函数
 * @param {Object} params
 * @returns [err, res] 返回被捕获异常和成功的结果
 * demo:
    async loginOut(){
        let [err ,res] = await capturedAsync(LoginOut,{mobileLogin:true})
        if(err) return
    }
     export function LoginOut(params){
        return request.get('/a/logout',{params:params})
      }
 */
export const capturedAsync = async (asyncFun, params) => {
  try {
    const res = await asyncFun(params)
    return [null, res]
  } catch (err) {
    return [err, null]
  }
}

/**
 * 是否是空json对象
 * @param obj
 * @returns {boolean}
 */
export function isEmptyObject(obj) {
  return !obj || Object.keys(obj).length === 0
}

/**
 * 判断是否为object对象，排除null
 * @param  {obj}  value 判断的对像
 * @return {Boolean} true/false
 *
 */
function isObject(obj) {
  const type = typeof obj
  return type === "function" || (type === "object" && !!obj)
}


/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== "object") {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result
  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp
    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }
    return result
  }
}

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 * index:parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}"
  let date
  if (typeof time === "object") {
    date = time
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value]
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value
    }
    return value || 0
  })
  return time_str
}

const _base64_keystr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="

/**
 * polyfill for btoa
 * @param {any} input
 * @returns {string}
 */

export function btoa(input) {
  let output = ""

  let chr1, chr2, chr3, enc1, enc2, enc3, enc4

  let i = 0

  input = _utf8_encode(input)

  while (i < input.length) {
    chr1 = input.charCodeAt(i++)

    chr2 = input.charCodeAt(i++)

    chr3 = input.charCodeAt(i++)

    enc1 = chr1 >> 2

    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)

    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)

    enc4 = chr3 & 63

    if (isNaN(chr2)) {
      enc3 = enc4 = 64
    } else if (isNaN(chr3)) {
      enc4 = 64
    }

    output =
      output +
      _base64_keystr.charAt(enc1) +
      _base64_keystr.charAt(enc2) +
      _base64_keystr.charAt(enc3) +
      _base64_keystr.charAt(enc4)
  }

  return output
}

/**
 * polyfill for atob
 * @param {string} input
 * @returns {string}
 */

export function atob(input) {
  let output = ""

  let chr1, chr2, chr3

  let enc1, enc2, enc3, enc4

  let i = 0

  input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "")

  while (i < input.length) {
    enc1 = _base64_keystr.indexOf(input.charAt(i++))

    enc2 = _base64_keystr.indexOf(input.charAt(i++))

    enc3 = _base64_keystr.indexOf(input.charAt(i++))

    enc4 = _base64_keystr.indexOf(input.charAt(i++))

    chr1 = (enc1 << 2) | (enc2 >> 4)

    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2)

    chr3 = ((enc3 & 3) << 6) | enc4

    output = output + String.fromCharCode(chr1)

    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2)
    }

    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3)
    }
  }

  output = _utf8_decode(output)
  return output
}

/**
 * UTF-8 encoding
 * @param {String} string
 * @returns
 */
export function utf8Encode(string) {
  string = string.replace(/\r\n/g, "\n")

  let utftext = ""

  for (let n = 0; n < string.length; n++) {
    let c = string.charCodeAt(n)

    if (c < 128) {
      utftext += String.fromCharCode(c)
    } else if (c > 127 && c < 2048) {
      utftext += String.fromCharCode((c >> 6) | 192)

      utftext += String.fromCharCode((c & 63) | 128)
    } else {
      utftext += String.fromCharCode((c >> 12) | 224)

      utftext += String.fromCharCode(((c >> 6) & 63) | 128)

      utftext += String.fromCharCode((c & 63) | 128)
    }
  }

  return utftext
}

/**
 * @description UTF-8 decoding
 * @param {String} utftext
 * @returns {String}
*/
export function utf8Decode(utftext) {
  let string = ""

  let i = 0

  let c = 0

  const c1 = 0

  let c2 = 0

  let c3 = 0

  while (i < utftext.length) {
    c = utftext.charCodeAt(i)

    if (c < 128) {
      string += String.fromCharCode(c)

      i++
    } else if (c > 191 && c < 224) {
      c2 = utftext.charCodeAt(i + 1)

      string += String.fromCharCode(((c & 31) << 6) | (c2 & 63))

      i += 2
    } else {
      c2 = utftext.charCodeAt(i + 1)

      c3 = utftext.charCodeAt(i + 2)

      string += String.fromCharCode(
        ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
      )

      i += 3
    }
  }

  return string
}
export function getUUID () {
  let i,
    uuid,
    s = [],
    hexDigits = '0123456789abcdef';
  for (i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(16 * Math.random()), 1);
    s[14] = '4';
    s[19] = hexDigits.substr((3 & s[19]) | 8, 1);
    s[8] = s[13] = s[18] = s[23] = '';// 正常情况是将-赋值进去的
    uuid = s.join('');
  }
  return uuid;
}
