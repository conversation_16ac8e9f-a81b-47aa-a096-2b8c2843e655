"use strict";
const common_vendor = require("../common/vendor.js");
const rsaKey = `MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI6lqRhSuXe7n2z4he+fMakiR94dJfoygPOn2uOyCL252AkV+2PSoXOYsuA/cMjQ7rboW6aWwrHoVOLme0rfw08CAwEAAQ==`;
let __appkey;
function encryptRsa(text, key) {
  return new Promise((resolve, reject) => {
    my.rsa({
      action: "encrypt",
      text,
      // 设置公钥，需替换你自己的公钥
      key: key || rsaKey,
      success: (result) => {
        resolve(result.text);
      },
      fail(error) {
        reject(error);
      }
    });
  });
}
function encryptAes(word, key = getAppKey()) {
  const keyHex = common_vendor.CryptoJS.enc.Utf8.parse(key);
  let srcs = "";
  if (typeof word == "string") {
    srcs = common_vendor.CryptoJS.enc.Utf8.parse(word);
  } else if (typeof word == "object") {
    srcs = common_vendor.CryptoJS.enc.Utf8.parse(JSON.stringify(word));
  }
  return common_vendor.CryptoJS.AES.encrypt(srcs, keyHex, {
    // iv: iv,
    mode: common_vendor.CryptoJS.mode.ECB,
    padding: common_vendor.CryptoJS.pad.Pkcs7
  }).toString();
}
function decryptAes(word, key = getAppKey()) {
  const keyHex = common_vendor.CryptoJS.enc.Utf8.parse(key);
  let decrypted = common_vendor.CryptoJS.AES.decrypt(word, keyHex, {
    // iv: iv,
    mode: common_vendor.CryptoJS.mode.ECB,
    padding: common_vendor.CryptoJS.pad.Pkcs7
  });
  return decrypted.toString(common_vendor.CryptoJS.enc.Utf8);
}
function randomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
function randomNumArray(min, max, bitNumber) {
  let result = [];
  for (let i = 0; i < bitNumber; i++) {
    result.push(randomNumber(min, max));
  }
  return result;
}
function getRandomAesKey() {
  return randomNumArray(0, 15, 16).map((item) => {
    return item.toString(16);
  }).join("");
}
function getPaiduiKey() {
  return getRandomAesKey();
}
function getAppKey() {
  if (!__appkey) {
    __appkey = my.getAppIdSync().appId.toString();
  }
  return __appkey;
}
const transitEncrypt = {
  encryptRsa,
  encryptAes,
  decryptAes,
  getRandomAesKey,
  getPaiduiKey
};
exports.decryptAes = decryptAes;
exports.encryptAes = encryptAes;
exports.transitEncrypt = transitEncrypt;
