.card{
  border-top-left-radius:18px;
  border-top-right-radius:18px;
  background: #fff;
  position: relative;
  .title{
    height: 72px;
    background: #F5F7FB;
    display: flex;
    align-items: center;
    padding:0 10px 0 30px;
    border-radius: 18px 18px 0px 0px;
    .title-name{
      font-size: 32px;
      color: #3D3D3D;
      flex:1;
    }
    .title-btn,.icon-arrow1{
      font-size: 24px;
      color: #007EFF;
    }
  }
  .content{
    padding:0 30px 30px;
    &::before{
      content: "";
      display: table;
    }
  }

}
.card-footer{
  width:690px;
  height:13px;
  background: url("@/static/home/<USER>") no-repeat 0 0;
  background-size: cover;
  position: absolute;
  bottom: -13px;
  left:0
}
.tips-title{
  font-size: 28px;
  color: #000000;
  line-height: 40px;
  margin-bottom: 12px;
  font-weight: bold;
}
.tips-content{
  font-size: 26px;
  color: rgba(0,0,0,0.6);
  line-height: 38px;
}
.btn-con{
  display: flex;
  justify-content: space-between;
  padding:0 39px;
  .btn{
    width: 259px;
    height: 80px;
    color: #999;
    text-align: center;
    line-height: 80px;
    font-size: 32px;
    border-radius: 90px;
    font-weight: bold;
    &-cancle{
      color: #007EFF;
      border: 2px solid #007EFF;
    }
    &-confirm{
      color: #fff;
      background: linear-gradient( 262deg, #07B7FF 0%, #0380FF 100%);
    }
    &.disabled{
      opacity:0.5;
      cursor: not-allowed;
    }
  }
}
.btn-con-center{
  justify-content: center;
  .btn-confirm{
    width: 363px;
  }
}
.content-more{
  margin:60px auto 54px;
  height:2px;
  border-top: 2px dashed #CDCDCD;
  position: relative;
  .content-more-con{
    width:149px;
    height:47px;
    position:absolute;
    left:50%;
    top:0;
    transform: translate(-50%,-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    font-size: 24px;
  }
}
.icon-arrow-blue{
  width:40px;
  height:40px;
  display: block;
  background: url("@/static/home/<USER>") center no-repeat;
  background-size: contain;
}
.icon-arrow-blue-little{
  width:30px;
  height:30px;
  display: block;
  background: url("@/static/home/<USER>") center no-repeat;
  background-size: contain;
}
.care-con{
  .tips-title,.tips-content{
    font-size: 32px!important;
    font-weight: bold!important;
    line-height: 48px;
    &.font30{
      font-size: 30px!important;
    }
  }
  .title-btn{
    transform: translate(0,1px)!important;
  }
}
