// 在 utils/global-data.js 文件中
export function setupPageStackListener() {
  let pagesCount = 0
  let isBack = false
  const updatePagesCount = (isPageShow,type) => {
    const $pages = getCurrentPages()
    if (isPageShow) {
      pagesCount = $pages.length
    }
    let isBack = pagesCount>$pages.length ? true : false
    // console.log(`Current pages count: ${$pages.length}`)
    // console.log(`Page count before update: ${pagesCount}---${type}`)
    let storageIsBack = uni.getStorageSync('isBack')
    if(storageIsBack !== isBack){ 
      uni.setStorageSync('isBack',isBack)
    }
    console.log(`isBack: ${isBack}`)
  }

  // 定义全局的页面栈监听mixin
  const pageStackListenerMixin = {
    onShow() {
      updatePagesCount(false,'show')
    },
    onLoad() {
      updatePagesCount(true,'onLoad')
    }
  }

  // 返回全局变量和mixin
  return {
    pagesCount,
    pageStackListenerMixin
  };
}

