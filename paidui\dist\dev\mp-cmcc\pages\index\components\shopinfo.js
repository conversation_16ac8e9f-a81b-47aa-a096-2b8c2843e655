"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_options = require("../../../store/options.js");
const Servicepopup = () => "../../../components/servicepopup.js";
const NaviOpenApp = () => "../../../components/navi-open-app.js";
const _sfc_main = {
  props: {
    hallDetail: {
      type: Object,
      default: () => ({})
    },
    showTag: {
      type: Boolean,
      default: false
    },
    showTips: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Servicepopup,
    NaviOpenApp
  },
  computed: {
    ...common_vendor.mapState(store_options.useOptionsStore, ["verType"]),
    careClass: function() {
      if (this.verType == "care") {
        return "care-con";
      }
      if (this.verType == "en") {
        return "en-con";
      }
      return "";
    }
  },
  data() {
    return {};
  },
  methods: {
    goStoreList() {
      this.$emit("gdpTrack", "clk", "切换营业厅");
      this.$emit("goStoreList");
    },
    showServiceModel() {
      this.$refs.serPopup.showServiceModel();
    },
    goToMap(item) {
      this.$emit("gdpTrack", "clk", "地图导航");
      if (this.verType == "en") {
        this.$refs.naviOpenApp.itemMarkertap(item);
        return false;
      }
      let url = `/pages/map/index?stationCode=${item.stationCode}&latitude=${item.latitude}&longitude=${item.longitude}`;
      common_vendor.index.navigateTo({ url });
    }
  }
};
if (!Array) {
  const _component_Servicepopup = common_vendor.resolveComponent("Servicepopup");
  const _component_NaviOpenApp = common_vendor.resolveComponent("NaviOpenApp");
  (_component_Servicepopup + _component_NaviOpenApp)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.showTag
  }, $props.showTag ? {
    b: common_vendor.t(_ctx.$t("nearShop"))
  } : {}, {
    c: common_vendor.t($props.hallDetail.stationName),
    d: $options.careClass !== "en-con"
  }, $options.careClass !== "en-con" ? {
    e: common_vendor.t(_ctx.$t("toogle")),
    f: common_vendor.o((...args) => $options.goStoreList && $options.goStoreList(...args)),
    g: common_vendor.o((...args) => $options.goStoreList && $options.goStoreList(...args))
  } : {}, {
    h: common_vendor.t($props.hallDetail.stationAddress),
    i: $options.careClass === "care-con"
  }, $options.careClass === "care-con" ? {
    j: common_vendor.t($props.hallDetail.distance)
  } : {}, {
    k: common_vendor.o(($event) => $options.goToMap($props.hallDetail)),
    l: common_vendor.t(_ctx.$t("workPeriod")),
    m: common_vendor.t($props.hallDetail.workPeriod),
    n: common_vendor.t($props.hallDetail.isBusiness ? _ctx.$t("working") : _ctx.$t("huisinessClose")),
    o: common_vendor.t(_ctx.$t("serviceInfo")),
    p: common_vendor.f($props.hallDetail.serviceInfoShort, (service, k0, i0) => {
      return {
        a: common_vendor.t(service.serviceName),
        b: service.serviceCode
      };
    }),
    q: common_vendor.o((...args) => $options.showServiceModel && $options.showServiceModel(...args)),
    r: $props.hallDetail.newServiceInfo && $props.hallDetail.newServiceInfo.length > 4,
    s: common_vendor.o((...args) => $options.showServiceModel && $options.showServiceModel(...args)),
    t: $props.showTips
  }, $props.showTips ? common_vendor.e({
    v: $options.careClass !== "en-con"
  }, $options.careClass !== "en-con" ? {} : {}, {
    w: common_vendor.t(_ctx.$t("tipsDes"))
  }) : {}, {
    x: common_vendor.p({
      ["service-info"]: $props.hallDetail.newServiceInfo
    }),
    y: common_vendor.n($options.careClass)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c6dd0a0d"]]);
my.createComponent(Component);
