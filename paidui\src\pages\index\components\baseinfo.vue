<template>
  <view class="content" :class="[careClass]">
    <view v-for="(item,key) in currentContent" class="content-item">
      <view class="label-con">
        <view class="left-box"></view>
        <view class="label">
          {{ key}}
        </view>
      </view>
      <view class="value">
        {{ item}}
      </view>
    </view>
  </view>
  <view class="content-more" :class="[careClass]">
    <view class="content-more-con" @click="tabDetail">
      <view class="content-more-text">
        {{ $t('waitDetail')}} &nbsp;
      </view>
      <view class="iconfont icon-cc-arrow-down-circle" :class="{'arrow-up':activeDetail}"></view>
    </view>
  </view>
  <view v-if="activeDetail" class="wait-detail" :class="[careClass]">
    <view class="wait-detail-title">
      <view class="left-box"></view>
      <view class="wait-detail-title-label">
        {{ $t('waitNum') }}
      </view>
      <view class="iconfont icon-refresh" :class="{'refresh-again':isRotate}" @click="replay"></view>
    </view>
    <view v-for="item in queueInfo" class="wait-detail-item">
      <view class="label">{{item.queueName}}</view>
      <view class="value">{{item.queueNum||0}}</view>
    </view>
  </view>
</template>
<script>
import paiduiApi from '@/api/paidui.js'
import { useUserStore } from '@/store/user'
import { useOptionsStore } from '@/store/options.js'
import { mapState } from 'pinia'
import { QUEUEINFO } from "@/const/index.js"
const user = useUserStore()
export default {
  props:{
    basetyle:{
      type:String,
      default:""
    },
    hallDetail:{
      type:Object,
      default:()=>({})
    }
  },
  data(){
    return {
      appointmentContent:[
        {
          label:'appointNumber',
          value:"number",
        },
        {
          label:'stationCode',
          value:"stationName",
        },
        {
          label:'stationAddress',
          value:"stationAddress",
        },
        {
          label:'appointTime',
          value:"appHandleDate",
        }
      ],
      pickupnumberContent:[
        {
          label:'takeTime',
          value:"ticketTime",
        },
        {
          label:'stationCode',
          value:"stationName",
        },
        {
          label:'stationAddress',
          value:"stationAddress",
        }
      ],
      isRotate:false,
      activeDetail:false,
      queueInfo:{}
    }
  },
  computed:{
    currentContent:function(){
      if(this.basetyle=='appointment'){
        let newObj = {}
        this.appointmentContent.forEach(item=>{
          let label = this.$t(item.label)
          newObj[label] = this.hallDetail[item.value]

        })
        return newObj
      }
      if(this.basetyle=='picknumber'){
        let newObj = {}
        this.pickupnumberContent.forEach(item=>{
          let label = this.$t(item.label)
          newObj[label] = this.hallDetail[item.value]
        })
        return newObj
      }
      return []
    },
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='care'){
        return 'care-con'
      }
      if(this.verType=='en'){
        return 'en-con'
      }
      return  ''
    },
  },
  mounted(){
    this.replay()
  },
  methods:{
    tabDetail(){
      this.activeDetail = !this.activeDetail
      if(this.activeDetail){
        this.replay()
      }
      // if(!this.activeDetail) return;
      // const result = await this.getQueueArr()
      // console.log(result,'result----')
      // if (result) {
      //   console.log(true)
      // }
    },
    async replay () {
      const result = await this.getQueueArr()
      if (result) {
        this.$emit('replay')
      }
    },
    async getQueueArr () {
      if(this.isRotate) return false;
      const params = {stationCode: this.hallDetail.stationCode, mobile:user.info.msisdn}
      this.starRotate()
      return  paiduiApi.queryHallQueueInfo(params)
        .then(res => {
          if(res.bizCode == '0000'){
            this.stopRotate()
            this.queueInfo = res.resultData.queueInfo;
            let setNum = 0
            this.queueInfo.forEach((item)=>{
              setNum+= parseInt(item.queueNum)
              if(this.verType=='en'){
                if(QUEUEINFO[item.queueName]){
                  item.queueName = QUEUEINFO[item.queueName]
                }

              }
            })
            this.$emit('getWaitCount',{isNew:true,waitCount:setNum})
            return true
          }else{
            this.stopRotate()
          }
        })
        .catch(e => {
          this.stopRotate()
        });
    },
    starRotate() {
      this.isRotate = true;
      // 菊花出现后15秒后自动消失
      const timer = setTimeout(() => {
        if (timer) {
          clearTimeout(timer);
        }
        this.isRotate = false;
      }, 20000);

    },
    stopRotate() {
      const timer = setTimeout(() => {
        if (timer) {
          clearTimeout(timer);
        }
        this.isRotate = false;
      }, 1000);
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/home/<USER>";
@keyframes refresh{
  0% {
    transform:rotate(0deg);
  }
  50% {
    transform:rotate(180deg);
  }
  100% {
    transform:rotate(360deg);
  }
}
.refresh-again{
  animation: refresh 1s linear 1;
}
.care-con{
  margin-top:53px;
  .content-item{
    margin-top: 36px;
    .left-box{
      margin-top:6px;
    }
    .label,.value{
      font-size: 36px;
      font-weight: bold;
      line-height: 0.42rem;
    }
  }
  .content-more-con{
    width:222px;
    height:47px;
    font-size: 32px;
    line-height: 40px;
    font-weight: bold;
    .icon-cc-arrow-down-circle{
      font-size: 32px;
    }
  }
  .wait-detail-title-label{
    font-size:36px!important;
  }
  .icon-refresh{
    font-size: 40px;
  }
  &.wait-detail{
    margin-top:68px!important;
    margin-bottom:36px!important;
    .wait-detail-item{
      margin-top:36px;
      .label,.value{
        font-size:36px!important;
      }
      .label{
        font-weight: bold;
      }
    }
  }
}
.en-con{
  .label{
    width:250px;
  }
  .content-more-con{
    width:220px;
    .content-more-text{
      width:170px;
      margin-right:0;
    }
  }
}
</style>
