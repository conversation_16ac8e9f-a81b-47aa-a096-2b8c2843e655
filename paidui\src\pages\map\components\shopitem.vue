<template>
  <view class="shop-item" v-if="item && item.stationCode" :class="[careClass]">
    <view class="shop-info" >
      <view class="shop-info-con">
        <view class="shop-title">
          <span class="span span-name">{{ item.stationName }}</span>
        </view>
        <view class="shop-busi-hour">
          <span class="span">{{ $t('workPeriod') }}：{{ item.workPeriod }}</span>
        </view>
      </view>
      <view class="icon-daohang-icon" @click.stop="goNavi(item)"></view>
    </view>
    <view class="shop-address">
      <view class="icon-address-icon"></view>
      <view class="span-address">
        {{ item.stationAddress }}
      </view>
    </view>
    <view class="shop-btn">
      <view class="btn-item btn-yuyue" :class="{'disabled':item.isAppointment==0}" @click.stop="goPickupnumber(item,item.isAppointment==0)">
        {{ $t('apptake') }}
      </view>
    </view>
  </view>
  <view class="hr"></view>
</template>
<script>
import Servicepopup from '@/components/servicepopup.vue'
import { useUserStore } from '@/store/user'
import { mapState } from 'pinia'
import { useOptionsStore } from '@/store/options.js'
export default {
  data() {
    return {
    }
  },
  props: {
    item: {
      type: Object,
      default() { return null }
    },
    isMap:{//map页面的店铺列表不展示一些功能
      type:Boolean,
      default:false
    },
    maxNumLength:{
      type:Number,
      default:3
    }
  },
  components:{
    Servicepopup
  },
  computed: {
    ...mapState(useUserStore, ['geo']),
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='en'){
        return 'en-con'
      }
      if(this.verType=='care'){
        return 'care-con'
      }
      return ''

    }
  },
  methods:{
    goPickupnumber(item,isDisabled){
      uni.navigateTo({ url: `/pages/index/index?stationCode=${item.stationCode}&provinceCode=${item.provinceCode}&cityCode=${item.cityCode}` })
    },
    showServiceModel(item){
      if(item.newServiceInfo && item.newServiceInfo.length>4){
        this.$refs.serPopup.showServiceModel()
      }else{
        this.goToShop(item)
      }
    },
    goToMap(item){
      uni.navigateTo({ url: `/pages/map/index?stationCode=${item.stationCode}&latitude=${item.latitude}&longitude=${item.longitude}` })
    },
    goNavi(item){
      this.$emit('goNavi',item)
    }
  }
}
</script>
<style lang="scss" scoped>
.hr{
  height: 0;
  border-bottom: 1px solid #CCCCCC;
  margin:0 30px;
}
.shop-item{
  box-sizing: border-box;
  padding:30px 30px 36px;
  .shop-info{
    display: flex;
    .shop-info-con{
      flex:1;
    }
    .shop-title{
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      .span-name{
        font-size: 36px;
        color: #000000;
        line-height: 40px;
      }
    }
    .icon-daohang-icon{
      width:72px;
      height:72px;
      background: url("@/static/home/<USER>") center no-repeat;
      background-size: contain;
    }
  }
  .shop-address,.shop-busi-hour{
    font-size: 36px;
  }
  .shop-address{
    display: flex;
    align-items: flex-start;
    color: #0256FF;
    margin-top:30px;
    line-height: 54px;
    .span-address{
      flex:1;
    }
    .icon-address-icon{
      width:25px;
      height:33px;
      background: url("@/static/home/<USER>") center no-repeat;
      background-size: contain;
      margin-right: 8px;
      margin-top:9px;
    }
  }
  .shop-busi-hour{
    display: flex;
    align-items: center;
    line-height: 50px;
    margin-top:6px;
    color: #000;
  }
  .shop-btn{
    display: flex;
    align-items: center;
    margin-top: 24px;
    justify-content: right;
    flex-direction: row-reverse;
    .btn-item-label{
      font-size: 36px;
      color: #3D3D3D;
      flex:1;
      font-weight:bold;
      display: flex;
      .btn-item-label-item:nth-child(2){
        color: #007EFF;
      }
    }
    .btn-item{
      font-weight: 400;
      font-size: 36px;
      width:198px;
      box-sizing: border-box;
      border-radius: 90px 90px 90px 90px;
      height:62px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.btn-quhao{
        border: 2px solid #007EFF;
        color:#007EFF;
      }
      &.btn-yuyue{
        background: linear-gradient( 262deg, #07B7FF 0%, #0380FF 100%);
        color:#fff;
        margin-left: 19px;
      }
      &.disabled{
        opacity:0.5;
        cursor: not-allowed;
      }

    }
  }
}
.en-con{
  .span-name,.shop-address,.shop-busi-hour{
    font-size: 28px!important;
    line-height: 36px!important;
  }
  .shop-busi-hour,.shop-btn{
    margin-top:30px!important;
  }
  .shop-btn .btn-item{
    width:100%!important;
    margin-left:0px!important;
    font-size: 28px!important;
  }
  .icon-address-icon{
    width:18px!important;
    height:24px!important;
    background: url("@/static/home/<USER>") center no-repeat;
    background-size: contain;
    margin-right: 8px;
    margin-top:9px;
  }
}
</style>
