"use strict";
const common_vendor = require("../common/vendor.js");
const store_user = require("../store/user.js");
const store_options = require("../store/options.js");
const sdk_cmcc = require("../sdk/cmcc.js");
const Navi = () => "./navi.js";
const _sfc_main = {
  data() {
    return {
      naviTarget: null,
      mapListEn: [
        {
          text: "Amap",
          name: "amap",
          textName: "高德导航"
        },
        {
          text: "Baidu map",
          name: "baidu",
          textName: "百度导航"
        },
        {
          text: "Tencent Maps",
          name: "tencent",
          textName: "腾讯导航"
        },
        {
          text: "Apple Maps",
          name: "apple",
          textName: "苹果导航"
        }
      ]
    };
  },
  components: {
    Navi
  },
  computed: {
    ...common_vendor.mapState(store_user.useUserStore, ["geo"]),
    ...common_vendor.mapState(store_options.useOptionsStore, ["verType"]),
    careClass: function() {
      if (this.verType == "en") {
        return "en-con";
      }
      if (this.verType == "care") {
        return "care-con";
      }
      return "";
    }
  },
  methods: {
    itemMarkertap(item) {
      this.naviTarget = item;
      this.$refs.naviSelector.open();
    },
    goToNaviApp({ item }) {
      sdk_cmcc.cmcc.openNaviApp({
        app: item.name,
        address: this.naviTarget.stationAddress,
        latitude: this.naviTarget.latitude,
        longitude: this.naviTarget.longitude,
        name: this.naviTarget.stationName
      }).catch((e) => {
        common_vendor.index.showToast({
          title: this.$t("mapErrorTip"),
          duration: 2e3
        });
      });
    },
    close() {
      this.$refs.naviSelector.close();
    }
  }
};
if (!Array) {
  const _component_Navi = common_vendor.resolveComponent("Navi");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_Navi + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../node-modules/npm-scope-dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.goToNaviApp),
    b: common_vendor.o($options.close),
    c: common_vendor.p({
      ["map-list"]: $data.mapListEn
    }),
    d: common_vendor.p({
      type: "bottom",
      safeArea: true,
      backgroundColor: "#fff"
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
my.createComponent(Component);
