/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.shop-item.data-v-31f40c6f {
  width: 690rpx;
  background: linear-gradient(180deg, #E3F1FF 0%, #FFFFFF 40%);
  box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.1);
  border-radius: 18rpx;
  margin: 0 auto 24rpx;
  box-sizing: border-box;
  padding: 24rpx;
}
.shop-item .shop-info.data-v-31f40c6f {
  display: flex;
}
.shop-item .shop-info .img-con.data-v-31f40c6f {
  width: 160rpx;
  height: 160rpx;
  padding: 17rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 18rpx;
  border: 2rpx solid #F5F5F5;
}
.shop-item .shop-info .img-con image.data-v-31f40c6f {
  width: 126rpx;
  height: 126rpx;
}
.shop-item .shop-info .shop-info-con.data-v-31f40c6f {
  margin-left: 24rpx;
  width: 470rpx;
  margin-top: 3rpx;
}
.shop-item .shop-info .shop-info-con .shop-title.data-v-31f40c6f {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.shop-item .shop-info .shop-info-con .shop-title .span-name.data-v-31f40c6f {
  font-size: 36rpx;
  font-weight: bold;
  color: #000000;
  line-height: 40rpx;
}
.shop-item .shop-info .shop-info-con .shop-title .span-distance.data-v-31f40c6f {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.6);
}
.shop-item .shop-address.data-v-31f40c6f, .shop-item .shop-busi-hour.data-v-31f40c6f {
  font-size: 36rpx;
  color: rgba(0, 0, 0, 0.6);
  line-height: 50rpx;
  margin-top: 20rpx;
  font-weight: bold;
}
.shop-item .shop-busi-hour.data-v-31f40c6f {
  display: flex;
  align-items: center;
  height: 40rpx;
  margin-top: 40rpx;
}
.shop-item .shop-busi-hour .span.data-v-31f40c6f:last-child {
  height: 36rpx;
  border-radius: 6rpx 6rpx 6rpx 6rpx;
  border: 1rpx solid #007EFF;
  padding: 0 14rpx;
  font-size: 28rpx;
  color: #007EFF;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}
.shop-item .shop-busi-scope.data-v-31f40c6f {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
}
.shop-item .shop-busi-scope .label.data-v-31f40c6f {
  font-size: 36rpx;
  color: rgba(0, 0, 0, 0.6);
  font-weight: bold;
}
.shop-item .shop-busi-scope .scope-list.data-v-31f40c6f {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.shop-item .shop-busi-scope .scope-list .scope-item.data-v-31f40c6f {
  border-radius: 12rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  font-size: 28rpx;
  line-height: 34rpx;
  color: rgba(0, 0, 0, 0.6);
  padding: 6rpx 12rpx;
  margin-left: 12rpx;
  font-weight: bold;
}
.shop-item .shop-busi-scope .icon-arrow1.data-v-31f40c6f {
  font-size: 30rpx;
}
.shop-item .shop-btn.data-v-31f40c6f {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
}
.shop-item .shop-btn .btn-item-label.data-v-31f40c6f {
  font-size: 36rpx;
  color: #3D3D3D;
  flex: 1;
  font-weight: bold;
  display: flex;
}
.shop-item .shop-btn .btn-item-label .btn-item-label-item.data-v-31f40c6f:nth-child(2) {
  color: #007EFF;
}
.shop-item .shop-btn .btn-item.data-v-31f40c6f {
  font-weight: 400;
  font-size: 36rpx;
  width: 198rpx;
  box-sizing: border-box;
  border-radius: 90rpx 90rpx 90rpx 90rpx;
  height: 62rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.shop-item .shop-btn .btn-item.btn-quhao.data-v-31f40c6f {
  border: 2rpx solid #007EFF;
  color: #007EFF;
}
.shop-item .shop-btn .btn-item.btn-yuyue.data-v-31f40c6f {
  background: linear-gradient(262deg, #07B7FF 0%, #0380FF 100%);
  color: #fff;
  margin-left: 19rpx;
}
.shop-item .shop-btn .btn-item.disabled.data-v-31f40c6f {
  opacity: 0.5;
  cursor: not-allowed;
}