"use strict";
const utils_request = require("../utils/request.js");
const utils_transitEncrypt = require("../utils/transit-encrypt.js");
const key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDTy220ZMFhFZPtAbyQKGaIYzv4S0jWjMn5/xuK2dczarHfEVaWJhtM+FQENcwMApevVwVaUjHb+JloNNBH1uYqDudKl/da/jgGwKnEuX/5YhlDfZ7lu4narQTSvCSb7pdXKksgJL+I0nNLI2VjxOkOC5RQvsr2UN3vWSwhuZ/eOQIDAQAB";
let ORIGIN, BASEURL;
ORIGIN = "https://grey.touch.10086.cn";
BASEURL = "https://grey.touch.10086.cn/yundian/netShop";
async function _request(config) {
  const aeskey = utils_transitEncrypt.transitEncrypt.getPaiduiKey();
  let _data = config.data;
  config.header = {
    Origin: ORIGIN,
    ...config.header || {}
  };
  if (config.method === "POST" && _data && config.needEncrypt === 1) {
    config.header.leaf = await utils_transitEncrypt.transitEncrypt.encryptRsa(aeskey, key);
    config.data = { encrypt: utils_transitEncrypt.transitEncrypt.encryptAes(JSON.stringify(_data), aeskey) };
  }
  const res = await utils_request.request(config);
  if ((res == null ? void 0 : res.data) && config.needEncrypt === 1) {
    res.data = JSON.parse(utils_transitEncrypt.transitEncrypt.decryptAes(res.data, aeskey));
  }
  return res;
}
function queryChannelCode(channelCode) {
  return _request({
    url: `${BASEURL}/channelCode/query`,
    method: "POST",
    data: { channelCode }
  });
}
function queryShopInfo({ unifiedChannelIdList, stationCodeList }) {
  let data = {};
  if (unifiedChannelIdList) {
    data.unifiedChannelIdList = unifiedChannelIdList;
  }
  if (stationCodeList) {
    data.stationCodeList = stationCodeList;
  }
  console.log("sendData", data);
  return _request({
    url: `${BASEURL}/app/shopInfo/query`,
    method: "POST",
    data,
    needEncrypt: 1
  });
}
const yundianApi = {
  queryChannelCode,
  queryShopInfo
};
exports.yundianApi = yundianApi;
