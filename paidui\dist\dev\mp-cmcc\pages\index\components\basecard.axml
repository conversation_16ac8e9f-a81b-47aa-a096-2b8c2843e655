<view a:if="{{a}}" class="{{('base-card') + ' ' + 'data-v-6674fa3e' + ' ' + T}}"><view class="header data-v-6674fa3e"><view class="after data-v-6674fa3e"></view></view><view class="content data-v-6674fa3e"><block a:if="{{b}}"><view class="appoint-time-con data-v-6674fa3e" onTap="{{f}}"><view class="label data-v-6674fa3e">{{c}}</view><view class="value-con data-v-6674fa3e"><view class="value data-v-6674fa3e">{{d}}</view><view class="value time data-v-6674fa3e">{{e}}</view></view><view class="iconfont icon-arrow-icon data-v-6674fa3e"></view></view><view class="tips-title data-v-6674fa3e">{{g}}</view><view class="tips-content font30 data-v-6674fa3e">{{h}}</view><base-info class="data-v-6674fa3e" u-i="6674fa3e-0" onVI="__l" u-p="{{i}}"/><view class="btn-con btn-con-center data-v-6674fa3e"><view class="{{('btn') + ' ' + 'btn-confirm' + ' ' + 'data-v-6674fa3e' + ' ' + (k && 'disabled')}}" onTap="{{l}}">{{j}}</view></view></block><block a:if="{{m}}"><view class="pickupnumber-title data-v-6674fa3e">{{n}}</view><view class="pickupnumber-number data-v-6674fa3e">{{o}}</view><base-info class="data-v-6674fa3e" onGetWaitCount="{{p}}" u-i="6674fa3e-1" onVI="__l" u-p="{{q}}"/><view class="btn-con appoint-btn data-v-6674fa3e"><view class="{{('btn') + ' ' + 'btn-cancle' + ' ' + 'data-v-6674fa3e' + ' ' + (s && 'disabled')}}" onTap="{{t}}">{{r}}</view><view class="{{('btn') + ' ' + 'btn-confirm' + ' ' + 'data-v-6674fa3e' + ' ' + (w && 'disabled')}}" onTap="{{x}}">{{v}}</view></view></block><view class="card-footer data-v-6674fa3e"></view></view><view class="tips-con data-v-6674fa3e"><view class="tips-records data-v-6674fa3e"><view class="data-v-6674fa3e" onTap="{{z}}">{{y}}</view><view class="iconfont icon-arrow1-icon data-v-6674fa3e" onTap="{{A}}"></view></view><block a:if="{{B}}"><view class="tips-title data-v-6674fa3e">{{C}}</view><view class="tips-content data-v-6674fa3e">{{D}}</view></block></view><uni-popup u-s="{{['d']}}" ref="__r" u-r="appTimePopup" class="{{('data-v-6674fa3e') + ' ' + R}}" u-i="6674fa3e-2" onVI="__l" u-p="{{S}}"><view class="apptime-popup-title data-v-6674fa3e">{{E}}</view><picker-view value="{{J}}" onChange="{{K}}" class="my-picker data-v-6674fa3e" indicator-style="{{L}}"><picker-view-column class="data-v-6674fa3e"><view a:for="{{F}}" a:for-item="item" class="data-v-6674fa3e" style="{{G}}">{{item.a}}</view></picker-view-column><picker-view-column class="data-v-6674fa3e"><view a:for="{{H}}" a:for-item="item" class="data-v-6674fa3e" style="{{I}}">{{item.a}}</view></picker-view-column></picker-view><view class="btn-con app-btn-con data-v-6674fa3e"><view class="btn btn-cancle data-v-6674fa3e" onTap="{{N}}">{{M}}</view><view class="btn btn-confirm data-v-6674fa3e" onTap="{{P}}">{{O}}</view></view><view class="close data-v-6674fa3e" onTap="{{Q}}"></view></uni-popup></view>