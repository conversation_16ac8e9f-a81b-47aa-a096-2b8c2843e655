"use strict";const common_vendor=require("../../common/vendor.js");const store_user=require("../../store/user.js");const api_paidui=require("../../api/paidui.js");const sdk_cmcc=require("../../sdk/cmcc.js");const utils_utils=require("../../utils/utils.js");const const_index=require("../../const/index.js");const store_options=require("../../store/options.js");const utils_gioTrack=require("../../utils/gio-track.js");const api_yundian=require("../../api/yundian.js");const ShopItem=()=>"./components/shopitem.js";const ShopItemLim=()=>"../shoplist/components/shopitem.js";const Navi=()=>"../../components/navi.js";const _sfc_main={data(){return{hallList:[],latitude:"",longitude:"",naviTarget:null,markers:{},searchCriteria:"",GCJ02latitude:"",GCJ02longitude:"",careClass:null}},components:{ShopItem:ShopItem,ShopItemLim:ShopItemLim,Navi:Navi},computed:{...common_vendor.mapState(store_user.useUserStore,["geo"]),...common_vendor.mapState(store_options.useOptionsStore,["verType"]),careClass:function(){if(this.verType=="en"){return"en-con"}if(this.verType=="care"){return"care-con"}return""}},watch:{searchCriteria:{handler(val){if(val){this.onChange()}},immediate:true}},onLoad(options){const optionsStore=store_options.useOptionsStore();optionsStore.setVerType(options);this.latitude=options.latitude;this.longitude=options.longitude;let{latitude:latitude,longitude:longitude}=this.getGCJ(this.latitude,this.longitude);this.GCJ02latitude=latitude;this.GCJ02longitude=longitude;my.setNavigationBar({reset:true,title:this.$t("mpappTitle")});this.getHallList()},methods:{getGCJ(latitude,longitude){if(latitude&&longitude){let result=common_vendor.exported.transform([longitude,latitude],common_vendor.exported.BD09,common_vendor.exported.GCJ02);return{latitude:result[1],longitude:result[0]}}},async getHallList(){const user=store_user.useUserStore();let sendData={mobile:user.info.msisdn||"",latitude:this.latitude,longitude:this.longitude,currentPage:1,pageSize:this.verType=="en"?1:5};if(this.searchCriteria)sendData.searchCriteria=this.searchCriteria;api_paidui.paiduiApi.queryHallList(sendData).then((async res=>{if(res.resultData&&res.resultData.list&&res.resultData.list.length>0){this.hallList=res.resultData.list;if(this.verType=="en"){await this.queryShopInfo([this.hallList[0].stationCode])}this.markers=this.hallList.map((hall=>{let{latitude:latitude,longitude:longitude}=this.getGCJ(hall.latitude,hall.longitude);return{id:hall.stationCode,title:hall.stationName,latitude:latitude,longitude:longitude,label:{content:hall.stationName,color:"#000000"}}}));if(this.searchCriteria){this.changePositon(this.hallList[0])}}else{this.hallList=[]}}));this.multiTrack()},onChange:utils_utils.debounce((function(){this.getHallList()}),1e3),markertap(e){const hall=this.hallList.find((hall2=>hall2.stationCode===e.detail.markerId));this.naviTarget=hall;this.$refs.naviSelector.open()},itemMarkertap(item){this.naviTarget=item;this.$refs.naviSelector.open()},goToNaviApp({item:item}){sdk_cmcc.cmcc.openNaviApp({app:item.name,address:this.naviTarget.stationAddress,latitude:this.naviTarget.latitude,longitude:this.naviTarget.longitude,name:this.naviTarget.stationName}).catch((e=>{common_vendor.index.showToast({title:"APP未安装",duration:2e3})}))},close(){this.$refs.naviSelector.close()},changePositon(item){let{latitude:latitude,longitude:longitude}=this.getGCJ(item.latitude,item.longitude);this.GCJ02latitude=latitude;this.GCJ02longitude=longitude},async multiTrack(){await utils_gioTrack.gdpSetGeneralProps({WT_page_type:"排队取号-"+const_index.ENUM_VERTYPE[this.verType]});utils_gioTrack.gdpDcsMultiTrack("pageView",{WT_et:"pageview",WT_event:"MPXPageShow",WT_ti:"地图导航"})},async queryShopInfo(stationCodeList){await api_yundian.yundianApi.queryShopInfo({stationCodeList:stationCodeList}).then((res=>{if(res&&res.data&&res.data.shopEnInfoList&&res.data.shopEnInfoList.length>0){this.hallList=[res.data.shopEnInfoList[0]]}})).catch((()=>{}))}}};if(!Array){const _component_ShopItem=common_vendor.resolveComponent("ShopItem");const _component_ShopItemLim=common_vendor.resolveComponent("ShopItemLim");const _component_Navi=common_vendor.resolveComponent("Navi");const _easycom_uni_popup2=common_vendor.resolveComponent("uni-popup");(_component_ShopItem+_component_ShopItemLim+_component_Navi+_easycom_uni_popup2)()}const _easycom_uni_popup=()=>"../../node-modules/npm-scope-dcloudio/uni-ui/lib/uni-popup/uni-popup.js";if(!Math){_easycom_uni_popup()}function _sfc_render(_ctx,_cache,$props,$setup,$data,$options){return common_vendor.e({a:$data.markers.length>0},$data.markers.length>0?{b:$data.GCJ02latitude,c:$data.GCJ02longitude,d:$data.markers,e:common_vendor.o(((...args)=>$options.markertap&&$options.markertap(...args)))}:{},{f:$options.careClass!=="en-con"},$options.careClass!=="en-con"?{g:_ctx.$t("shortPlaceholder"),h:$data.searchCriteria,i:common_vendor.o(($event=>$data.searchCriteria=$event.detail.value))}:{},{j:$options.careClass=="care-con"||$options.careClass=="en-con"},$options.careClass=="care-con"||$options.careClass=="en-con"?{k:common_vendor.f($data.hallList,((item,k0,i0)=>({a:"7110ce9c-0-"+i0,b:common_vendor.p({item:item,["is-map"]:true}),c:item.stationCode,d:common_vendor.o(($event=>$options.changePositon(item)))}))),l:common_vendor.o($options.itemMarkertap)}:{m:common_vendor.f($data.hallList,((item,k0,i0)=>({a:"7110ce9c-1-"+i0,b:common_vendor.p({item:item,["is-map"]:true}),c:item.stationCode,d:common_vendor.o(($event=>$options.changePositon(item)))}))),n:common_vendor.o($options.itemMarkertap)},{o:common_vendor.n($options.careClass),p:common_vendor.o($options.goToNaviApp),q:common_vendor.o($options.close),r:common_vendor.p({type:"bottom",safeArea:true,backgroundColor:"#fff"})})}const MiniProgramPage=common_vendor._export_sfc(_sfc_main,[["render",_sfc_render],["__scopeId","data-v-7110ce9c"]]);my.createPage(MiniProgramPage);
