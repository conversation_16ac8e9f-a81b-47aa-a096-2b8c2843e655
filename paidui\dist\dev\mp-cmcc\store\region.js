"use strict";
const common_vendor = require("../common/vendor.js");
const regionTmp = common_vendor.index.getStorageSync("region") || {};
const useRegionStore = common_vendor.defineStore("region", {
  state: () => {
    return {
      info: regionTmp.list || [],
      timestamp: regionTmp.timestamp || (/* @__PURE__ */ new Date()).valueOf()
    };
  },
  actions: {
    setRegion(region) {
      this.info = region;
      common_vendor.index.setStorage({
        key: "region",
        data: {
          list: region,
          timestamp: (/* @__PURE__ */ new Date()).valueOf()
        },
        success: () => {
        }
      });
    }
  }
});
exports.useRegionStore = useRegionStore;
