/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content-item.data-v-433313ba {
  display: flex;
  margin-top: 30rpx;
  line-height: 40rpx;
}
.content-item .label-con.data-v-433313ba {
  display: flex;
}
.content-item .label-con .label.data-v-433313ba {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.6);
  min-width: 168rpx;
  margin-left: 12rpx;
}
.content-item .value.data-v-433313ba {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #000000;
  font-weight: bold;
}
.left-box.data-v-433313ba {
  width: 6rpx;
  height: 23rpx;
  background: linear-gradient(360deg, #5DADFF 0%, #007EFF 100%);
  border-radius: 90rpx 90rpx 90rpx 90rpx;
  margin-top: 7rpx;
}
.content-more.data-v-433313ba {
  margin: 53rpx auto 54rpx;
  height: 2rpx;
  border-top: 2rpx dashed #CDCDCD;
  position: relative;
}
.content-more .content-more-con.data-v-433313ba {
  width: 149rpx;
  height: 47rpx;
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  font-size: 24rpx;
}
.content-more .content-more-con .content-more-text.data-v-433313ba {
  margin-right: 6rpx;
}
.content-more .icon-cc-arrow-down-circle.data-v-433313ba {
  font-size: 20rpx;
  font-weight: bold;
}
.content-more .icon-cc-arrow-down-circle.arrow-up.data-v-433313ba {
  transform: rotate(180deg);
}
.wait-detail.data-v-433313ba {
  margin-bottom: 30rpx;
}
.wait-detail-title.data-v-433313ba {
  color: #000000;
  font-size: 28rpx;
  line-height: 40rpx;
  display: flex;
  margin-top: -12rpx;
  margin-bottom: 18rpx;
}
.wait-detail-title .wait-detail-title-label.data-v-433313ba {
  margin: 0 12rpx 0 18rpx;
  font-weight: bold;
}
.wait-detail-title .icon-refresh.data-v-433313ba {
  color: #007EFF;
  font-size: 32rpx;
}
.wait-detail-item.data-v-433313ba {
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  margin-bottom: 13rpx;
  line-height: 40rpx;
}
.wait-detail-item .label.data-v-433313ba {
  flex: 1;
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
}
.wait-detail-item .value.data-v-433313ba {
  color: #F3513B;
  font-size: 28rpx;
}
@keyframes refresh-433313ba {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(180deg);
}
100% {
    transform: rotate(360deg);
}
}
.refresh-again.data-v-433313ba {
  animation: refresh-433313ba 1s linear 1;
}
.care-con.data-v-433313ba {
  margin-top: 53rpx;
}
.care-con .content-item.data-v-433313ba {
  margin-top: 36rpx;
}
.care-con .content-item .left-box.data-v-433313ba {
  margin-top: 6rpx;
}
.care-con .content-item .label.data-v-433313ba, .care-con .content-item .value.data-v-433313ba {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 0.42rem;
}
.care-con .content-more-con.data-v-433313ba {
  width: 222rpx;
  height: 47rpx;
  font-size: 32rpx;
  line-height: 40rpx;
  font-weight: bold;
}
.care-con .content-more-con .icon-cc-arrow-down-circle.data-v-433313ba {
  font-size: 32rpx;
}
.care-con .wait-detail-title-label.data-v-433313ba {
  font-size: 36rpx !important;
}
.care-con .icon-refresh.data-v-433313ba {
  font-size: 40rpx;
}
.care-con.wait-detail.data-v-433313ba {
  margin-top: 68rpx !important;
  margin-bottom: 36rpx !important;
}
.care-con.wait-detail .wait-detail-item.data-v-433313ba {
  margin-top: 36rpx;
}
.care-con.wait-detail .wait-detail-item .label.data-v-433313ba, .care-con.wait-detail .wait-detail-item .value.data-v-433313ba {
  font-size: 36rpx !important;
}
.care-con.wait-detail .wait-detail-item .label.data-v-433313ba {
  font-weight: bold;
}
.en-con .label.data-v-433313ba {
  width: 250rpx;
}
.en-con .content-more-con.data-v-433313ba {
  width: 220rpx;
}
.en-con .content-more-con .content-more-text.data-v-433313ba {
  width: 170rpx;
  margin-right: 0;
}