<template>
  <view>
    <view :class="[careClass]">
      <map
        v-if="markers.length > 0"
        style="width: 375px;height:375px;"
        :latitude="GCJ02latitude"
        :longitude="GCJ02longitude"
        :markers="markers"
        @markertap="markertap"
      ></map>
      <view v-if="careClass!=='en-con'" class="search-con">
        <div class="search-bar" >
          <input
            v-model="searchCriteria"
            :placeholder="$t('shortPlaceholder')"
            placeholder-style="color:rgba(0,0,0,0.6)"
          />
          <span class="iconfont icon-search-icon"></span>
        </div>
      </view>
      <div class="shop-container">
        <template v-if="careClass=='care-con' || careClass=='en-con'">
          <view v-for="item in hallList" :key="item.stationCode"  @click="changePositon(item)">
            <ShopItem :item="item" :is-map="true" @goNavi="itemMarkertap" />
          </view>
        </template>
        <template v-else>
          <view v-for="item in hallList" :key="item.stationCode"  @click="changePositon(item)">
            <ShopItemLim :item="item" :is-map="true" @goNavi="itemMarkertap" />
          </view>
        </template>
      </div>
    </view>
    <uni-popup ref="naviSelector" type="bottom" safeArea backgroundColor="#fff">
      <Navi @select="goToNaviApp" @close="close"/>
    </uni-popup>
  </view>
</template>
<script>
import { useUserStore } from '@/store/user';
import paiduiApi from '@/api/paidui.js'
import cmcc from "@/sdk/cmcc.js"
import ShopItem from './components/shopitem.vue'
import ShopItemLim from '../shoplist/components/shopitem.vue'
import {debounce} from '@/utils/utils.js'
import Navi from "@/components/navi.vue"
import { ENUM_VERTYPE } from "@/const/index.js"
import gcoord from 'gcoord'
import { useOptionsStore } from '@/store/options.js'
import { mapState } from 'pinia'
import { gdpDcsMultiTrack,gdpSetGeneralProps } from "@/utils/gio-track.js"
import yundianApi from "@/api/yundian.js"

export default {
  data() {
    return {
      hallList: [],
      latitude: '',
      longitude: '',
      naviTarget: null,
      markers: {
      },
      searchCriteria:'',
      GCJ02latitude:"",
      GCJ02longitude:"",
      careClass:null
    }
  },
  components: {
    ShopItem,
    ShopItemLim,
    Navi
  },
  computed:{
    ...mapState(useUserStore, ['geo']),
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='en'){
        return 'en-con'
      }
      if(this.verType=='care'){
        return 'care-con'
      }
      return ''

    }
  },
  watch:{
    searchCriteria:{
      handler(val){
        if(val){
          this.onChange()
        }
      },
      immediate:true
    }
  },
  onLoad(options) {
    const optionsStore = useOptionsStore()
    optionsStore.setVerType(options)
    this.latitude = options.latitude
    this.longitude = options.longitude
    let {latitude,longitude} = this.getGCJ(this.latitude,this.longitude)
    this.GCJ02latitude = latitude
    this.GCJ02longitude = longitude
    my.setNavigationBar({
      reset: true,
      title: this.$t('mpappTitle'),
    });
    this.getHallList()
  },
  methods: {
    getGCJ(latitude,longitude){
      if(latitude && longitude){
      let result = gcoord.transform(
        [longitude,latitude],    // 经纬度坐标[116.403988, 39.914266]
        gcoord.BD09,               // 当前坐标系:坐标系：WGS84、BD09、GCJ02
        gcoord.GCJ02                 // 目标坐标系
      );
      return {
        latitude:result[1],
        longitude:result[0]
      }
    }
    },
    /**获取最近厅店信息 */
    async getHallList() {
      const user = useUserStore()
      let sendData = {
        "mobile": user.info.msisdn || '',
        "latitude":this.latitude,
        "longitude":this.longitude,
        "currentPage":1,
        "pageSize":this.verType=='en' ? 1 : 5
      }
      if (this.searchCriteria) sendData.searchCriteria = this.searchCriteria
      paiduiApi.queryHallList(sendData).then(async(res)=>{
        if(res.resultData && res.resultData.list && res.resultData.list.length>0){
          this.hallList = res.resultData.list
          if(this.verType=='en'){
            await this.queryShopInfo([this.hallList[0].stationCode])
          }
          this.markers = this.hallList.map(hall => {
            let {latitude,longitude} = this.getGCJ(hall.latitude,hall.longitude)
            return {
              id: hall.stationCode,
              title: hall.stationName,
              latitude,
              longitude,
              label: {
                content: hall.stationName,
                color:"#000000",
              }
            }
          })

          if(this.searchCriteria){
            this.changePositon(this.hallList[0])
          }
        }else{
          this.hallList = []
        }
      })
      this.multiTrack()
    },
     /**搜索附近店铺 */
    onChange:debounce(function(){
      this.getHallList()
    }, 1000),
    /**打开地图 */
    markertap(e) {
      const hall = this.hallList.find(hall => hall.stationCode === e.detail.markerId)
      this.naviTarget = hall
			this.$refs.naviSelector.open()
    },
    itemMarkertap(item){
      this.naviTarget = item
			this.$refs.naviSelector.open()
    },
    goToNaviApp({item}) {
      cmcc.openNaviApp({
        app: item.name,
        address: this.naviTarget.stationAddress,
        latitude : this.naviTarget.latitude,
        longitude: this.naviTarget.longitude,
        name:this.naviTarget.stationName
      }).catch(e => {
        uni.showToast({
          title:'APP未安装',
          duration:2000
        })
      })
    },
    close(){
      this.$refs.naviSelector.close()
    },
    /**改变地图位置 */
    changePositon(item) {
      let {latitude,longitude} = this.getGCJ(item.latitude,item.longitude)
      this.GCJ02latitude = latitude
      this.GCJ02longitude = longitude
    },
    /**插码 */
    async multiTrack(){
      await gdpSetGeneralProps({
        WT_page_type: '排队取号-' + ENUM_VERTYPE[this.verType],
      })
      gdpDcsMultiTrack('pageView',{
        "WT_et" : "pageview",
        "WT_event" : "MPXPageShow",
        "WT_ti" : "地图导航"
      })
    },
    /**
     * 去云店查询stationCode
     */
    async queryShopInfo(stationCodeList){
      await yundianApi.queryShopInfo({stationCodeList}).then(res=> {
        if(res && res.data && res.data.shopEnInfoList && res.data.shopEnInfoList.length>0) {
          this.hallList =  [res.data.shopEnInfoList[0]]
        }
      }).catch(()=> {
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.shop-container {
  width: 750px;
  position: fixed;
  bottom: 0;
  top: 750px;
  overflow-y: scroll;
  background-color: #fff;
}
@import "@/assets/css/search/search.scss";
.search-con{
  position: fixed;
  top:20px;
  padding:0 30px;
  background-color: transparent;
  .search-bar{
    background-color: #fff;
  }
}
</style>
