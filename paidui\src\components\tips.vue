<template>
  <uni-popup ref="popup" border-radius="10px" background-color="#fff">
    <view class="popup-container" :class="[careClass]">
      <view class="popup-title">
        {{ $t('wxTip1') }}
      </view>
      <view class="popup-content">
        {{ $t('wxCancelTip') }}
      </view>
      <view class="foot-container">
        <view class="btn" @click="closePopup">
          {{ $t('cancelBtn') }}
        </view>
        <view class="btn btn-confirm" @click="confirmPopup">
          {{ $t('confirmBtn1') }}
        </view>
      </view>
    </view>
  </uni-popup>
</template>
<script>
import { useOptionsStore } from '@/store/options'
import { mapState } from 'pinia'
export default {
  data() {
    return {
    }
  },
  props: {
    serviceInfo: {
      type: Object,
      default() { return null }
    }
  },
  computed: {
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='care'){
        return 'care-con'
      }
      if(this.verType=='en'){
        return 'en-con'
      }
      return  ''
    }
  },
  methods:{
    showTips(){
      this.$refs.popup.open('center')
    },
    closePopup(){
      this.$refs.popup.close()
    },
    confirmPopup(){
      this.$emit('cancelQueuing')
    }
  }
}
</script>
<style lang="scss" scoped>
.popup-container{
  padding:0px 30px 30px;
  box-sizing: border-box;
  width:624px;
  .popup-title{
    font-size: 32px;
    color: #000000;
    font-weight: bold;
    margin-bottom: 28px;
    text-align: center;
    height:116px;
    line-height:116px;
    border-bottom:1px solid #E5E5E5;
  }
  .popup-content{
    font-size: 28px;
    color: #333333;
    line-height: 40px;
    text-align:center;
    margin-bottom:45px;
    padding:0 10px;
  }
  .foot-container{
    display: flex;
    justify-content: space-between;
    .btn{
      width: 270px;
      height: 90px;
      line-height:90px;
      background: #F6F6F6;
      border-radius: 18px;
      font-size: 32px;
      text-align: center;
      font-weight:bold;
      color: #666;
      &.btn-confirm{
        background: linear-gradient( 90deg, #2892FF 0%, #007EFF 100%);
        color: #FFFFFF;
      }
    }
  }
}
.care-con{
  .popup-title{
    font-size:40px!important;
  }
  .popup-content{
    font-size: 36px!important;
  }
  .close{
    width:54px!important;
    height:54px!important;
    top:30px!important;
    position: absolute;
  }
}
.en-con{
  .popup-title{
    font-size:40px;
  }
}
</style>
