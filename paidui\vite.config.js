import { defineConfig } from 'vite'
import { fileURLToPath, URL } from 'node:url'
import uni from '@dcloudio/vite-plugin-uni'
import pxToViewport from 'postcss-px-to-viewport-8-plugin'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
  ],
  css: {
    postcss: {
      plugins: [
        pxToViewport({
          viewportWidth: 100,
          unitToConvert: 'px',
          unitPrecision: 5, // 单位转换后保留的精度
          propList: ['*'], // 能转化为vw的属性列表
          viewportUnit: 'rpx', // 希望使用的视口单位
          fontViewportUnit: 'rpx', // 字体使用的视口单位
          selectorBlackList: ['ignore-'], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
          minPixelValue: 0.1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
          mediaQuery: true, // 媒体查询里的单位是否需要转换单位
          replace: true, //  是否直接更换属性值，而不添加备用属性
          exclude: [
          ], // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
          include: [], // 如果设置了include，那将只有匹配到的文件才会被转换
          landscape: false, // 是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
          landscapeUnit: 'rpx', // 横屏时使用的单位
          landscapeWidth: 100, // 横屏时使用的视口宽度
        })
      ]
    }
  },
  build: {
    rollupOptions: {
      external: [
        // 'gio-uniapp'
        'gio-miniprogram-sdk-cdp'
      ]
    }
  },
  resolve: {
    extensions:['.js','.ts','.mjs','.ejs'],
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  define: {__VUE_I18N_FULL_INSTALL__: true, __VUE_I18N_LEGACY_API__: true, __INTLIFY_PROD_DEVTOOLS__: false},
})
