"use strict";
const common_vendor = require("../common/vendor.js");
function setupPageStackListener() {
  let pagesCount = 0;
  const updatePagesCount = (isPageShow, type) => {
    const $pages = getCurrentPages();
    if (isPageShow) {
      pagesCount = $pages.length;
    }
    let isBack = pagesCount > $pages.length ? true : false;
    let storageIsBack = common_vendor.index.getStorageSync("isBack");
    if (storageIsBack !== isBack) {
      common_vendor.index.setStorageSync("isBack", isBack);
    }
    console.log(`isBack: ${isBack}`);
  };
  const pageStackListenerMixin = {
    onShow() {
      updatePagesCount(false);
    },
    onLoad() {
      updatePagesCount(true);
    }
  };
  return {
    pagesCount,
    pageStackListenerMixin
  };
}
exports.setupPageStackListener = setupPageStackListener;
