"use strict";
function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;
  const later = function() {
    const last = +/* @__PURE__ */ new Date() - timestamp;
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      {
        result = func.apply(context, args);
        if (!timeout)
          context = args = null;
      }
    }
  };
  return function(...args2) {
    context = this;
    timestamp = +/* @__PURE__ */ new Date();
    if (!timeout)
      timeout = setTimeout(later, wait);
    return result;
  };
}
function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1e3;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}
function getUUID() {
  let i, uuid, s = [], hexDigits = "0123456789abcdef";
  for (i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(16 * Math.random()), 1);
    s[14] = "4";
    s[19] = hexDigits.substr(3 & s[19] | 8, 1);
    s[8] = s[13] = s[18] = s[23] = "";
    uuid = s.join("");
  }
  return uuid;
}
exports.debounce = debounce;
exports.getUUID = getUUID;
exports.parseTime = parseTime;
