"use strict";
const common_vendor = require("../common/vendor.js");
function callLogin() {
  my.call(
    "showLogin",
    {
      debug: false
    },
    (ret) => {
      ret.errCode;
      ret.isSuccess;
    }
  );
}
function callUserStatus() {
  return new Promise((resolve, reject) => {
    my.call(
      "userStatus",
      {
        debug: false
      },
      (res) => {
        const status = res.status;
        if (status == 0) {
          reject(res);
        } else {
          console.log(res, "App:UserStatus");
          resolve(res);
        }
      }
    );
  });
}
function callGetUserInfo() {
  return new Promise((resolve, reject) => {
    my.call(
      "getUserInfo",
      {
        debug: false
      },
      (res) => {
        console.log(res, "App:UserInfo");
        if (res) {
          resolve(res);
        } else {
          reject(res);
        }
      }
    );
  });
}
function checkSessionIdIsvalid() {
  return new Promise((resolve, reject) => {
    my.call(
      "checkSessionIsvalid",
      {
        debug: false
      },
      (res) => {
        console.log(res, "App:checkSessionIsvalid");
        if (res) {
          resolve(res);
        } else {
          reject(res);
        }
      }
    );
  });
}
function callGetYDRZToken(sourceId = "019018") {
  return new Promise((resolve, reject) => {
    my.call(
      "getYDRZToken",
      {
        debug: false,
        sourceId
        //目标业务平台sourceId
      },
      (res) => {
        console.log(res, "App:YDRZToken");
        if (res.token) {
          resolve(res);
        } else {
          reject(res);
        }
      }
    );
  });
}
function callGetCatchInfo({ isShowLocationAlert = false }) {
  return new Promise((resolve, reject) => {
    my.call(
      "getCatchInfo",
      {
        debug: false,
        isShowLocationAlert,
        isGetLocation: isShowLocationAlert
      },
      (res) => {
        console.log(res, "App:getCatchInfo");
        resolve(res);
      }
    );
  });
}
function callGetLocation() {
  return new Promise((resolve, reject) => {
    my.call(
      "getLocation",
      {
        debug: false,
        type: 1
      },
      (res) => {
        console.log(res, "App:callGetLocation");
        resolve(res);
      }
    );
  });
}
function getSsoToken(sourceId) {
  return new Promise((resolve, reject) => {
    callUserStatus().then(() => {
      checkSessionIdIsvalid().then(() => {
        callGetYDRZToken(sourceId).then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
      }).catch(() => {
        callOverTime();
      });
    }).catch(() => {
      callLogin();
    });
  });
}
function openOtherApp({
  debug = false,
  type = "0",
  packageName,
  schemeString
}) {
  return new Promise((resolve, reject) => {
    my.call(
      "openOtherAPP",
      {
        debug,
        type,
        packageName,
        schemeString
      },
      (res) => {
        if (res.resCode == 0 || res == "success") {
          resolve(res);
        } else {
          reject(res);
        }
      }
    );
  });
}
async function openNaviApp({
  latitude,
  longitude,
  app,
  address,
  name
}) {
  const platform = common_vendor.index.getSystemInfoSync().platform.toLowerCase();
  let result = common_vendor.exported.transform(
    [longitude, latitude],
    // 经纬度坐标[116.403988, 39.914266]
    common_vendor.exported.BD09,
    // 当前坐标系:坐标系：WGS84、BD09-百度坐标、GCJ02-高德坐标
    common_vendor.exported.GCJ02
    // 目标坐标系
  );
  if (app == "amap") {
    const queryStr = `?sourceApplication=${encodeURIComponent("中国移动")}&poiname=${encodeURIComponent(address)}&lat=${result[1] || latitude}&lon=${result[0] || longitude}&dev=0`;
    return openOtherApp({
      packageName: platform == "ios" ? "iosamap" : "com.autonavi.minimap",
      schemeString: platform == "ios" ? `iosamap://viewMap${queryStr}` : `androidamap://viewMap${queryStr}`
    });
  } else if (app == "baidu") {
    const queryStr = `?location=${latitude},${longitude}&title=${name}&content=${address}&output=html&src=webapp.baidu.openAPIdemo&coord_type=bd09ll`;
    return openOtherApp({
      packageName: platform == "ios" ? "" : "com.baidu.BaiduMap",
      schemeString: `baidumap://map/marker${queryStr}`
    });
  } else if (app == "tencent") {
    const queryStr = `?marker=coord:${result[1] || latitude},${result[0] || longitude};title:${name};addr:${address}&referer=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77`;
    console.log("queryStr", queryStr);
    return openOtherApp({
      packageName: platform == "ios" ? "" : "com.tencent.map",
      schemeString: `qqmap://map/marker${queryStr}`
    });
  } else if (app == "apple") {
    const queryStr = `http://maps.apple.com/?q=${name}&ll=${result[1] || latitude},${result[0] || longitude}&address=${address}&z=10&t=m`;
    console.log("queryStr", queryStr);
    return openOtherApp({
      packageName: "",
      schemeString: queryStr
    });
  } else {
    return false;
  }
}
const cmcc = {
  callGetYDRZToken,
  callGetUserInfo,
  callUserStatus,
  callLogin,
  callGetLocation,
  callGetCatchInfo,
  checkSessionIdIsvalid,
  getSsoToken,
  openOtherApp,
  openNaviApp
};
exports.callGetUserInfo = callGetUserInfo;
exports.cmcc = cmcc;
