"use strict";const stationInfo={notice:"notice",nearShop:"The Nearest Business Hall",workPeriod:"Business Hours",serviceInfo:"Business Scope",tipsDes:"We recommend clicking on 'Business Scope' to confirm that this business hall can fulfill your needs before taking a number.",toogle:"Switch",working:"Open for Business",huisinessClose:"Temporarily Closed",currentQueue:"Current Queue",stationName:"Business Hall",stationAddress:"Add"};const takeInfoObj={takeInfo:"Queue Number Acquisition Information",takeRecord:"Queue Number Acquisition Record",takeNumber:"Queue Number",waitCount:"Waiting Ahead",takeTime:"Queue Number Acquisition Time",stationCode:"Business Hall",stationAddress:"Add",waitDetail:"Queue Details",waitNum:"Waiting Count"};const appointInfo={appoint:"Reservation Information",appointRecord:"Reservation Record",appointNumber:"Reservation Number",appointTime:"Appointment Time for  Service",reservedTime:"Reserved Time",appointToday:"Today",appointTomorrow:"Tomorrow",pleaseSelectTitle:"Please select the appointment time for your service.",appointNoTime:"No availability"};const wxTipsInfo={wxTip:"Warm Reminder",wxTip1:"Tips",wxTakeSecTip:"Please pay attention to the real-time number calling screen.If you miss your turn, you need to obtain a new queue number. Have a nice day!",wxAppointSec1Tip:"1. Please arrive at the business hall with your valid identification in advance to sign-in and get your queue ticket; and pay attention to the SMS reminder.",wxAppointSec2Tip:"2. If you need to reschedule your appointment, please cancel the existing one first. Thank you!",wxAppointCon1Tip:"You can sign-in and obtain your queue number 30 minutes prior to your appointment time.",wxAppointCon2Tip:"If you need to reschedule your appointment, please cancel the existing one first. Thank you!",wxCardTip:"Each number is allowed a maximum of 5 onlinenumber takings or  cancellations per day. After successfully taking anumber, please pay attention to the queue statusand the system announcements to avoid missingyour turn.",wxAppointTimeTip:"After successfully making an online appointment, if you need to make any changes, please cancel it first before proceeding.",wxCancelTip:"Are you sure you want to cancel the ticket number?"};const searcPlaceholder={shortPlaceholder:"Please enter the name/address of the business hall you want to search for.",langPlaceholder:"Please enter the name/address of the business hall you want to search for."};const btnInfo={takeNow:"Get a Number Now",appointOnline:"Online Appointment",appointConfirm:"Confirm the Reservation",signTake:"Sign in and Obtain a Queue Number",cancelAppoint:"Cancel the Reservation",historyRecord:"History Record",cancelBtn:"Cancel",confirmBtn:"Confirm",confirmBtn1:"Sure",apptake:"Get a Number /Online Appointment",cancelQueue:"Cancel Queuing"};const orderStatus={appointSec:"Reserve Success",takeSec:"Queue Number Acquisition Success"};const textObj={selectText:"Please select",mpappTitle:"Online Queue Reservation",mapSelTitle:"Navigate to the Destination",mapErrorTip:"APP not installed",emptyTip:"The online appointment system at the business hall is currently under maintenance.",emptyBtnTip:"Refresh and Try Again",refreshTip:"Refresh too frequently",noRecord:"No records available at the moment",servicHallTitle:"Types of Main Business in the Service Hall"};const message={...stationInfo,...takeInfoObj,...appointInfo,...orderStatus,...wxTipsInfo,...btnInfo,...searcPlaceholder,...textObj};exports.message=message;
