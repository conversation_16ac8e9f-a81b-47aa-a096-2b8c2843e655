<view class="{{('tabs') + ' ' + 'data-v-b6853b6d' + ' ' + b}}"><view a:for="{{a}}" a:for-item="item" class="{{('tab-item') + ' ' + 'data-v-b6853b6d' + ' ' + (item.b && 'active')}}" onTap="{{item.c}}">{{item.a}}</view></view><view class="{{('record-list') + ' ' + 'data-v-b6853b6d' + ' ' + g}}"><view a:for="{{c}}" a:for-item="items" class="record-item data-v-b6853b6d"><view class="record-item-title data-v-b6853b6d"><view class="record-item-title-time data-v-b6853b6d"><view class="data-v-b6853b6d">{{d}}</view><view class="data-v-b6853b6d">{{items.a}}</view></view><view class="{{('record-status') + ' ' + 'data-v-b6853b6d' + ' ' + items.c}}">{{items.b}}</view></view><view class="content data-v-b6853b6d"><view a:for="{{items.d}}" a:for-item="item" class="content-item data-v-b6853b6d"><view class="label-con data-v-b6853b6d">{{item.a}}</view><view class="value data-v-b6853b6d">{{item.b}}</view></view></view></view><view a:if="{{e}}" class="data-v-b6853b6d"><view class="empty-img data-v-b6853b6d"></view><view class="empty-text data-v-b6853b6d">{{f}}</view></view></view>