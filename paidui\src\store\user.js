import { defineStore } from 'pinia'
import { encryptAes, decryptAes } from '@/utils/transit-encrypt'

const {
  msisdn ='',
  msisdnmask = '',
  passid = '',
  usessionid = '',
  gdpUserId = '',
} = uni.getStorageSync('user') || {}

const {
  province,
  city,
  cityName,
  latitude,
  longitude,
} = uni.getStorageSync('geo') || {}

export const useUserStore = defineStore('user', {
  state: () => {
    /**
     * @namespace
     * @property {object|null} info
     * @property {string} info.msisdn 手机号
     * @property {string} info.msisdnmask 脱敏手机号
     * @property {string} info.passid
     * @property {string} info.usessionid
     * @property {string} info.gdpUserId 插码用户ID
    */
    const data = {
      isLogined: !!msisdn,
      info: {
        msisdn: msisdn ? decryptAes(msisdn) : '',
        msisdnmask,
        passid,
        usessionid,
        gdpUserId,
        province: province || '',
      },
      geo: {
        province,
        city,
        cityName,
        latitude,
        longitude
      }
    }
    return data
  },

  actions: {
    setUser (user) {
      this.info =  Object.assign({} , user || {})
      if(user && user.msisdn){
        user.msisdn = encryptAes(user.msisdn)
      }
      uni.setStorageSync('user',user)
      this.isLogined = (user && user.msisdn) ? true : false
    },
    setGeo (geo) {
      Object.assign(this.geo, geo)
      uni.setStorageSync('geo',this.geo)
    },
  },
})

export default {
  useUserStore
}
