"use strict";const _export_sfc=(sfc,props)=>{const target=sfc.__vccOpts||sfc;for(const[key,val]of props){target[key]=val}return target};
/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**/function makeMap(str,expectsLowerCase){const set2=new Set(str.split(","));return val=>set2.has(val)}const EMPTY_OBJ={};const EMPTY_ARR=[];const NOOP=()=>{};const NO=()=>false;const isOn=key=>key.charCodeAt(0)===111&&key.charCodeAt(1)===110&&(key.charCodeAt(2)>122||key.charCodeAt(2)<97);const isModelListener=key=>key.startsWith("onUpdate:");const extend=Object.assign;const remove=(arr,el)=>{const i2=arr.indexOf(el);if(i2>-1){arr.splice(i2,1)}};const hasOwnProperty$4=Object.prototype.hasOwnProperty;const hasOwn$3=(val,key)=>hasOwnProperty$4.call(val,key);const isArray$3=Array.isArray;const isMap=val=>toTypeString$2(val)==="[object Map]";const isSet=val=>toTypeString$2(val)==="[object Set]";const isFunction$2=val=>typeof val==="function";const isString$2=val=>typeof val==="string";const isSymbol=val=>typeof val==="symbol";const isObject$4=val=>val!==null&&typeof val==="object";const isPromise=val=>(isObject$4(val)||isFunction$2(val))&&isFunction$2(val.then)&&isFunction$2(val.catch);const objectToString$2=Object.prototype.toString;const toTypeString$2=value=>objectToString$2.call(value);const toRawType=value=>toTypeString$2(value).slice(8,-1);const isPlainObject$3=val=>toTypeString$2(val)==="[object Object]";const isIntegerKey=key=>isString$2(key)&&key!=="NaN"&&key[0]!=="-"&&""+parseInt(key,10)===key;const isReservedProp=makeMap(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted");const cacheStringFunction=fn=>{const cache2=Object.create(null);return str=>{const hit=cache2[str];return hit||(cache2[str]=fn(str))}};const camelizeRE=/-(\w)/g;const camelize=cacheStringFunction((str=>str.replace(camelizeRE,((_2,c2)=>c2?c2.toUpperCase():""))));const hyphenateRE=/\B([A-Z])/g;const hyphenate=cacheStringFunction((str=>str.replace(hyphenateRE,"-$1").toLowerCase()));const capitalize=cacheStringFunction((str=>str.charAt(0).toUpperCase()+str.slice(1)));const toHandlerKey=cacheStringFunction((str=>{const s2=str?`on${capitalize(str)}`:``;return s2}));const hasChanged=(value,oldValue)=>!Object.is(value,oldValue);const invokeArrayFns$1=(fns,arg)=>{for(let i2=0;i2<fns.length;i2++){fns[i2](arg)}};const def=(obj,key,value)=>{Object.defineProperty(obj,key,{configurable:true,enumerable:false,value:value})};const looseToNumber=val=>{const n2=parseFloat(val);return isNaN(n2)?val:n2};let _globalThis;const getGlobalThis=()=>_globalThis||(_globalThis=typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:typeof window!=="undefined"?window:typeof global!=="undefined"?global:{});function normalizeStyle(value){if(isArray$3(value)){const res={};for(let i2=0;i2<value.length;i2++){const item=value[i2];const normalized=isString$2(item)?parseStringStyle(item):normalizeStyle(item);if(normalized){for(const key in normalized){res[key]=normalized[key]}}}return res}else if(isString$2(value)||isObject$4(value)){return value}}const listDelimiterRE=/;(?![^(]*\))/g;const propertyDelimiterRE=/:([^]+)/;const styleCommentRE=/\/\*[^]*?\*\//g;function parseStringStyle(cssText){const ret={};cssText.replace(styleCommentRE,"").split(listDelimiterRE).forEach((item=>{if(item){const tmp=item.split(propertyDelimiterRE);tmp.length>1&&(ret[tmp[0].trim()]=tmp[1].trim())}}));return ret}function normalizeClass(value){let res="";if(isString$2(value)){res=value}else if(isArray$3(value)){for(let i2=0;i2<value.length;i2++){const normalized=normalizeClass(value[i2]);if(normalized){res+=normalized+" "}}}else if(isObject$4(value)){for(const name in value){if(value[name]){res+=name+" "}}}return res.trim()}const toDisplayString$1=val=>isString$2(val)?val:val==null?"":isArray$3(val)||isObject$4(val)&&(val.toString===objectToString$2||!isFunction$2(val.toString))?JSON.stringify(val,replacer,2):String(val);const replacer=(_key,val)=>{if(val&&val.__v_isRef){return replacer(_key,val.value)}else if(isMap(val)){return{[`Map(${val.size})`]:[...val.entries()].reduce(((entries,[key,val2],i2)=>{entries[stringifySymbol(key,i2)+" =>"]=val2;return entries}),{})}}else if(isSet(val)){return{[`Set(${val.size})`]:[...val.values()].map((v2=>stringifySymbol(v2)))}}else if(isSymbol(val)){return stringifySymbol(val)}else if(isObject$4(val)&&!isArray$3(val)&&!isPlainObject$3(val)){return String(val)}return val};const stringifySymbol=(v2,i2="")=>{var _a;return isSymbol(v2)?`Symbol(${(_a=v2.description)!=null?_a:i2})`:v2};const SLOT_DEFAULT_NAME="d";const ON_SHOW="onShow";const ON_HIDE="onHide";const ON_LAUNCH="onLaunch";const ON_ERROR="onError";const ON_THEME_CHANGE="onThemeChange";const ON_PAGE_NOT_FOUND="onPageNotFound";const ON_UNHANDLE_REJECTION="onUnhandledRejection";const ON_EXIT="onExit";const ON_LOAD="onLoad";const ON_READY="onReady";const ON_UNLOAD="onUnload";const ON_INIT="onInit";const ON_SAVE_EXIT_STATE="onSaveExitState";const ON_RESIZE="onResize";const ON_BACK_PRESS="onBackPress";const ON_PAGE_SCROLL="onPageScroll";const ON_TAB_ITEM_TAP="onTabItemTap";const ON_REACH_BOTTOM="onReachBottom";const ON_PULL_DOWN_REFRESH="onPullDownRefresh";const ON_SHARE_TIMELINE="onShareTimeline";const ON_ADD_TO_FAVORITES="onAddToFavorites";const ON_SHARE_APP_MESSAGE="onShareAppMessage";const ON_NAVIGATION_BAR_BUTTON_TAP="onNavigationBarButtonTap";const ON_NAVIGATION_BAR_SEARCH_INPUT_CLICKED="onNavigationBarSearchInputClicked";const ON_NAVIGATION_BAR_SEARCH_INPUT_CHANGED="onNavigationBarSearchInputChanged";const ON_NAVIGATION_BAR_SEARCH_INPUT_CONFIRMED="onNavigationBarSearchInputConfirmed";const ON_NAVIGATION_BAR_SEARCH_INPUT_FOCUS_CHANGED="onNavigationBarSearchInputFocusChanged";const customizeRE=/:/g;function customizeEvent(str){return camelize(str.replace(customizeRE,"-"))}function hasLeadingSlash(str){return str.indexOf("/")===0}function addLeadingSlash(str){return hasLeadingSlash(str)?str:"/"+str}const invokeArrayFns=(fns,arg)=>{let ret;for(let i2=0;i2<fns.length;i2++){ret=fns[i2](arg)}return ret};function once(fn,ctx=null){let res;return(...args)=>{if(fn){res=fn.apply(ctx,args);fn=null}return res}}function getValueByDataPath(obj,path){if(!isString$2(path)){return}path=path.replace(/\[(\d+)\]/g,".$1");const parts=path.split(".");let key=parts[0];if(!obj){obj={}}if(parts.length===1){return obj[key]}return getValueByDataPath(obj[key],parts.slice(1).join("."))}const encode=encodeURIComponent;function stringifyQuery(obj,encodeStr=encode){const res=obj?Object.keys(obj).map((key=>{let val=obj[key];if(typeof val===void 0||val===null){val=""}else if(isPlainObject$3(val)){val=JSON.stringify(val)}return encodeStr(key)+"="+encodeStr(val)})).filter((x2=>x2.length>0)).join("&"):null;return res?`?${res}`:""}class EventChannel{constructor(id2,events){this.id=id2;this.listener={};this.emitCache=[];if(events){Object.keys(events).forEach((name=>{this.on(name,events[name])}))}}emit(eventName,...args){const fns=this.listener[eventName];if(!fns){return this.emitCache.push({eventName:eventName,args:args})}fns.forEach((opt=>{opt.fn.apply(opt.fn,args)}));this.listener[eventName]=fns.filter((opt=>opt.type!=="once"))}on(eventName,fn){this._addListener(eventName,"on",fn);this._clearCache(eventName)}once(eventName,fn){this._addListener(eventName,"once",fn);this._clearCache(eventName)}off(eventName,fn){const fns=this.listener[eventName];if(!fns){return}if(fn){for(let i2=0;i2<fns.length;){if(fns[i2].fn===fn){fns.splice(i2,1);i2--}i2++}}else{delete this.listener[eventName]}}_clearCache(eventName){for(let index2=0;index2<this.emitCache.length;index2++){const cache2=this.emitCache[index2];const _name=eventName?cache2.eventName===eventName?eventName:null:cache2.eventName;if(!_name)continue;const location=this.emit.apply(this,[_name,...cache2.args]);if(typeof location==="number"){this.emitCache.pop();continue}this.emitCache.splice(index2,1);index2--}}_addListener(eventName,type,fn){(this.listener[eventName]||(this.listener[eventName]=[])).push({fn:fn,type:type})}}const PAGE_HOOKS=[ON_INIT,ON_LOAD,ON_SHOW,ON_HIDE,ON_UNLOAD,ON_BACK_PRESS,ON_PAGE_SCROLL,ON_TAB_ITEM_TAP,ON_REACH_BOTTOM,ON_PULL_DOWN_REFRESH,ON_SHARE_TIMELINE,ON_SHARE_APP_MESSAGE,ON_ADD_TO_FAVORITES,ON_SAVE_EXIT_STATE,ON_NAVIGATION_BAR_BUTTON_TAP,ON_NAVIGATION_BAR_SEARCH_INPUT_CLICKED,ON_NAVIGATION_BAR_SEARCH_INPUT_CHANGED,ON_NAVIGATION_BAR_SEARCH_INPUT_CONFIRMED,ON_NAVIGATION_BAR_SEARCH_INPUT_FOCUS_CHANGED];function isRootHook(name){return PAGE_HOOKS.indexOf(name)>-1}const UniLifecycleHooks=[ON_SHOW,ON_HIDE,ON_LAUNCH,ON_ERROR,ON_THEME_CHANGE,ON_PAGE_NOT_FOUND,ON_UNHANDLE_REJECTION,ON_EXIT,ON_INIT,ON_LOAD,ON_READY,ON_UNLOAD,ON_RESIZE,ON_BACK_PRESS,ON_PAGE_SCROLL,ON_TAB_ITEM_TAP,ON_REACH_BOTTOM,ON_PULL_DOWN_REFRESH,ON_SHARE_TIMELINE,ON_ADD_TO_FAVORITES,ON_SHARE_APP_MESSAGE,ON_SAVE_EXIT_STATE,ON_NAVIGATION_BAR_BUTTON_TAP,ON_NAVIGATION_BAR_SEARCH_INPUT_CLICKED,ON_NAVIGATION_BAR_SEARCH_INPUT_CHANGED,ON_NAVIGATION_BAR_SEARCH_INPUT_CONFIRMED,ON_NAVIGATION_BAR_SEARCH_INPUT_FOCUS_CHANGED];const MINI_PROGRAM_PAGE_RUNTIME_HOOKS=(()=>({onPageScroll:1,onShareAppMessage:1<<1,onShareTimeline:1<<2}))();function isUniLifecycleHook(name,value,checkType=true){if(checkType&&!isFunction$2(value)){return false}if(UniLifecycleHooks.indexOf(name)>-1){return true}else if(name.indexOf("on")===0){return true}return false}let vueApp;const createVueAppHooks=[];function onCreateVueApp(hook){if(vueApp){return hook(vueApp)}createVueAppHooks.push(hook)}function invokeCreateVueAppHook(app){vueApp=app;createVueAppHooks.forEach((hook=>hook(app)))}const invokeCreateErrorHandler=once(((app,createErrorHandler2)=>{if(isFunction$2(app._component.onError)){return createErrorHandler2(app)}}));const E$1=function(){};E$1.prototype={on:function(name,callback,ctx){var e2=this.e||(this.e={});(e2[name]||(e2[name]=[])).push({fn:callback,ctx:ctx});return this},once:function(name,callback,ctx){var self2=this;function listener(){self2.off(name,listener);callback.apply(ctx,arguments)}listener._=callback;return this.on(name,listener,ctx)},emit:function(name){var data=[].slice.call(arguments,1);var evtArr=((this.e||(this.e={}))[name]||[]).slice();var i2=0;var len=evtArr.length;for(i2;i2<len;i2++){evtArr[i2].fn.apply(evtArr[i2].ctx,data)}return this},off:function(name,callback){var e2=this.e||(this.e={});var evts=e2[name];var liveEvents=[];if(evts&&callback){for(var i2=evts.length-1;i2>=0;i2--){if(evts[i2].fn===callback||evts[i2].fn._===callback){evts.splice(i2,1);break}}liveEvents=evts}liveEvents.length?e2[name]=liveEvents:delete e2[name];return this}};var E$1$1=E$1;const isObject$3=val=>val!==null&&typeof val==="object";const defaultDelimiters=["{","}"];class BaseFormatter{constructor(){this._caches=Object.create(null)}interpolate(message,values,delimiters=defaultDelimiters){if(!values){return[message]}let tokens=this._caches[message];if(!tokens){tokens=parse$2(message,delimiters);this._caches[message]=tokens}return compile(tokens,values)}}const RE_TOKEN_LIST_VALUE=/^(?:\d)+/;const RE_TOKEN_NAMED_VALUE=/^(?:\w)+/;function parse$2(format,[startDelimiter,endDelimiter]){const tokens=[];let position=0;let text="";while(position<format.length){let char=format[position++];if(char===startDelimiter){if(text){tokens.push({type:"text",value:text})}text="";let sub="";char=format[position++];while(char!==void 0&&char!==endDelimiter){sub+=char;char=format[position++]}const isClosed=char===endDelimiter;const type=RE_TOKEN_LIST_VALUE.test(sub)?"list":isClosed&&RE_TOKEN_NAMED_VALUE.test(sub)?"named":"unknown";tokens.push({value:sub,type:type})}else{text+=char}}text&&tokens.push({type:"text",value:text});return tokens}function compile(tokens,values){const compiled=[];let index2=0;const mode=Array.isArray(values)?"list":isObject$3(values)?"named":"unknown";if(mode==="unknown"){return compiled}while(index2<tokens.length){const token=tokens[index2];switch(token.type){case"text":compiled.push(token.value);break;case"list":compiled.push(values[parseInt(token.value,10)]);break;case"named":if(mode==="named"){compiled.push(values[token.value])}break}index2++}return compiled}const LOCALE_ZH_HANS="zh-Hans";const LOCALE_ZH_HANT="zh-Hant";const LOCALE_EN="en";const LOCALE_FR="fr";const LOCALE_ES="es";const hasOwnProperty$3=Object.prototype.hasOwnProperty;const hasOwn$2=(val,key)=>hasOwnProperty$3.call(val,key);const defaultFormatter=new BaseFormatter;function include(str,parts){return!!parts.find((part=>str.indexOf(part)!==-1))}function startsWith(str,parts){return parts.find((part=>str.indexOf(part)===0))}function normalizeLocale(locale,messages2){if(!locale){return}locale=locale.trim().replace(/_/g,"-");if(messages2&&messages2[locale]){return locale}locale=locale.toLowerCase();if(locale==="chinese"){return LOCALE_ZH_HANS}if(locale.indexOf("zh")===0){if(locale.indexOf("-hans")>-1){return LOCALE_ZH_HANS}if(locale.indexOf("-hant")>-1){return LOCALE_ZH_HANT}if(include(locale,["-tw","-hk","-mo","-cht"])){return LOCALE_ZH_HANT}return LOCALE_ZH_HANS}let locales=[LOCALE_EN,LOCALE_FR,LOCALE_ES];if(messages2&&Object.keys(messages2).length>0){locales=Object.keys(messages2)}const lang=startsWith(locale,locales);if(lang){return lang}}class I18n{constructor({locale:locale,fallbackLocale:fallbackLocale,messages:messages2,watcher:watcher,formater:formater2}){this.locale=LOCALE_EN;this.fallbackLocale=LOCALE_EN;this.message={};this.messages={};this.watchers=[];if(fallbackLocale){this.fallbackLocale=fallbackLocale}this.formater=formater2||defaultFormatter;this.messages=messages2||{};this.setLocale(locale||LOCALE_EN);if(watcher){this.watchLocale(watcher)}}setLocale(locale){const oldLocale=this.locale;this.locale=normalizeLocale(locale,this.messages)||this.fallbackLocale;if(!this.messages[this.locale]){this.messages[this.locale]={}}this.message=this.messages[this.locale];if(oldLocale!==this.locale){this.watchers.forEach((watcher=>{watcher(this.locale,oldLocale)}))}}getLocale(){return this.locale}watchLocale(fn){const index2=this.watchers.push(fn)-1;return()=>{this.watchers.splice(index2,1)}}add(locale,message,override=true){const curMessages=this.messages[locale];if(curMessages){if(override){Object.assign(curMessages,message)}else{Object.keys(message).forEach((key=>{if(!hasOwn$2(curMessages,key)){curMessages[key]=message[key]}}))}}else{this.messages[locale]=message}}f(message,values,delimiters){return this.formater.interpolate(message,values,delimiters).join("")}t(key,locale,values){let message=this.message;if(typeof locale==="string"){locale=normalizeLocale(locale,this.messages);locale&&(message=this.messages[locale])}else{values=locale}if(!hasOwn$2(message,key)){console.warn(`Cannot translate the value of keypath ${key}. Use the value of keypath as default.`);return key}return this.formater.interpolate(message[key],values).join("")}}function watchAppLocale(appVm,i18n){if(appVm.$watchLocale){appVm.$watchLocale((newLocale=>{i18n.setLocale(newLocale)}))}else{appVm.$watch((()=>appVm.$locale),(newLocale=>{i18n.setLocale(newLocale)}))}}function getDefaultLocale(){if(typeof index!=="undefined"&&index.getLocale){return index.getLocale()}if(typeof global!=="undefined"&&global.getLocale){return global.getLocale()}return LOCALE_EN}function initVueI18n(locale,messages2={},fallbackLocale,watcher){if(typeof locale!=="string"){const options=[messages2,locale];locale=options[0];messages2=options[1]}if(typeof locale!=="string"){locale=getDefaultLocale()}if(typeof fallbackLocale!=="string"){fallbackLocale=typeof __uniConfig!=="undefined"&&__uniConfig.fallbackLocale||LOCALE_EN}const i18n=new I18n({locale:locale,fallbackLocale:fallbackLocale,messages:messages2,watcher:watcher});let t2=(key,values)=>{if(typeof getApp!=="function"){t2=function(key2,values2){return i18n.t(key2,values2)}}else{let isWatchedAppLocale=false;t2=function(key2,values2){const appVm=getApp().$vm;if(appVm){appVm.$locale;if(!isWatchedAppLocale){isWatchedAppLocale=true;watchAppLocale(appVm,i18n)}}return i18n.t(key2,values2)}}return t2(key,values)};return{i18n:i18n,f(message,values,delimiters){return i18n.f(message,values,delimiters)},t(key,values){return t2(key,values)},add(locale2,message,override=true){return i18n.add(locale2,message,override)},watch(fn){return i18n.watchLocale(fn)},getLocale(){return i18n.getLocale()},setLocale(newLocale){return i18n.setLocale(newLocale)}}}function getBaseSystemInfo(){return my.getSystemInfoSync()}function tryCatch(fn){return function(){try{return fn.apply(fn,arguments)}catch(e2){console.error(e2)}}}let invokeCallbackId=1;const invokeCallbacks={};function addInvokeCallback(id2,name,callback,keepAlive=false){invokeCallbacks[id2]={name:name,keepAlive:keepAlive,callback:callback};return id2}function invokeCallback(id2,res,extras){if(typeof id2==="number"){const opts=invokeCallbacks[id2];if(opts){if(!opts.keepAlive){delete invokeCallbacks[id2]}return opts.callback(res,extras)}}return res}const API_SUCCESS="success";const API_FAIL="fail";const API_COMPLETE="complete";function getApiCallbacks(args){const apiCallbacks={};for(const name in args){const fn=args[name];if(isFunction$2(fn)){apiCallbacks[name]=tryCatch(fn);delete args[name]}}return apiCallbacks}function normalizeErrMsg(errMsg,name){if(!errMsg||errMsg.indexOf(":fail")===-1){return name+":ok"}return name+errMsg.substring(errMsg.indexOf(":fail"))}function createAsyncApiCallback(name,args={},{beforeAll:beforeAll,beforeSuccess:beforeSuccess}={}){if(!isPlainObject$3(args)){args={}}const{success:success,fail:fail,complete:complete}=getApiCallbacks(args);const hasSuccess=isFunction$2(success);const hasFail=isFunction$2(fail);const hasComplete=isFunction$2(complete);const callbackId=invokeCallbackId++;addInvokeCallback(callbackId,name,(res=>{res=res||{};res.errMsg=normalizeErrMsg(res.errMsg,name);isFunction$2(beforeAll)&&beforeAll(res);if(res.errMsg===name+":ok"){isFunction$2(beforeSuccess)&&beforeSuccess(res,args);hasSuccess&&success(res)}else{hasFail&&fail(res)}hasComplete&&complete(res)}));return callbackId}const HOOK_SUCCESS="success";const HOOK_FAIL="fail";const HOOK_COMPLETE="complete";const globalInterceptors={};const scopedInterceptors={};function wrapperHook(hook,params){return function(data){return hook(data,params)||data}}function queue$1(hooks,data,params){let promise=false;for(let i2=0;i2<hooks.length;i2++){const hook=hooks[i2];if(promise){promise=Promise.resolve(wrapperHook(hook,params))}else{const res=hook(data,params);if(isPromise(res)){promise=Promise.resolve(res)}if(res===false){return{then(){},catch(){}}}}}return promise||{then(callback){return callback(data)},catch(){}}}function wrapperOptions(interceptors2,options={}){[HOOK_SUCCESS,HOOK_FAIL,HOOK_COMPLETE].forEach((name=>{const hooks=interceptors2[name];if(!isArray$3(hooks)){return}const oldCallback=options[name];options[name]=function callbackInterceptor(res){queue$1(hooks,res,options).then((res2=>isFunction$2(oldCallback)&&oldCallback(res2)||res2))}}));return options}function wrapperReturnValue(method,returnValue2){const returnValueHooks=[];if(isArray$3(globalInterceptors.returnValue)){returnValueHooks.push(...globalInterceptors.returnValue)}const interceptor=scopedInterceptors[method];if(interceptor&&isArray$3(interceptor.returnValue)){returnValueHooks.push(...interceptor.returnValue)}returnValueHooks.forEach((hook=>{returnValue2=hook(returnValue2)||returnValue2}));return returnValue2}function getApiInterceptorHooks(method){const interceptor=Object.create(null);Object.keys(globalInterceptors).forEach((hook=>{if(hook!=="returnValue"){interceptor[hook]=globalInterceptors[hook].slice()}}));const scopedInterceptor=scopedInterceptors[method];if(scopedInterceptor){Object.keys(scopedInterceptor).forEach((hook=>{if(hook!=="returnValue"){interceptor[hook]=(interceptor[hook]||[]).concat(scopedInterceptor[hook])}}))}return interceptor}function invokeApi(method,api,options,params){const interceptor=getApiInterceptorHooks(method);if(interceptor&&Object.keys(interceptor).length){if(isArray$3(interceptor.invoke)){const res=queue$1(interceptor.invoke,options);return res.then((options2=>api(wrapperOptions(getApiInterceptorHooks(method),options2),...params)))}else{return api(wrapperOptions(interceptor,options),...params)}}return api(options,...params)}function hasCallback(args){if(isPlainObject$3(args)&&[API_SUCCESS,API_FAIL,API_COMPLETE].find((cb=>isFunction$2(args[cb])))){return true}return false}function handlePromise(promise){return promise}function promisify$1(name,fn){return(args={},...rest)=>{if(hasCallback(args)){return wrapperReturnValue(name,invokeApi(name,fn,args,rest))}return wrapperReturnValue(name,handlePromise(new Promise(((resolve2,reject)=>{invokeApi(name,fn,extend(args,{success:resolve2,fail:reject}),rest)}))))}}function formatApiArgs(args,options){args[0];{return}}function invokeSuccess(id2,name,res){const result={errMsg:name+":ok"};return invokeCallback(id2,extend(res||{},result))}function invokeFail(id2,name,errMsg,errRes={}){const apiErrMsg=name+":fail"+(errMsg?" "+errMsg:"");delete errRes.errCode;let res=extend({errMsg:apiErrMsg},errRes);return invokeCallback(id2,res)}function beforeInvokeApi(name,args,protocol,options){const errMsg=formatApiArgs(args);if(errMsg){return errMsg}}function parseErrMsg(errMsg){if(!errMsg||isString$2(errMsg)){return errMsg}if(errMsg.stack){console.error(errMsg.message+"\n"+errMsg.stack);return errMsg.message}return errMsg}function wrapperTaskApi(name,fn,protocol,options){return args=>{const id2=createAsyncApiCallback(name,args,options);const errMsg=beforeInvokeApi(name,[args]);if(errMsg){return invokeFail(id2,name,errMsg)}return fn(args,{resolve:res=>invokeSuccess(id2,name,res),reject:(errMsg2,errRes)=>invokeFail(id2,name,parseErrMsg(errMsg2),errRes)})}}function wrapperSyncApi(name,fn,protocol,options){return(...args)=>{const errMsg=beforeInvokeApi(name,args);if(errMsg){throw new Error(errMsg)}return fn.apply(null,args)}}function wrapperAsyncApi(name,fn,protocol,options){return wrapperTaskApi(name,fn,protocol,options)}function defineSyncApi(name,fn,protocol,options){return wrapperSyncApi(name,fn)}function defineAsyncApi(name,fn,protocol,options){return promisify$1(name,wrapperAsyncApi(name,fn,void 0,options))}const API_UPX2PX="upx2px";const EPS=1e-4;const BASE_DEVICE_WIDTH=750;let isIOS=false;let deviceWidth=0;let deviceDPR=0;function checkDeviceWidth(){const{platform:platform,pixelRatio:pixelRatio,windowWidth:windowWidth}=getBaseSystemInfo();deviceWidth=windowWidth;deviceDPR=pixelRatio;isIOS=platform==="ios"}const upx2px=defineSyncApi(API_UPX2PX,((number2,newDeviceWidth)=>{if(deviceWidth===0){checkDeviceWidth()}number2=Number(number2);if(number2===0){return 0}let width=newDeviceWidth||deviceWidth;let result=number2/BASE_DEVICE_WIDTH*width;if(result<0){result=-result}result=Math.floor(result+EPS);if(result===0){if(deviceDPR===1||!isIOS){result=1}else{result=.5}}return number2<0?-result:result}));const API_ADD_INTERCEPTOR="addInterceptor";const API_REMOVE_INTERCEPTOR="removeInterceptor";function mergeInterceptorHook(interceptors2,interceptor){Object.keys(interceptor).forEach((hook=>{if(isFunction$2(interceptor[hook])){interceptors2[hook]=mergeHook(interceptors2[hook],interceptor[hook])}}))}function removeInterceptorHook(interceptors2,interceptor){if(!interceptors2||!interceptor){return}Object.keys(interceptor).forEach((name=>{const hooks=interceptors2[name];const hook=interceptor[name];if(isArray$3(hooks)&&isFunction$2(hook)){remove(hooks,hook)}}))}function mergeHook(parentVal,childVal){const res=childVal?parentVal?parentVal.concat(childVal):isArray$3(childVal)?childVal:[childVal]:parentVal;return res?dedupeHooks(res):res}function dedupeHooks(hooks){const res=[];for(let i2=0;i2<hooks.length;i2++){if(res.indexOf(hooks[i2])===-1){res.push(hooks[i2])}}return res}const addInterceptor=defineSyncApi(API_ADD_INTERCEPTOR,((method,interceptor)=>{if(isString$2(method)&&isPlainObject$3(interceptor)){mergeInterceptorHook(scopedInterceptors[method]||(scopedInterceptors[method]={}),interceptor)}else if(isPlainObject$3(method)){mergeInterceptorHook(globalInterceptors,method)}}));const removeInterceptor=defineSyncApi(API_REMOVE_INTERCEPTOR,((method,interceptor)=>{if(isString$2(method)){if(isPlainObject$3(interceptor)){removeInterceptorHook(scopedInterceptors[method],interceptor)}else{delete scopedInterceptors[method]}}else if(isPlainObject$3(method)){removeInterceptorHook(globalInterceptors,method)}}));const interceptors={};const API_ON="$on";const API_ONCE="$once";const API_OFF="$off";const API_EMIT="$emit";const emitter=new E$1$1;const $on=defineSyncApi(API_ON,((name,callback)=>{emitter.on(name,callback);return()=>emitter.off(name,callback)}));const $once=defineSyncApi(API_ONCE,((name,callback)=>{emitter.once(name,callback);return()=>emitter.off(name,callback)}));const $off=defineSyncApi(API_OFF,((name,callback)=>{if(!name){emitter.e={};return}if(!isArray$3(name))name=[name];name.forEach((n2=>emitter.off(n2,callback)))}));const $emit=defineSyncApi(API_EMIT,((name,...args)=>{emitter.emit(name,...args)}));let cid;let cidErrMsg;let enabled;function normalizePushMessage(message){try{return JSON.parse(message)}catch(e2){}return message}function invokePushCallback(args){if(args.type==="enabled"){enabled=true}else if(args.type==="clientId"){cid=args.cid;cidErrMsg=args.errMsg;invokeGetPushCidCallbacks(cid,args.errMsg)}else if(args.type==="pushMsg"){const message={type:"receive",data:normalizePushMessage(args.message)};for(let i2=0;i2<onPushMessageCallbacks.length;i2++){const callback=onPushMessageCallbacks[i2];callback(message);if(message.stopped){break}}}else if(args.type==="click"){onPushMessageCallbacks.forEach((callback=>{callback({type:"click",data:normalizePushMessage(args.message)})}))}}const getPushCidCallbacks=[];function invokeGetPushCidCallbacks(cid2,errMsg){getPushCidCallbacks.forEach((callback=>{callback(cid2,errMsg)}));getPushCidCallbacks.length=0}const API_GET_PUSH_CLIENT_ID="getPushClientId";const getPushClientId=defineAsyncApi(API_GET_PUSH_CLIENT_ID,((_2,{resolve:resolve2,reject:reject})=>{Promise.resolve().then((()=>{if(typeof enabled==="undefined"){enabled=false;cid="";cidErrMsg="uniPush is not enabled"}getPushCidCallbacks.push(((cid2,errMsg)=>{if(cid2){resolve2({cid:cid2})}else{reject(errMsg)}}));if(typeof cid!=="undefined"){invokeGetPushCidCallbacks(cid,cidErrMsg)}}))}));const onPushMessageCallbacks=[];const onPushMessage=fn=>{if(onPushMessageCallbacks.indexOf(fn)===-1){onPushMessageCallbacks.push(fn)}};const offPushMessage=fn=>{if(!fn){onPushMessageCallbacks.length=0}else{const index2=onPushMessageCallbacks.indexOf(fn);if(index2>-1){onPushMessageCallbacks.splice(index2,1)}}};const SYNC_API_RE=/^\$|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/;const CONTEXT_API_RE=/^create|Manager$/;const CONTEXT_API_RE_EXC=["createBLEConnection"];const ASYNC_API=["createBLEConnection"];const CALLBACK_API_RE=/^on|^off/;function isContextApi(name){return CONTEXT_API_RE.test(name)&&CONTEXT_API_RE_EXC.indexOf(name)===-1}function isSyncApi(name){return SYNC_API_RE.test(name)&&ASYNC_API.indexOf(name)===-1}function isCallbackApi(name){return CALLBACK_API_RE.test(name)&&name!=="onPush"}function shouldPromise(name){if(isContextApi(name)||isSyncApi(name)||isCallbackApi(name)){return false}return true}if(!Promise.prototype.finally){Promise.prototype.finally=function(onfinally){const promise=this.constructor;return this.then((value=>promise.resolve(onfinally&&onfinally()).then((()=>value))),(reason=>promise.resolve(onfinally&&onfinally()).then((()=>{throw reason}))))}}function promisify(name,api){if(!shouldPromise(name)){return api}if(!isFunction$2(api)){return api}return function promiseApi(options={},...rest){if(isFunction$2(options.success)||isFunction$2(options.fail)||isFunction$2(options.complete)){return wrapperReturnValue(name,invokeApi(name,api,options,rest))}return wrapperReturnValue(name,handlePromise(new Promise(((resolve2,reject)=>{invokeApi(name,api,extend({},options,{success:resolve2,fail:reject}),rest)}))))}}const CALLBACKS=["success","fail","cancel","complete"];function initWrapper(protocols2){function processCallback(methodName,method,returnValue2){return function(res){return method(processReturnValue(methodName,res,returnValue2))}}function processArgs(methodName,fromArgs,argsOption={},returnValue2={},keepFromArgs=false){if(isPlainObject$3(fromArgs)){const toArgs=keepFromArgs===true?fromArgs:{};if(isFunction$2(argsOption)){argsOption=argsOption(fromArgs,toArgs)||{}}for(const key in fromArgs){if(hasOwn$3(argsOption,key)){let keyOption=argsOption[key];if(isFunction$2(keyOption)){keyOption=keyOption(fromArgs[key],fromArgs,toArgs)}if(!keyOption){console.warn(`支付宝小程序 ${methodName} 暂不支持 ${key}`)}else if(isString$2(keyOption)){toArgs[keyOption]=fromArgs[key]}else if(isPlainObject$3(keyOption)){toArgs[keyOption.name?keyOption.name:key]=keyOption.value}}else if(CALLBACKS.indexOf(key)!==-1){const callback=fromArgs[key];if(isFunction$2(callback)){toArgs[key]=processCallback(methodName,callback,returnValue2)}}else{if(!keepFromArgs&&!hasOwn$3(toArgs,key)){toArgs[key]=fromArgs[key]}}}return toArgs}else if(isFunction$2(fromArgs)){fromArgs=processCallback(methodName,fromArgs,returnValue2)}return fromArgs}function processReturnValue(methodName,res,returnValue2,keepReturnValue=false){if(isFunction$2(protocols2.returnValue)){res=protocols2.returnValue(methodName,res)}return processArgs(methodName,res,returnValue2,{},keepReturnValue)}return function wrapper(methodName,method){if(!hasOwn$3(protocols2,methodName)){return method}const protocol=protocols2[methodName];if(!protocol){return function(){console.error(`支付宝小程序 暂不支持${methodName}`)}}return function(arg1,arg2){let options=protocol;if(isFunction$2(protocol)){options=protocol(arg1)}arg1=processArgs(methodName,arg1,options.args,options.returnValue);const args=[arg1];if(typeof arg2!=="undefined"){args.push(arg2)}const returnValue2=my[options.name||methodName].apply(my,args);if(isSyncApi(methodName)){return processReturnValue(methodName,returnValue2,options.returnValue,isContextApi(methodName))}return returnValue2}}}const getLocale=()=>{const app=isFunction$2(getApp)&&getApp({allowDefault:true});if(app&&app.$vm){return app.$vm.$locale}return normalizeLocale(my.getSystemInfoSync().language)||LOCALE_EN};const setLocale=locale=>{const app=isFunction$2(getApp)&&getApp();if(!app){return false}const oldLocale=app.$vm.$locale;if(oldLocale!==locale){app.$vm.$locale=locale;onLocaleChangeCallbacks.forEach((fn=>fn({locale:locale})));return true}return false};const onLocaleChangeCallbacks=[];const onLocaleChange=fn=>{if(onLocaleChangeCallbacks.indexOf(fn)===-1){onLocaleChangeCallbacks.push(fn)}};if(typeof global!=="undefined"){global.getLocale=getLocale}const UUID_KEY="__DC_STAT_UUID";let deviceId;function useDeviceId(global2=my){return function addDeviceId(_2,toRes){deviceId=deviceId||global2.getStorageSync(UUID_KEY);if(!deviceId){deviceId=Date.now()+""+Math.floor(Math.random()*1e7);my.setStorage({key:UUID_KEY,data:deviceId})}toRes.deviceId=deviceId}}function addSafeAreaInsets(fromRes,toRes){if(fromRes.safeArea){const safeArea=fromRes.safeArea;toRes.safeAreaInsets={top:safeArea.top,left:safeArea.left,right:fromRes.windowWidth-safeArea.right,bottom:fromRes.screenHeight-safeArea.bottom}}}function populateParameters(fromRes,toRes){const{brand:brand="",model:model="",system:system="",language:language="",theme:theme,version:version2,platform:platform,fontSizeSetting:fontSizeSetting,SDKVersion:SDKVersion,pixelRatio:pixelRatio,deviceOrientation:deviceOrientation}=fromRes;let osName="";let osVersion="";{osName=platform;osVersion=system}let hostVersion=version2;let deviceType=getGetDeviceType(fromRes,model);let deviceBrand=getDeviceBrand(brand);let _hostName=getHostName(fromRes);let _deviceOrientation=deviceOrientation;let _devicePixelRatio=pixelRatio;let _SDKVersion=SDKVersion;{_SDKVersion=my.SDKVersion}const hostLanguage=language.replace(/_/g,"-");const parameters={appId:"",appName:"预约取号",appVersion:"1.0.0",appVersionCode:"100",appLanguage:getAppLanguage(hostLanguage),uniCompileVersion:"4.29",uniRuntimeVersion:"4.29",uniPlatform:"mp-alipay",deviceBrand:deviceBrand,deviceModel:model,deviceType:deviceType,devicePixelRatio:_devicePixelRatio,deviceOrientation:_deviceOrientation,osName:osName.toLocaleLowerCase(),osVersion:osVersion,hostTheme:theme,hostVersion:hostVersion,hostLanguage:hostLanguage,hostName:_hostName,hostSDKVersion:_SDKVersion,hostFontSizeSetting:fontSizeSetting,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0};extend(toRes,parameters)}function getGetDeviceType(fromRes,model){let deviceType=fromRes.deviceType||"phone";{const deviceTypeMaps={ipad:"pad",windows:"pc",mac:"pc"};const deviceTypeMapsKeys=Object.keys(deviceTypeMaps);const _model=model.toLocaleLowerCase();for(let index2=0;index2<deviceTypeMapsKeys.length;index2++){const _m=deviceTypeMapsKeys[index2];if(_model.indexOf(_m)!==-1){deviceType=deviceTypeMaps[_m];break}}}return deviceType}function getDeviceBrand(brand){let deviceBrand=brand;if(deviceBrand){deviceBrand=deviceBrand.toLocaleLowerCase()}return deviceBrand}function getAppLanguage(defaultLanguage){return getLocale?getLocale():defaultLanguage}function getHostName(fromRes){const _platform="mp-alipay".split("-")[1];let _hostName=fromRes.hostName||_platform;_hostName=fromRes.app;return _hostName}const redirectTo={};const eventChannels={};let id=0;function initEventChannel(events,cache2=true){id++;const eventChannel=new my.EventChannel(id,events);if(cache2){eventChannels[id]=eventChannel}return eventChannel}function getEventChannel(id2){const eventChannel=eventChannels[id2];delete eventChannels[id2];return eventChannel}const navigateTo$1=()=>{let eventChannel;return{args(fromArgs){eventChannel=initEventChannel(fromArgs.events);if(fromArgs.url){fromArgs.url=fromArgs.url+(fromArgs.url.indexOf("?")===-1?"?":"&")+"__id__="+eventChannel.id}},returnValue(fromRes){fromRes.eventChannel=eventChannel}}};const baseApis={$on:$on,$off:$off,$once:$once,$emit:$emit,upx2px:upx2px,interceptors:interceptors,addInterceptor:addInterceptor,removeInterceptor:removeInterceptor,onCreateVueApp:onCreateVueApp,invokeCreateVueAppHook:invokeCreateVueAppHook,getLocale:getLocale,setLocale:setLocale,onLocaleChange:onLocaleChange,getPushClientId:getPushClientId,onPushMessage:onPushMessage,offPushMessage:offPushMessage,invokePushCallback:invokePushCallback};function initUni(api,protocols2,platform=my){const wrapper=initWrapper(protocols2);const UniProxyHandlers={get(target,key){if(hasOwn$3(target,key)){return target[key]}if(hasOwn$3(api,key)){return promisify(key,api[key])}if(hasOwn$3(baseApis,key)){return promisify(key,baseApis[key])}return promisify(key,wrapper(key,platform[key]))}};{platform.$emit=$emit;if(!my.canIUse("getOpenerEventChannel"))platform.getEventChannel=getEventChannel}return new Proxy({},UniProxyHandlers)}function initGetProvider(providers){return function getProvider2({service:service,success:success,fail:fail,complete:complete}){let res;if(providers[service]){res={errMsg:"getProvider:ok",service:service,provider:providers[service]};isFunction$2(success)&&success(res)}else{res={errMsg:"getProvider:fail:服务["+service+"]不存在"};isFunction$2(fail)&&fail(res)}isFunction$2(complete)&&complete(res)}}let onKeyboardHeightChangeCallback;const getProvider=initGetProvider({oauth:["alipay"],share:["alipay"],payment:["alipay"],push:["alipay"]});function setStorageSync(key,data){return my.setStorageSync({key:key,data:data})}function getStorageSync(key){const result=my.getStorageSync({key:key});return result.data!==null?result.data:""}function removeStorageSync(key){return my.removeStorageSync({key:key})}function startGyroscope(args){if(hasOwn$3(args,"interval")){console.warn("支付宝小程序 startGyroscope暂不支持interval")}args.success&&args.success({errMsg:"startGyroscope:ok"});args.complete&&args.complete({errMsg:"startGyroscope:ok"})}function createExecCallback(execCallback){return function wrapperExecCallback(res){this.actions.forEach(((action,index2)=>{(action._$callbacks||[]).forEach((callback=>{callback(res[index2])}))}));if(isFunction$2(execCallback)){execCallback(res)}}}function addCallback(callback){if(isFunction$2(callback)){const action=this.actions[this.actions.length-1];if(action){(action._$callbacks||(action._$callbacks=[])).push(callback)}}}function createSelectorQuery(){const query=my.createSelectorQuery();const oldExec=query.exec;const oldScrollOffset=query.scrollOffset;const oldBoundingClientRect=query.boundingClientRect;query.exec=function exec(callback){return oldExec.call(this,createExecCallback(callback).bind(this))};query.scrollOffset=function scrollOffset(callback){const ret=oldScrollOffset.call(this);addCallback.call(this,callback);return ret};query.boundingClientRect=function boundingClientRect(callback){const ret=oldBoundingClientRect.call(this);addCallback.call(this,callback);return ret};if(!query.fields){query.fields=function({rect:rect,size:size2,scrollOffset:scrollOffset},callback){if(rect||size2){this.boundingClientRect()}if(scrollOffset){this.scrollOffset()}addCallback.call(this,callback);return this}}if(!query.in){query.in=function(){return this}}return query}function createIntersectionObserver(component,options){if(options&&options.observeAll){options.selectAll=options.observeAll;delete options.observeAll}return my.createIntersectionObserver(options)}function onKeyboardHeightChange(callback){if(onKeyboardHeightChangeCallback){$off("uni:keyboardHeightChange",onKeyboardHeightChangeCallback)}onKeyboardHeightChangeCallback=callback;$on("uni:keyboardHeightChange",onKeyboardHeightChangeCallback)}function offKeyboardHeightChange(){$off("uni:keyboardHeightChange",onKeyboardHeightChangeCallback);onKeyboardHeightChangeCallback=void 0}var shims=Object.freeze({__proto__:null,createIntersectionObserver:createIntersectionObserver,createSelectorQuery:createSelectorQuery,getProvider:getProvider,getStorageSync:getStorageSync,offKeyboardHeightChange:offKeyboardHeightChange,onKeyboardHeightChange:onKeyboardHeightChange,removeStorageSync:removeStorageSync,setStorageSync:setStorageSync,startGyroscope:startGyroscope});function handleNetworkInfo(fromRes,toRes){const nextworkType=fromRes.networkType;switch(nextworkType){case"NOTREACHABLE":toRes.networkType="none";break;case"WWAN":toRes.networkType="3g";break;default:toRes.networkType=fromRes.networkType.toLowerCase();break}}function reviseScreenSize(fromRes,toRes){if(fromRes.screen){toRes.screenWidth=fromRes.screen.width;toRes.screenHeight=fromRes.screen.height}}function handleSystemInfo(fromRes,toRes){reviseScreenSize(fromRes,toRes);addSafeAreaInsets(fromRes,toRes);useDeviceId({getStorageSync:getStorageSync})(fromRes,toRes);populateParameters(fromRes,toRes);let platform=fromRes.platform?fromRes.platform.toLowerCase():"devtools";if(my.canIUse("isIDE")){platform=my.isIDE?"devtools":platform}else{if(!~["android","ios"].indexOf(platform)){platform="devtools"}}toRes.platform=platform}function returnValue(methodName,res={}){if(res.error||res.errorMessage){res.errMsg=`${methodName}:fail ${res.errorMessage||res.error}`;delete res.error;delete res.errorMessage}else{res.errMsg=`${methodName}:ok`}return res}const request={name:my.canIUse("request")?"request":"httpRequest",args(fromArgs){const method=fromArgs.method||"GET";if(!fromArgs.header){fromArgs.header={}}const headers={"content-type":"application/json"};Object.keys(fromArgs.header).forEach((key=>{headers[key.toLocaleLowerCase()]=fromArgs.header[key]}));return{header(){return{name:"headers",value:headers}},data(data){if(my.canIUse("saveFileToDingTalk")&&method.toUpperCase()==="POST"&&headers["content-type"].indexOf("application/json")===0&&isPlainObject$3(data)){return{name:"data",value:JSON.stringify(data)}}return{name:"data",value:data}},method:"method",responseType:false}},returnValue:{status:"statusCode",headers:"header"}};const setNavigationBarColor={name:"setNavigationBar",args:{frontColor:false,animation:false}};const setNavigationBarTitle={name:"setNavigationBar"};function showModal({showCancel:showCancel=true}={}){if(showCancel){return{name:"confirm",args:{cancelColor:false,confirmColor:false,cancelText:"cancelButtonText",confirmText:"confirmButtonText"},returnValue(fromRes,toRes){toRes.confirm=fromRes.confirm;toRes.cancel=!fromRes.confirm}}}return{name:"alert",args:{confirmColor:false,confirmText:"buttonText"},returnValue(fromRes,toRes){toRes.confirm=true;toRes.cancel=false}}}function showToast({icon:icon="success"}={}){const args={title:"content",icon:"type",image:false,mask:false};if(icon==="loading"){return{name:"showLoading",args:args}}return{name:"showToast",args:args}}const showActionSheet={name:"showActionSheet",args:{itemList:"items",itemColor:false},returnValue:{index:"tapIndex"}};const showLoading={args:{title:"content"}};const uploadFile={args:{name:"fileName"}};const downloadFile={returnValue:{apFilePath:"tempFilePath"}};const getFileInfo={args:{filePath:"apFilePath"}};const compressImage={args(fromArgs,toArgs){toArgs.compressLevel=4;if(fromArgs&&fromArgs.quality){toArgs.compressLevel=Math.floor(fromArgs.quality/26)}if(fromArgs.src){toArgs.apFilePaths=[fromArgs.src]}},returnValue(fromRes,toRes){const apFilePaths=fromRes.apFilePaths;if(apFilePaths&&apFilePaths.length){toRes.tempFilePath=apFilePaths[0]}}};const chooseVideo={returnValue:{apFilePath:"tempFilePath"}};const connectSocket={args:{method:false,protocols:false}};const chooseImage={returnValue(result){var _a,_b;const hasTempFilePaths=hasOwn$3(result,"tempFilePaths")&&result.tempFilePaths;if(hasOwn$3(result,"apFilePaths")&&!hasTempFilePaths){result.tempFilePaths=[];(_a=result.apFilePaths)===null||_a===void 0?void 0:_a.forEach((apFilePath=>{var _a2;return(_a2=result.tempFilePaths)===null||_a2===void 0?void 0:_a2.push(apFilePath)}))}if(!hasOwn$3(result,"tempFiles")&&hasTempFilePaths){result.tempFiles=[];(_b=result.tempFilePaths)===null||_b===void 0?void 0:_b.forEach((tempFilePath=>{var _a2;return(_a2=result.tempFiles)===null||_a2===void 0?void 0:_a2.push({path:tempFilePath})}))}return{}}};const previewImage={args(fromArgs,toArgs){const currentIndex=Number(fromArgs.current);if(isNaN(currentIndex)){if(fromArgs.current&&isArray$3(fromArgs.urls)){const index2=fromArgs.urls.indexOf(fromArgs.current);toArgs.current=~index2?index2:0}}else{toArgs.current=currentIndex}return{indicator:false,loop:false}}};const saveFile={args:{tempFilePath:"apFilePath"},returnValue:{apFilePath:"savedFilePath"}};const getSavedFileInfo={args:{filePath:"apFilePath"}};const getSavedFileList={returnValue(fromRes,toRes){toRes.fileList=fromRes.fileList.map((file=>({filePath:file.apFilePath,createTime:file.createTime,size:file.size})))}};const removeSavedFile={args:{filePath:"apFilePath"}};const getLocation={args:{type:false,altitude:false}};const openLocation={args:{}};const getNetworkType={returnValue:handleNetworkInfo};const onNetworkStatusChange={returnValue:handleNetworkInfo};const stopAccelerometer={name:"offAccelerometerChange"};const stopCompass={name:"offCompassChange"};const scanCode={name:"scan",args:{onlyFromCamera:"hideAlbum"},returnValue:{code:"result"}};const setClipboardData={name:"setClipboard",args:{data:"text"}};const getClipboardData={name:"getClipboard",returnValue:{text:"data"}};const pageScrollTo={args:{duration:false}};const login={name:"getAuthCode",returnValue:{authCode:"code"}};const getUserInfo={name:my.canIUse("getOpenUserInfo")?"getOpenUserInfo":"getAuthUserInfo",returnValue(fromRes,toRes){if(my.canIUse("getOpenUserInfo")){let response;try{response=JSON.parse(fromRes.response).response}catch(e2){}if(response){toRes.userInfo=response;toRes.userInfo.avatarUrl=response.avatar;delete response.avatar}}else{toRes.userInfo={openId:"",nickName:fromRes.nickName,avatarUrl:fromRes.avatar}}}};const requestPayment={name:"tradePay",args:{orderInfo:"tradeNO"}};const getBLEDeviceServices={returnValue(fromRes,toRes){toRes.services=fromRes.services.map((item=>({uuid:item.serviceId,isPrimary:item.isPrimary})))}};const createBLEConnection={name:"connectBLEDevice",args:{timeout:false}};const closeBLEConnection={name:"disconnectBLEDevice"};const onBLEConnectionStateChange={name:"onBLEConnectionStateChanged"};const makePhoneCall={args:{phoneNumber:"number"}};const stopGyroscope={name:"offGyroscopeChange"};const getSystemInfo={returnValue:handleSystemInfo};const getSystemInfoSync={returnValue:handleSystemInfo};const canvasToTempFilePath={returnValue(fromRes,toRes){toRes.tempFilePath=fromRes.apFilePath}};const setScreenBrightness={args:{value:"brightness"}};const getScreenBrightness={returnValue:{brightness:"value"}};const showShareMenu={name:"showSharePanel"};const hideHomeButton={name:"hideBackHome"};const saveImageToPhotosAlbum=my.canIUse("saveImageToPhotosAlbum")?{}:{name:"saveImage",args:{filePath:"url"}};const saveVideoToPhotosAlbum={args:{filePath:"src"}};const chooseAddress={name:"getAddress",returnValue(fromRes,toRes){const info=fromRes.result||{};toRes.userName=info.fullname;toRes.countyName=info.country;toRes.provinceName=info.prov;toRes.cityName=info.city;toRes.detailInfo=info.address;toRes.telNumber=info.mobilePhone;toRes.errMsg=toRes.errMsg+" "+fromRes.resultStatus}};const navigateTo=my.canIUse("getOpenerEventChannel")?{}:navigateTo$1();var protocols=Object.freeze({__proto__:null,canvasToTempFilePath:canvasToTempFilePath,chooseAddress:chooseAddress,chooseImage:chooseImage,chooseVideo:chooseVideo,closeBLEConnection:closeBLEConnection,compressImage:compressImage,connectSocket:connectSocket,createBLEConnection:createBLEConnection,downloadFile:downloadFile,getBLEDeviceServices:getBLEDeviceServices,getClipboardData:getClipboardData,getFileInfo:getFileInfo,getLocation:getLocation,getNetworkType:getNetworkType,getSavedFileInfo:getSavedFileInfo,getSavedFileList:getSavedFileList,getScreenBrightness:getScreenBrightness,getSystemInfo:getSystemInfo,getSystemInfoSync:getSystemInfoSync,getUserInfo:getUserInfo,hideHomeButton:hideHomeButton,login:login,makePhoneCall:makePhoneCall,navigateTo:navigateTo,onBLEConnectionStateChange:onBLEConnectionStateChange,onNetworkStatusChange:onNetworkStatusChange,openLocation:openLocation,pageScrollTo:pageScrollTo,previewImage:previewImage,redirectTo:redirectTo,removeSavedFile:removeSavedFile,request:request,requestPayment:requestPayment,returnValue:returnValue,saveFile:saveFile,saveImageToPhotosAlbum:saveImageToPhotosAlbum,saveVideoToPhotosAlbum:saveVideoToPhotosAlbum,scanCode:scanCode,setClipboardData:setClipboardData,setNavigationBarColor:setNavigationBarColor,setNavigationBarTitle:setNavigationBarTitle,setScreenBrightness:setScreenBrightness,showActionSheet:showActionSheet,showLoading:showLoading,showModal:showModal,showShareMenu:showShareMenu,showToast:showToast,stopAccelerometer:stopAccelerometer,stopCompass:stopCompass,stopGyroscope:stopGyroscope,uploadFile:uploadFile});var index=initUni(shims,protocols);new Set(Object.getOwnPropertyNames(Symbol).filter((key=>key!=="arguments"&&key!=="caller")).map((key=>Symbol[key])).filter(isSymbol));{const g2=getGlobalThis();const registerGlobalSetter=(key,setter)=>{let setters;if(!(setters=g2[key]))setters=g2[key]=[];setters.push(setter);return v2=>{if(setters.length>1)setters.forEach((set2=>set2(v2)));else setters[0](v2)}};registerGlobalSetter(`__VUE_INSTANCE_SETTERS__`,(v2=>v2));registerGlobalSetter(`__VUE_SSR_SETTERS__`,(v2=>v2))}let activeEffectScope;class EffectScope{constructor(detached=false){this.detached=detached;this._active=true;this.effects=[];this.cleanups=[];this.parent=activeEffectScope;if(!detached&&activeEffectScope){this.index=(activeEffectScope.scopes||(activeEffectScope.scopes=[])).push(this)-1}}get active(){return this._active}run(fn){if(this._active){const currentEffectScope=activeEffectScope;try{activeEffectScope=this;return fn()}finally{activeEffectScope=currentEffectScope}}}on(){activeEffectScope=this}off(){activeEffectScope=this.parent}stop(fromParent){if(this._active){let i2,l2;for(i2=0,l2=this.effects.length;i2<l2;i2++){this.effects[i2].stop()}for(i2=0,l2=this.cleanups.length;i2<l2;i2++){this.cleanups[i2]()}if(this.scopes){for(i2=0,l2=this.scopes.length;i2<l2;i2++){this.scopes[i2].stop(true)}}if(!this.detached&&this.parent&&!fromParent){const last=this.parent.scopes.pop();if(last&&last!==this){this.parent.scopes[this.index]=last;last.index=this.index}}this.parent=void 0;this._active=false}}}function effectScope(detached){return new EffectScope(detached)}function recordEffectScope(effect2,scope=activeEffectScope){if(scope&&scope.active){scope.effects.push(effect2)}}function getCurrentScope(){return activeEffectScope}function onScopeDispose(fn){if(activeEffectScope){activeEffectScope.cleanups.push(fn)}}let activeEffect;class ReactiveEffect2{constructor(fn,trigger2,scheduler,scope){this.fn=fn;this.trigger=trigger2;this.scheduler=scheduler;this.active=true;this.deps=[];this._dirtyLevel=4;this._trackId=0;this._runnings=0;this._shouldSchedule=false;this._depsLength=0;recordEffectScope(this,scope)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1;pauseTracking();for(let i2=0;i2<this._depsLength;i2++){const dep=this.deps[i2];if(dep.computed){triggerComputed(dep.computed);if(this._dirtyLevel>=4){break}}}if(this._dirtyLevel===1){this._dirtyLevel=0}resetTracking()}return this._dirtyLevel>=4}set dirty(v2){this._dirtyLevel=v2?4:0}run(){this._dirtyLevel=0;if(!this.active){return this.fn()}let lastShouldTrack=shouldTrack;let lastEffect=activeEffect;try{shouldTrack=true;activeEffect=this;this._runnings++;preCleanupEffect(this);return this.fn()}finally{postCleanupEffect(this);this._runnings--;activeEffect=lastEffect;shouldTrack=lastShouldTrack}}stop(){var _a;if(this.active){preCleanupEffect(this);postCleanupEffect(this);(_a=this.onStop)==null?void 0:_a.call(this);this.active=false}}}function triggerComputed(computed2){return computed2.value}function preCleanupEffect(effect2){effect2._trackId++;effect2._depsLength=0}function postCleanupEffect(effect2){if(effect2.deps.length>effect2._depsLength){for(let i2=effect2._depsLength;i2<effect2.deps.length;i2++){cleanupDepEffect(effect2.deps[i2],effect2)}effect2.deps.length=effect2._depsLength}}function cleanupDepEffect(dep,effect2){const trackId=dep.get(effect2);if(trackId!==void 0&&effect2._trackId!==trackId){dep.delete(effect2);if(dep.size===0){dep.cleanup()}}}let shouldTrack=true;let pauseScheduleStack=0;const trackStack=[];function pauseTracking(){trackStack.push(shouldTrack);shouldTrack=false}function resetTracking(){const last=trackStack.pop();shouldTrack=last===void 0?true:last}function pauseScheduling(){pauseScheduleStack++}function resetScheduling(){pauseScheduleStack--;while(!pauseScheduleStack&&queueEffectSchedulers.length){queueEffectSchedulers.shift()()}}function trackEffect(effect2,dep,debuggerEventExtraInfo){if(dep.get(effect2)!==effect2._trackId){dep.set(effect2,effect2._trackId);const oldDep=effect2.deps[effect2._depsLength];if(oldDep!==dep){if(oldDep){cleanupDepEffect(oldDep,effect2)}effect2.deps[effect2._depsLength++]=dep}else{effect2._depsLength++}}}const queueEffectSchedulers=[];function triggerEffects(dep,dirtyLevel,debuggerEventExtraInfo){pauseScheduling();for(const effect2 of dep.keys()){let tracking;if(effect2._dirtyLevel<dirtyLevel&&(tracking!=null?tracking:tracking=dep.get(effect2)===effect2._trackId)){effect2._shouldSchedule||(effect2._shouldSchedule=effect2._dirtyLevel===0);effect2._dirtyLevel=dirtyLevel}if(effect2._shouldSchedule&&(tracking!=null?tracking:tracking=dep.get(effect2)===effect2._trackId)){effect2.trigger();if((!effect2._runnings||effect2.allowRecurse)&&effect2._dirtyLevel!==2){effect2._shouldSchedule=false;if(effect2.scheduler){queueEffectSchedulers.push(effect2.scheduler)}}}}resetScheduling()}const createDep=(cleanup,computed2)=>{const dep=new Map;dep.cleanup=cleanup;dep.computed=computed2;return dep};const targetMap=new WeakMap;const ITERATE_KEY=Symbol("");const MAP_KEY_ITERATE_KEY=Symbol("");function track(target,type,key){if(shouldTrack&&activeEffect){let depsMap=targetMap.get(target);if(!depsMap){targetMap.set(target,depsMap=new Map)}let dep=depsMap.get(key);if(!dep){depsMap.set(key,dep=createDep((()=>depsMap.delete(key))))}trackEffect(activeEffect,dep)}}function trigger(target,type,key,newValue,oldValue,oldTarget){const depsMap=targetMap.get(target);if(!depsMap){return}let deps=[];if(type==="clear"){deps=[...depsMap.values()]}else if(key==="length"&&isArray$3(target)){const newLength=Number(newValue);depsMap.forEach(((dep,key2)=>{if(key2==="length"||!isSymbol(key2)&&key2>=newLength){deps.push(dep)}}))}else{if(key!==void 0){deps.push(depsMap.get(key))}switch(type){case"add":if(!isArray$3(target)){deps.push(depsMap.get(ITERATE_KEY));if(isMap(target)){deps.push(depsMap.get(MAP_KEY_ITERATE_KEY))}}else if(isIntegerKey(key)){deps.push(depsMap.get("length"))}break;case"delete":if(!isArray$3(target)){deps.push(depsMap.get(ITERATE_KEY));if(isMap(target)){deps.push(depsMap.get(MAP_KEY_ITERATE_KEY))}}break;case"set":if(isMap(target)){deps.push(depsMap.get(ITERATE_KEY))}break}}pauseScheduling();for(const dep of deps){if(dep){triggerEffects(dep,4)}}resetScheduling()}function getDepFromReactive(object,key){var _a;return(_a=targetMap.get(object))==null?void 0:_a.get(key)}const isNonTrackableKeys=makeMap(`__proto__,__v_isRef,__isVue`);const builtInSymbols=new Set(Object.getOwnPropertyNames(Symbol).filter((key=>key!=="arguments"&&key!=="caller")).map((key=>Symbol[key])).filter(isSymbol));const arrayInstrumentations=createArrayInstrumentations();function createArrayInstrumentations(){const instrumentations={};["includes","indexOf","lastIndexOf"].forEach((key=>{instrumentations[key]=function(...args){const arr=toRaw(this);for(let i2=0,l2=this.length;i2<l2;i2++){track(arr,"get",i2+"")}const res=arr[key](...args);if(res===-1||res===false){return arr[key](...args.map(toRaw))}else{return res}}}));["push","pop","shift","unshift","splice"].forEach((key=>{instrumentations[key]=function(...args){pauseTracking();pauseScheduling();const res=toRaw(this)[key].apply(this,args);resetScheduling();resetTracking();return res}}));return instrumentations}function hasOwnProperty$2(key){const obj=toRaw(this);track(obj,"has",key);return obj.hasOwnProperty(key)}class BaseReactiveHandler2{constructor(_isReadonly=false,_isShallow=false){this._isReadonly=_isReadonly;this._isShallow=_isShallow}get(target,key,receiver){const isReadonly2=this._isReadonly,isShallow2=this._isShallow;if(key==="__v_isReactive"){return!isReadonly2}else if(key==="__v_isReadonly"){return isReadonly2}else if(key==="__v_isShallow"){return isShallow2}else if(key==="__v_raw"){if(receiver===(isReadonly2?isShallow2?shallowReadonlyMap:readonlyMap:isShallow2?shallowReactiveMap:reactiveMap).get(target)||Object.getPrototypeOf(target)===Object.getPrototypeOf(receiver)){return target}return}const targetIsArray=isArray$3(target);if(!isReadonly2){if(targetIsArray&&hasOwn$3(arrayInstrumentations,key)){return Reflect.get(arrayInstrumentations,key,receiver)}if(key==="hasOwnProperty"){return hasOwnProperty$2}}const res=Reflect.get(target,key,receiver);if(isSymbol(key)?builtInSymbols.has(key):isNonTrackableKeys(key)){return res}if(!isReadonly2){track(target,"get",key)}if(isShallow2){return res}if(isRef(res)){return targetIsArray&&isIntegerKey(key)?res:res.value}if(isObject$4(res)){return isReadonly2?readonly(res):reactive(res)}return res}}class MutableReactiveHandler2 extends BaseReactiveHandler2{constructor(isShallow2=false){super(false,isShallow2)}set(target,key,value,receiver){let oldValue=target[key];if(!this._isShallow){const isOldValueReadonly=isReadonly(oldValue);if(!isShallow(value)&&!isReadonly(value)){oldValue=toRaw(oldValue);value=toRaw(value)}if(!isArray$3(target)&&isRef(oldValue)&&!isRef(value)){if(isOldValueReadonly){return false}else{oldValue.value=value;return true}}}const hadKey=isArray$3(target)&&isIntegerKey(key)?Number(key)<target.length:hasOwn$3(target,key);const result=Reflect.set(target,key,value,receiver);if(target===toRaw(receiver)){if(!hadKey){trigger(target,"add",key,value)}else if(hasChanged(value,oldValue)){trigger(target,"set",key,value)}}return result}deleteProperty(target,key){const hadKey=hasOwn$3(target,key);target[key];const result=Reflect.deleteProperty(target,key);if(result&&hadKey){trigger(target,"delete",key,void 0)}return result}has(target,key){const result=Reflect.has(target,key);if(!isSymbol(key)||!builtInSymbols.has(key)){track(target,"has",key)}return result}ownKeys(target){track(target,"iterate",isArray$3(target)?"length":ITERATE_KEY);return Reflect.ownKeys(target)}}class ReadonlyReactiveHandler2 extends BaseReactiveHandler2{constructor(isShallow2=false){super(true,isShallow2)}set(target,key){return true}deleteProperty(target,key){return true}}const mutableHandlers=new MutableReactiveHandler2;const readonlyHandlers=new ReadonlyReactiveHandler2;const shallowReactiveHandlers=new MutableReactiveHandler2(true);const toShallow=value=>value;const getProto=v2=>Reflect.getPrototypeOf(v2);function get(target,key,isReadonly2=false,isShallow2=false){target=target["__v_raw"];const rawTarget=toRaw(target);const rawKey=toRaw(key);if(!isReadonly2){if(hasChanged(key,rawKey)){track(rawTarget,"get",key)}track(rawTarget,"get",rawKey)}const{has:has2}=getProto(rawTarget);const wrap=isShallow2?toShallow:isReadonly2?toReadonly:toReactive;if(has2.call(rawTarget,key)){return wrap(target.get(key))}else if(has2.call(rawTarget,rawKey)){return wrap(target.get(rawKey))}else if(target!==rawTarget){target.get(key)}}function has(key,isReadonly2=false){const target=this["__v_raw"];const rawTarget=toRaw(target);const rawKey=toRaw(key);if(!isReadonly2){if(hasChanged(key,rawKey)){track(rawTarget,"has",key)}track(rawTarget,"has",rawKey)}return key===rawKey?target.has(key):target.has(key)||target.has(rawKey)}function size(target,isReadonly2=false){target=target["__v_raw"];!isReadonly2&&track(toRaw(target),"iterate",ITERATE_KEY);return Reflect.get(target,"size",target)}function add(value){value=toRaw(value);const target=toRaw(this);const proto=getProto(target);const hadKey=proto.has.call(target,value);if(!hadKey){target.add(value);trigger(target,"add",value,value)}return this}function set$1(key,value){value=toRaw(value);const target=toRaw(this);const{has:has2,get:get2}=getProto(target);let hadKey=has2.call(target,key);if(!hadKey){key=toRaw(key);hadKey=has2.call(target,key)}const oldValue=get2.call(target,key);target.set(key,value);if(!hadKey){trigger(target,"add",key,value)}else if(hasChanged(value,oldValue)){trigger(target,"set",key,value)}return this}function deleteEntry(key){const target=toRaw(this);const{has:has2,get:get2}=getProto(target);let hadKey=has2.call(target,key);if(!hadKey){key=toRaw(key);hadKey=has2.call(target,key)}get2?get2.call(target,key):void 0;const result=target.delete(key);if(hadKey){trigger(target,"delete",key,void 0)}return result}function clear(){const target=toRaw(this);const hadItems=target.size!==0;const result=target.clear();if(hadItems){trigger(target,"clear",void 0,void 0)}return result}function createForEach(isReadonly2,isShallow2){return function forEach(callback,thisArg){const observed=this;const target=observed["__v_raw"];const rawTarget=toRaw(target);const wrap=isShallow2?toShallow:isReadonly2?toReadonly:toReactive;!isReadonly2&&track(rawTarget,"iterate",ITERATE_KEY);return target.forEach(((value,key)=>callback.call(thisArg,wrap(value),wrap(key),observed)))}}function createIterableMethod(method,isReadonly2,isShallow2){return function(...args){const target=this["__v_raw"];const rawTarget=toRaw(target);const targetIsMap=isMap(rawTarget);const isPair=method==="entries"||method===Symbol.iterator&&targetIsMap;const isKeyOnly=method==="keys"&&targetIsMap;const innerIterator=target[method](...args);const wrap=isShallow2?toShallow:isReadonly2?toReadonly:toReactive;!isReadonly2&&track(rawTarget,"iterate",isKeyOnly?MAP_KEY_ITERATE_KEY:ITERATE_KEY);return{next(){const{value:value,done:done}=innerIterator.next();return done?{value:value,done:done}:{value:isPair?[wrap(value[0]),wrap(value[1])]:wrap(value),done:done}},[Symbol.iterator](){return this}}}}function createReadonlyMethod(type){return function(...args){return type==="delete"?false:type==="clear"?void 0:this}}function createInstrumentations(){const mutableInstrumentations2={get(key){return get(this,key)},get size(){return size(this)},has:has,add:add,set:set$1,delete:deleteEntry,clear:clear,forEach:createForEach(false,false)};const shallowInstrumentations2={get(key){return get(this,key,false,true)},get size(){return size(this)},has:has,add:add,set:set$1,delete:deleteEntry,clear:clear,forEach:createForEach(false,true)};const readonlyInstrumentations2={get(key){return get(this,key,true)},get size(){return size(this,true)},has(key){return has.call(this,key,true)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(true,false)};const shallowReadonlyInstrumentations2={get(key){return get(this,key,true,true)},get size(){return size(this,true)},has(key){return has.call(this,key,true)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(true,true)};const iteratorMethods=["keys","values","entries",Symbol.iterator];iteratorMethods.forEach((method=>{mutableInstrumentations2[method]=createIterableMethod(method,false,false);readonlyInstrumentations2[method]=createIterableMethod(method,true,false);shallowInstrumentations2[method]=createIterableMethod(method,false,true);shallowReadonlyInstrumentations2[method]=createIterableMethod(method,true,true)}));return[mutableInstrumentations2,readonlyInstrumentations2,shallowInstrumentations2,shallowReadonlyInstrumentations2]}const[mutableInstrumentations,readonlyInstrumentations,shallowInstrumentations,shallowReadonlyInstrumentations]=createInstrumentations();function createInstrumentationGetter(isReadonly2,shallow){const instrumentations=shallow?isReadonly2?shallowReadonlyInstrumentations:shallowInstrumentations:isReadonly2?readonlyInstrumentations:mutableInstrumentations;return(target,key,receiver)=>{if(key==="__v_isReactive"){return!isReadonly2}else if(key==="__v_isReadonly"){return isReadonly2}else if(key==="__v_raw"){return target}return Reflect.get(hasOwn$3(instrumentations,key)&&key in target?instrumentations:target,key,receiver)}}const mutableCollectionHandlers={get:createInstrumentationGetter(false,false)};const shallowCollectionHandlers={get:createInstrumentationGetter(false,true)};const readonlyCollectionHandlers={get:createInstrumentationGetter(true,false)};const reactiveMap=new WeakMap;const shallowReactiveMap=new WeakMap;const readonlyMap=new WeakMap;const shallowReadonlyMap=new WeakMap;function targetTypeMap(rawType){switch(rawType){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function getTargetType(value){return value["__v_skip"]||!Object.isExtensible(value)?0:targetTypeMap(toRawType(value))}function reactive(target){if(isReadonly(target)){return target}return createReactiveObject(target,false,mutableHandlers,mutableCollectionHandlers,reactiveMap)}function shallowReactive(target){return createReactiveObject(target,false,shallowReactiveHandlers,shallowCollectionHandlers,shallowReactiveMap)}function readonly(target){return createReactiveObject(target,true,readonlyHandlers,readonlyCollectionHandlers,readonlyMap)}function createReactiveObject(target,isReadonly2,baseHandlers,collectionHandlers,proxyMap){if(!isObject$4(target)){return target}if(target["__v_raw"]&&!(isReadonly2&&target["__v_isReactive"])){return target}const existingProxy=proxyMap.get(target);if(existingProxy){return existingProxy}const targetType=getTargetType(target);if(targetType===0){return target}const proxy=new Proxy(target,targetType===2?collectionHandlers:baseHandlers);proxyMap.set(target,proxy);return proxy}function isReactive(value){if(isReadonly(value)){return isReactive(value["__v_raw"])}return!!(value&&value["__v_isReactive"])}function isReadonly(value){return!!(value&&value["__v_isReadonly"])}function isShallow(value){return!!(value&&value["__v_isShallow"])}function isProxy(value){return isReactive(value)||isReadonly(value)}function toRaw(observed){const raw=observed&&observed["__v_raw"];return raw?toRaw(raw):observed}function markRaw(value){if(Object.isExtensible(value)){def(value,"__v_skip",true)}return value}const toReactive=value=>isObject$4(value)?reactive(value):value;const toReadonly=value=>isObject$4(value)?readonly(value):value;class ComputedRefImpl{constructor(getter,_setter,isReadonly2,isSSR){this.getter=getter;this._setter=_setter;this.dep=void 0;this.__v_isRef=true;this["__v_isReadonly"]=false;this.effect=new ReactiveEffect2((()=>getter(this._value)),(()=>triggerRefValue(this,this.effect._dirtyLevel===2?2:3)));this.effect.computed=this;this.effect.active=this._cacheable=!isSSR;this["__v_isReadonly"]=isReadonly2}get value(){const self2=toRaw(this);if((!self2._cacheable||self2.effect.dirty)&&hasChanged(self2._value,self2._value=self2.effect.run())){triggerRefValue(self2,4)}trackRefValue(self2);if(self2.effect._dirtyLevel>=2){triggerRefValue(self2,2)}return self2._value}set value(newValue){this._setter(newValue)}get _dirty(){return this.effect.dirty}set _dirty(v2){this.effect.dirty=v2}}function computed$1(getterOrOptions,debugOptions,isSSR=false){let getter;let setter;const onlyGetter=isFunction$2(getterOrOptions);if(onlyGetter){getter=getterOrOptions;setter=NOOP}else{getter=getterOrOptions.get;setter=getterOrOptions.set}const cRef=new ComputedRefImpl(getter,setter,onlyGetter||!setter,isSSR);return cRef}function trackRefValue(ref2){var _a;if(shouldTrack&&activeEffect){ref2=toRaw(ref2);trackEffect(activeEffect,(_a=ref2.dep)!=null?_a:ref2.dep=createDep((()=>ref2.dep=void 0),ref2 instanceof ComputedRefImpl?ref2:void 0))}}function triggerRefValue(ref2,dirtyLevel=4,newVal){ref2=toRaw(ref2);const dep=ref2.dep;if(dep){triggerEffects(dep,dirtyLevel)}}function isRef(r2){return!!(r2&&r2.__v_isRef===true)}function ref(value){return createRef(value,false)}function createRef(rawValue,shallow){if(isRef(rawValue)){return rawValue}return new RefImpl(rawValue,shallow)}class RefImpl{constructor(value,__v_isShallow){this.__v_isShallow=__v_isShallow;this.dep=void 0;this.__v_isRef=true;this._rawValue=__v_isShallow?value:toRaw(value);this._value=__v_isShallow?value:toReactive(value)}get value(){trackRefValue(this);return this._value}set value(newVal){const useDirectValue=this.__v_isShallow||isShallow(newVal)||isReadonly(newVal);newVal=useDirectValue?newVal:toRaw(newVal);if(hasChanged(newVal,this._rawValue)){this._rawValue=newVal;this._value=useDirectValue?newVal:toReactive(newVal);triggerRefValue(this,4)}}}function unref(ref2){return isRef(ref2)?ref2.value:ref2}const shallowUnwrapHandlers={get:(target,key,receiver)=>unref(Reflect.get(target,key,receiver)),set:(target,key,value,receiver)=>{const oldValue=target[key];if(isRef(oldValue)&&!isRef(value)){oldValue.value=value;return true}else{return Reflect.set(target,key,value,receiver)}}};function proxyRefs(objectWithRefs){return isReactive(objectWithRefs)?objectWithRefs:new Proxy(objectWithRefs,shallowUnwrapHandlers)}function toRefs(object){const ret=isArray$3(object)?new Array(object.length):{};for(const key in object){ret[key]=propertyToRef(object,key)}return ret}class ObjectRefImpl{constructor(_object,_key,_defaultValue){this._object=_object;this._key=_key;this._defaultValue=_defaultValue;this.__v_isRef=true}get value(){const val=this._object[this._key];return val===void 0?this._defaultValue:val}set value(newVal){this._object[this._key]=newVal}get dep(){return getDepFromReactive(toRaw(this._object),this._key)}}class GetterRefImpl{constructor(_getter){this._getter=_getter;this.__v_isRef=true;this.__v_isReadonly=true}get value(){return this._getter()}}function toRef(source,key,defaultValue){if(isRef(source)){return source}else if(isFunction$2(source)){return new GetterRefImpl(source)}else if(isObject$4(source)&&arguments.length>1){return propertyToRef(source,key,defaultValue)}else{return ref(source)}}function propertyToRef(source,key,defaultValue){const val=source[key];return isRef(val)?val:new ObjectRefImpl(source,key,defaultValue)}function callWithErrorHandling(fn,instance,type,args){try{return args?fn(...args):fn()}catch(err){handleError(err,instance,type)}}function callWithAsyncErrorHandling(fn,instance,type,args){if(isFunction$2(fn)){const res=callWithErrorHandling(fn,instance,type,args);if(res&&isPromise(res)){res.catch((err=>{handleError(err,instance,type)}))}return res}const values=[];for(let i2=0;i2<fn.length;i2++){values.push(callWithAsyncErrorHandling(fn[i2],instance,type,args))}return values}function handleError(err,instance,type,throwInDev=true){const contextVNode=instance?instance.vnode:null;if(instance){let cur=instance.parent;const exposedInstance=instance.proxy;const errorInfo=`https://vuejs.org/error-reference/#runtime-${type}`;while(cur){const errorCapturedHooks=cur.ec;if(errorCapturedHooks){for(let i2=0;i2<errorCapturedHooks.length;i2++){if(errorCapturedHooks[i2](err,exposedInstance,errorInfo)===false){return}}}cur=cur.parent}const appErrorHandler=instance.appContext.config.errorHandler;if(appErrorHandler){callWithErrorHandling(appErrorHandler,null,10,[err,exposedInstance,errorInfo]);return}}logError(err,type,contextVNode,throwInDev)}function logError(err,type,contextVNode,throwInDev=true){{console.error(err)}}let isFlushing=false;let isFlushPending=false;const queue=[];let flushIndex=0;const pendingPostFlushCbs=[];let activePostFlushCbs=null;let postFlushIndex=0;const resolvedPromise=Promise.resolve();let currentFlushPromise=null;function nextTick$1(fn){const p2=currentFlushPromise||resolvedPromise;return fn?p2.then(this?fn.bind(this):fn):p2}function findInsertionIndex(id2){let start=flushIndex+1;let end=queue.length;while(start<end){const middle=start+end>>>1;const middleJob=queue[middle];const middleJobId=getId(middleJob);if(middleJobId<id2||middleJobId===id2&&middleJob.pre){start=middle+1}else{end=middle}}return start}function queueJob(job){if(!queue.length||!queue.includes(job,isFlushing&&job.allowRecurse?flushIndex+1:flushIndex)){if(job.id==null){queue.push(job)}else{queue.splice(findInsertionIndex(job.id),0,job)}queueFlush()}}function queueFlush(){if(!isFlushing&&!isFlushPending){isFlushPending=true;currentFlushPromise=resolvedPromise.then(flushJobs)}}function hasQueueJob(job){return queue.indexOf(job)>-1}function invalidateJob(job){const i2=queue.indexOf(job);if(i2>flushIndex){queue.splice(i2,1)}}function queuePostFlushCb(cb){if(!isArray$3(cb)){if(!activePostFlushCbs||!activePostFlushCbs.includes(cb,cb.allowRecurse?postFlushIndex+1:postFlushIndex)){pendingPostFlushCbs.push(cb)}}else{pendingPostFlushCbs.push(...cb)}queueFlush()}function flushPreFlushCbs(instance,seen,i2=(isFlushing?flushIndex+1:0)){for(;i2<queue.length;i2++){const cb=queue[i2];if(cb&&cb.pre){queue.splice(i2,1);i2--;cb()}}}function flushPostFlushCbs(seen){if(pendingPostFlushCbs.length){const deduped=[...new Set(pendingPostFlushCbs)].sort(((a2,b2)=>getId(a2)-getId(b2)));pendingPostFlushCbs.length=0;if(activePostFlushCbs){activePostFlushCbs.push(...deduped);return}activePostFlushCbs=deduped;for(postFlushIndex=0;postFlushIndex<activePostFlushCbs.length;postFlushIndex++){activePostFlushCbs[postFlushIndex]()}activePostFlushCbs=null;postFlushIndex=0}}const getId=job=>job.id==null?Infinity:job.id;const comparator=(a2,b2)=>{const diff2=getId(a2)-getId(b2);if(diff2===0){if(a2.pre&&!b2.pre)return-1;if(b2.pre&&!a2.pre)return 1}return diff2};function flushJobs(seen){isFlushPending=false;isFlushing=true;queue.sort(comparator);try{for(flushIndex=0;flushIndex<queue.length;flushIndex++){const job=queue[flushIndex];if(job&&job.active!==false){if(false);callWithErrorHandling(job,null,14)}}}finally{flushIndex=0;queue.length=0;flushPostFlushCbs();isFlushing=false;currentFlushPromise=null;if(queue.length||pendingPostFlushCbs.length){flushJobs()}}}function emit(instance,event,...rawArgs){if(instance.isUnmounted)return;const props=instance.vnode.props||EMPTY_OBJ;let args=rawArgs;const isModelListener2=event.startsWith("update:");const modelArg=isModelListener2&&event.slice(7);if(modelArg&&modelArg in props){const modifiersKey=`${modelArg==="modelValue"?"model":modelArg}Modifiers`;const{number:number2,trim:trim}=props[modifiersKey]||EMPTY_OBJ;if(trim){args=rawArgs.map((a2=>isString$2(a2)?a2.trim():a2))}if(number2){args=rawArgs.map(looseToNumber)}}let handlerName;let handler=props[handlerName=toHandlerKey(event)]||props[handlerName=toHandlerKey(camelize(event))];if(!handler&&isModelListener2){handler=props[handlerName=toHandlerKey(hyphenate(event))]}if(handler){callWithAsyncErrorHandling(handler,instance,6,args)}const onceHandler=props[handlerName+`Once`];if(onceHandler){if(!instance.emitted){instance.emitted={}}else if(instance.emitted[handlerName]){return}instance.emitted[handlerName]=true;callWithAsyncErrorHandling(onceHandler,instance,6,args)}}function normalizeEmitsOptions(comp,appContext,asMixin=false){const cache2=appContext.emitsCache;const cached=cache2.get(comp);if(cached!==void 0){return cached}const raw=comp.emits;let normalized={};let hasExtends=false;if(!isFunction$2(comp)){const extendEmits=raw2=>{const normalizedFromExtend=normalizeEmitsOptions(raw2,appContext,true);if(normalizedFromExtend){hasExtends=true;extend(normalized,normalizedFromExtend)}};if(!asMixin&&appContext.mixins.length){appContext.mixins.forEach(extendEmits)}if(comp.extends){extendEmits(comp.extends)}if(comp.mixins){comp.mixins.forEach(extendEmits)}}if(!raw&&!hasExtends){if(isObject$4(comp)){cache2.set(comp,null)}return null}if(isArray$3(raw)){raw.forEach((key=>normalized[key]=null))}else{extend(normalized,raw)}if(isObject$4(comp)){cache2.set(comp,normalized)}return normalized}function isEmitListener(options,key){if(!options||!isOn(key)){return false}key=key.slice(2).replace(/Once$/,"");return hasOwn$3(options,key[0].toLowerCase()+key.slice(1))||hasOwn$3(options,hyphenate(key))||hasOwn$3(options,key)}let currentRenderingInstance=null;function setCurrentRenderingInstance(instance){const prev=currentRenderingInstance;currentRenderingInstance=instance;instance&&instance.type.__scopeId||null;return prev}const COMPONENTS="components";function resolveComponent(name,maybeSelfReference){return resolveAsset(COMPONENTS,name,true,maybeSelfReference)||name}function resolveAsset(type,name,warnMissing=true,maybeSelfReference=false){const instance=currentRenderingInstance||currentInstance;if(instance){const Component2=instance.type;{const selfName=getComponentName(Component2,false);if(selfName&&(selfName===name||selfName===camelize(name)||selfName===capitalize(camelize(name)))){return Component2}}const res=resolve(instance[type]||Component2[type],name)||resolve(instance.appContext[type],name);if(!res&&maybeSelfReference){return Component2}return res}}function resolve(registry,name){return registry&&(registry[name]||registry[camelize(name)]||registry[capitalize(camelize(name))])}const INITIAL_WATCHER_VALUE={};function watch(source,cb,options){return doWatch(source,cb,options)}function doWatch(source,cb,{immediate:immediate,deep:deep,flush:flush,once:once2,onTrack:onTrack,onTrigger:onTrigger}=EMPTY_OBJ){if(cb&&once2){const _cb=cb;cb=(...args)=>{_cb(...args);unwatch()}}const instance=currentInstance;const reactiveGetter=source2=>deep===true?source2:traverse(source2,deep===false?1:void 0);let getter;let forceTrigger=false;let isMultiSource=false;if(isRef(source)){getter=()=>source.value;forceTrigger=isShallow(source)}else if(isReactive(source)){getter=()=>reactiveGetter(source);forceTrigger=true}else if(isArray$3(source)){isMultiSource=true;forceTrigger=source.some((s2=>isReactive(s2)||isShallow(s2)));getter=()=>source.map((s2=>{if(isRef(s2)){return s2.value}else if(isReactive(s2)){return reactiveGetter(s2)}else if(isFunction$2(s2)){return callWithErrorHandling(s2,instance,2)}else;}))}else if(isFunction$2(source)){if(cb){getter=()=>callWithErrorHandling(source,instance,2)}else{getter=()=>{if(cleanup){cleanup()}return callWithAsyncErrorHandling(source,instance,3,[onCleanup])}}}else{getter=NOOP}if(cb&&deep){const baseGetter=getter;getter=()=>traverse(baseGetter())}let cleanup;let onCleanup=fn=>{cleanup=effect2.onStop=()=>{callWithErrorHandling(fn,instance,4);cleanup=effect2.onStop=void 0}};let oldValue=isMultiSource?new Array(source.length).fill(INITIAL_WATCHER_VALUE):INITIAL_WATCHER_VALUE;const job=()=>{if(!effect2.active||!effect2.dirty){return}if(cb){const newValue=effect2.run();if(deep||forceTrigger||(isMultiSource?newValue.some(((v2,i2)=>hasChanged(v2,oldValue[i2]))):hasChanged(newValue,oldValue))||false){if(cleanup){cleanup()}callWithAsyncErrorHandling(cb,instance,3,[newValue,oldValue===INITIAL_WATCHER_VALUE?void 0:isMultiSource&&oldValue[0]===INITIAL_WATCHER_VALUE?[]:oldValue,onCleanup]);oldValue=newValue}}else{effect2.run()}};job.allowRecurse=!!cb;let scheduler;if(flush==="sync"){scheduler=job}else if(flush==="post"){scheduler=()=>queuePostRenderEffect$1(job,instance&&instance.suspense)}else{job.pre=true;if(instance)job.id=instance.uid;scheduler=()=>queueJob(job)}const effect2=new ReactiveEffect2(getter,NOOP,scheduler);const scope=getCurrentScope();const unwatch=()=>{effect2.stop();if(scope){remove(scope.effects,effect2)}};if(cb){if(immediate){job()}else{oldValue=effect2.run()}}else if(flush==="post"){queuePostRenderEffect$1(effect2.run.bind(effect2),instance&&instance.suspense)}else{effect2.run()}return unwatch}function instanceWatch(source,value,options){const publicThis=this.proxy;const getter=isString$2(source)?source.includes(".")?createPathGetter(publicThis,source):()=>publicThis[source]:source.bind(publicThis,publicThis);let cb;if(isFunction$2(value)){cb=value}else{cb=value.handler;options=value}const reset=setCurrentInstance(this);const res=doWatch(getter,cb.bind(publicThis),options);reset();return res}function createPathGetter(ctx,path){const segments=path.split(".");return()=>{let cur=ctx;for(let i2=0;i2<segments.length&&cur;i2++){cur=cur[segments[i2]]}return cur}}function traverse(value,depth,currentDepth=0,seen){if(!isObject$4(value)||value["__v_skip"]){return value}if(depth&&depth>0){if(currentDepth>=depth){return value}currentDepth++}seen=seen||new Set;if(seen.has(value)){return value}seen.add(value);if(isRef(value)){traverse(value.value,depth,currentDepth,seen)}else if(isArray$3(value)){for(let i2=0;i2<value.length;i2++){traverse(value[i2],depth,currentDepth,seen)}}else if(isSet(value)||isMap(value)){value.forEach((v2=>{traverse(v2,depth,currentDepth,seen)}))}else if(isPlainObject$3(value)){for(const key in value){traverse(value[key],depth,currentDepth,seen)}}return value}function createAppContext(){return{app:null,config:{isNativeTag:NO,performance:false,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let uid$1=0;function createAppAPI(render,hydrate){return function createApp2(rootComponent,rootProps=null){if(!isFunction$2(rootComponent)){rootComponent=extend({},rootComponent)}if(rootProps!=null&&!isObject$4(rootProps)){rootProps=null}const context=createAppContext();const installedPlugins=new WeakSet;const app=context.app={_uid:uid$1++,_component:rootComponent,_props:rootProps,_container:null,_context:context,_instance:null,version:version,get config(){return context.config},set config(v2){},use(plugin2,...options){if(installedPlugins.has(plugin2));else if(plugin2&&isFunction$2(plugin2.install)){installedPlugins.add(plugin2);plugin2.install(app,...options)}else if(isFunction$2(plugin2)){installedPlugins.add(plugin2);plugin2(app,...options)}else;return app},mixin(mixin){{if(!context.mixins.includes(mixin)){context.mixins.push(mixin)}}return app},component(name,component){if(!component){return context.components[name]}context.components[name]=component;return app},directive(name,directive){if(!directive){return context.directives[name]}context.directives[name]=directive;return app},mount(){},unmount(){},provide(key,value){context.provides[key]=value;return app},runWithContext(fn){const lastApp=currentApp;currentApp=app;try{return fn()}finally{currentApp=lastApp}}};return app}}let currentApp=null;function provide(key,value){if(!currentInstance);else{let provides=currentInstance.provides;const parentProvides=currentInstance.parent&&currentInstance.parent.provides;if(parentProvides===provides){provides=currentInstance.provides=Object.create(parentProvides)}provides[key]=value;if(currentInstance.type.mpType==="app"){currentInstance.appContext.app.provide(key,value)}}}function inject(key,defaultValue,treatDefaultAsFactory=false){const instance=currentInstance||currentRenderingInstance;if(instance||currentApp){const provides=instance?instance.parent==null?instance.vnode.appContext&&instance.vnode.appContext.provides:instance.parent.provides:currentApp._context.provides;if(provides&&key in provides){return provides[key]}else if(arguments.length>1){return treatDefaultAsFactory&&isFunction$2(defaultValue)?defaultValue.call(instance&&instance.proxy):defaultValue}else;}}function hasInjectionContext(){return!!(currentInstance||currentRenderingInstance||currentApp)}const isKeepAlive=vnode=>vnode.type.__isKeepAlive;function onActivated(hook,target){registerKeepAliveHook(hook,"a",target)}function onDeactivated(hook,target){registerKeepAliveHook(hook,"da",target)}function registerKeepAliveHook(hook,type,target=currentInstance){const wrappedHook=hook.__wdc||(hook.__wdc=()=>{let current=target;while(current){if(current.isDeactivated){return}current=current.parent}return hook()});injectHook(type,wrappedHook,target);if(target){let current=target.parent;while(current&&current.parent){if(isKeepAlive(current.parent.vnode)){injectToKeepAliveRoot(wrappedHook,type,target,current)}current=current.parent}}}function injectToKeepAliveRoot(hook,type,target,keepAliveRoot){const injected=injectHook(type,hook,keepAliveRoot,true);onUnmounted((()=>{remove(keepAliveRoot[type],injected)}),target)}function injectHook(type,hook,target=currentInstance,prepend=false){if(target){if(isRootHook(type)){target=target.root}const hooks=target[type]||(target[type]=[]);const wrappedHook=hook.__weh||(hook.__weh=(...args)=>{if(target.isUnmounted){return}pauseTracking();const reset=setCurrentInstance(target);const res=callWithAsyncErrorHandling(hook,target,type,args);reset();resetTracking();return res});if(prepend){hooks.unshift(wrappedHook)}else{hooks.push(wrappedHook)}return wrappedHook}}const createHook=lifecycle=>(hook,target=currentInstance)=>(!isInSSRComponentSetup||lifecycle==="sp")&&injectHook(lifecycle,((...args)=>hook(...args)),target);const onBeforeMount=createHook("bm");const onMounted=createHook("m");const onBeforeUpdate=createHook("bu");const onUpdated=createHook("u");const onBeforeUnmount=createHook("bum");const onUnmounted=createHook("um");const onServerPrefetch=createHook("sp");const onRenderTriggered=createHook("rtg");const onRenderTracked=createHook("rtc");function onErrorCaptured(hook,target=currentInstance){injectHook("ec",hook,target)}const getPublicInstance=i2=>{if(!i2)return null;if(isStatefulComponent(i2))return getExposeProxy(i2)||i2.proxy;return getPublicInstance(i2.parent)};const publicPropertiesMap=extend(Object.create(null),{$:i2=>i2,$el:i2=>i2.__$el||(i2.__$el={}),$data:i2=>i2.data,$props:i2=>i2.props,$attrs:i2=>i2.attrs,$slots:i2=>i2.slots,$refs:i2=>i2.refs,$parent:i2=>getPublicInstance(i2.parent),$root:i2=>getPublicInstance(i2.root),$emit:i2=>i2.emit,$options:i2=>resolveMergedOptions(i2),$forceUpdate:i2=>i2.f||(i2.f=()=>{i2.effect.dirty=true;queueJob(i2.update)}),$watch:i2=>instanceWatch.bind(i2)});const hasSetupBinding=(state,key)=>state!==EMPTY_OBJ&&!state.__isScriptSetup&&hasOwn$3(state,key);const PublicInstanceProxyHandlers={get({_:instance},key){const{ctx:ctx,setupState:setupState,data:data,props:props,accessCache:accessCache,type:type,appContext:appContext}=instance;let normalizedProps;if(key[0]!=="$"){const n2=accessCache[key];if(n2!==void 0){switch(n2){case 1:return setupState[key];case 2:return data[key];case 4:return ctx[key];case 3:return props[key]}}else if(hasSetupBinding(setupState,key)){accessCache[key]=1;return setupState[key]}else if(data!==EMPTY_OBJ&&hasOwn$3(data,key)){accessCache[key]=2;return data[key]}else if((normalizedProps=instance.propsOptions[0])&&hasOwn$3(normalizedProps,key)){accessCache[key]=3;return props[key]}else if(ctx!==EMPTY_OBJ&&hasOwn$3(ctx,key)){accessCache[key]=4;return ctx[key]}else if(shouldCacheAccess){accessCache[key]=0}}const publicGetter=publicPropertiesMap[key];let cssModule,globalProperties;if(publicGetter){if(key==="$attrs"){track(instance,"get",key)}return publicGetter(instance)}else if((cssModule=type.__cssModules)&&(cssModule=cssModule[key])){return cssModule}else if(ctx!==EMPTY_OBJ&&hasOwn$3(ctx,key)){accessCache[key]=4;return ctx[key]}else if(globalProperties=appContext.config.globalProperties,hasOwn$3(globalProperties,key)){{return globalProperties[key]}}else;},set({_:instance},key,value){const{data:data,setupState:setupState,ctx:ctx}=instance;if(hasSetupBinding(setupState,key)){setupState[key]=value;return true}else if(data!==EMPTY_OBJ&&hasOwn$3(data,key)){data[key]=value;return true}else if(hasOwn$3(instance.props,key)){return false}if(key[0]==="$"&&key.slice(1)in instance){return false}else{{ctx[key]=value}}return true},has({_:{data:data,setupState:setupState,accessCache:accessCache,ctx:ctx,appContext:appContext,propsOptions:propsOptions}},key){let normalizedProps;return!!accessCache[key]||data!==EMPTY_OBJ&&hasOwn$3(data,key)||hasSetupBinding(setupState,key)||(normalizedProps=propsOptions[0])&&hasOwn$3(normalizedProps,key)||hasOwn$3(ctx,key)||hasOwn$3(publicPropertiesMap,key)||hasOwn$3(appContext.config.globalProperties,key)},defineProperty(target,key,descriptor){if(descriptor.get!=null){target._.accessCache[key]=0}else if(hasOwn$3(descriptor,"value")){this.set(target,key,descriptor.value,null)}return Reflect.defineProperty(target,key,descriptor)}};function normalizePropsOrEmits(props){return isArray$3(props)?props.reduce(((normalized,p2)=>(normalized[p2]=null,normalized)),{}):props}let shouldCacheAccess=true;function applyOptions$1(instance){const options=resolveMergedOptions(instance);const publicThis=instance.proxy;const ctx=instance.ctx;shouldCacheAccess=false;if(options.beforeCreate){callHook$1(options.beforeCreate,instance,"bc")}const{data:dataOptions,computed:computedOptions,methods:methods,watch:watchOptions,provide:provideOptions,inject:injectOptions,created:created,beforeMount:beforeMount,mounted:mounted,beforeUpdate:beforeUpdate,updated:updated,activated:activated,deactivated:deactivated,beforeDestroy:beforeDestroy,beforeUnmount:beforeUnmount,destroyed:destroyed,unmounted:unmounted,render:render,renderTracked:renderTracked,renderTriggered:renderTriggered,errorCaptured:errorCaptured,serverPrefetch:serverPrefetch,expose:expose,inheritAttrs:inheritAttrs,components:components,directives:directives,filters:filters}=options;const checkDuplicateProperties=null;if(injectOptions){resolveInjections(injectOptions,ctx,checkDuplicateProperties)}if(methods){for(const key in methods){const methodHandler=methods[key];if(isFunction$2(methodHandler)){{ctx[key]=methodHandler.bind(publicThis)}}}}if(dataOptions){const data=dataOptions.call(publicThis,publicThis);if(!isObject$4(data));else{instance.data=reactive(data)}}shouldCacheAccess=true;if(computedOptions){for(const key in computedOptions){const opt=computedOptions[key];const get2=isFunction$2(opt)?opt.bind(publicThis,publicThis):isFunction$2(opt.get)?opt.get.bind(publicThis,publicThis):NOOP;const set2=!isFunction$2(opt)&&isFunction$2(opt.set)?opt.set.bind(publicThis):NOOP;const c2=computed({get:get2,set:set2});Object.defineProperty(ctx,key,{enumerable:true,configurable:true,get:()=>c2.value,set:v2=>c2.value=v2})}}if(watchOptions){for(const key in watchOptions){createWatcher(watchOptions[key],ctx,publicThis,key)}}{if(provideOptions){const provides=isFunction$2(provideOptions)?provideOptions.call(publicThis):provideOptions;Reflect.ownKeys(provides).forEach((key=>{provide(key,provides[key])}))}}{if(created){callHook$1(created,instance,"c")}}function registerLifecycleHook(register,hook){if(isArray$3(hook)){hook.forEach((_hook=>register(_hook.bind(publicThis))))}else if(hook){register(hook.bind(publicThis))}}registerLifecycleHook(onBeforeMount,beforeMount);registerLifecycleHook(onMounted,mounted);registerLifecycleHook(onBeforeUpdate,beforeUpdate);registerLifecycleHook(onUpdated,updated);registerLifecycleHook(onActivated,activated);registerLifecycleHook(onDeactivated,deactivated);registerLifecycleHook(onErrorCaptured,errorCaptured);registerLifecycleHook(onRenderTracked,renderTracked);registerLifecycleHook(onRenderTriggered,renderTriggered);registerLifecycleHook(onBeforeUnmount,beforeUnmount);registerLifecycleHook(onUnmounted,unmounted);registerLifecycleHook(onServerPrefetch,serverPrefetch);if(isArray$3(expose)){if(expose.length){const exposed=instance.exposed||(instance.exposed={});expose.forEach((key=>{Object.defineProperty(exposed,key,{get:()=>publicThis[key],set:val=>publicThis[key]=val})}))}else if(!instance.exposed){instance.exposed={}}}if(render&&instance.render===NOOP){instance.render=render}if(inheritAttrs!=null){instance.inheritAttrs=inheritAttrs}if(components)instance.components=components;if(directives)instance.directives=directives;if(instance.ctx.$onApplyOptions){instance.ctx.$onApplyOptions(options,instance,publicThis)}}function resolveInjections(injectOptions,ctx,checkDuplicateProperties=NOOP){if(isArray$3(injectOptions)){injectOptions=normalizeInject(injectOptions)}for(const key in injectOptions){const opt=injectOptions[key];let injected;if(isObject$4(opt)){if("default"in opt){injected=inject(opt.from||key,opt.default,true)}else{injected=inject(opt.from||key)}}else{injected=inject(opt)}if(isRef(injected)){Object.defineProperty(ctx,key,{enumerable:true,configurable:true,get:()=>injected.value,set:v2=>injected.value=v2})}else{ctx[key]=injected}}}function callHook$1(hook,instance,type){callWithAsyncErrorHandling(isArray$3(hook)?hook.map((h2=>h2.bind(instance.proxy))):hook.bind(instance.proxy),instance,type)}function createWatcher(raw,ctx,publicThis,key){const getter=key.includes(".")?createPathGetter(publicThis,key):()=>publicThis[key];if(isString$2(raw)){const handler=ctx[raw];if(isFunction$2(handler)){watch(getter,handler)}}else if(isFunction$2(raw)){watch(getter,raw.bind(publicThis))}else if(isObject$4(raw)){if(isArray$3(raw)){raw.forEach((r2=>createWatcher(r2,ctx,publicThis,key)))}else{const handler=isFunction$2(raw.handler)?raw.handler.bind(publicThis):ctx[raw.handler];if(isFunction$2(handler)){watch(getter,handler,raw)}}}else;}function resolveMergedOptions(instance){const base=instance.type;const{mixins:mixins,extends:extendsOptions}=base;const{mixins:globalMixins,optionsCache:cache2,config:{optionMergeStrategies:optionMergeStrategies}}=instance.appContext;const cached=cache2.get(base);let resolved;if(cached){resolved=cached}else if(!globalMixins.length&&!mixins&&!extendsOptions){{resolved=base}}else{resolved={};if(globalMixins.length){globalMixins.forEach((m2=>mergeOptions(resolved,m2,optionMergeStrategies,true)))}mergeOptions(resolved,base,optionMergeStrategies)}if(isObject$4(base)){cache2.set(base,resolved)}return resolved}function mergeOptions(to,from,strats,asMixin=false){const{mixins:mixins,extends:extendsOptions}=from;if(extendsOptions){mergeOptions(to,extendsOptions,strats,true)}if(mixins){mixins.forEach((m2=>mergeOptions(to,m2,strats,true)))}for(const key in from){if(asMixin&&key==="expose");else{const strat=internalOptionMergeStrats[key]||strats&&strats[key];to[key]=strat?strat(to[key],from[key]):from[key]}}return to}const internalOptionMergeStrats={data:mergeDataFn,props:mergeEmitsOrPropsOptions,emits:mergeEmitsOrPropsOptions,methods:mergeObjectOptions,computed:mergeObjectOptions,beforeCreate:mergeAsArray$1,created:mergeAsArray$1,beforeMount:mergeAsArray$1,mounted:mergeAsArray$1,beforeUpdate:mergeAsArray$1,updated:mergeAsArray$1,beforeDestroy:mergeAsArray$1,beforeUnmount:mergeAsArray$1,destroyed:mergeAsArray$1,unmounted:mergeAsArray$1,activated:mergeAsArray$1,deactivated:mergeAsArray$1,errorCaptured:mergeAsArray$1,serverPrefetch:mergeAsArray$1,components:mergeObjectOptions,directives:mergeObjectOptions,watch:mergeWatchOptions,provide:mergeDataFn,inject:mergeInject};function mergeDataFn(to,from){if(!from){return to}if(!to){return from}return function mergedDataFn(){return extend(isFunction$2(to)?to.call(this,this):to,isFunction$2(from)?from.call(this,this):from)}}function mergeInject(to,from){return mergeObjectOptions(normalizeInject(to),normalizeInject(from))}function normalizeInject(raw){if(isArray$3(raw)){const res={};for(let i2=0;i2<raw.length;i2++){res[raw[i2]]=raw[i2]}return res}return raw}function mergeAsArray$1(to,from){return to?[...new Set([].concat(to,from))]:from}function mergeObjectOptions(to,from){return to?extend(Object.create(null),to,from):from}function mergeEmitsOrPropsOptions(to,from){if(to){if(isArray$3(to)&&isArray$3(from)){return[...new Set([...to,...from])]}return extend(Object.create(null),normalizePropsOrEmits(to),normalizePropsOrEmits(from!=null?from:{}))}else{return from}}function mergeWatchOptions(to,from){if(!to)return from;if(!from)return to;const merged=extend(Object.create(null),to);for(const key in from){merged[key]=mergeAsArray$1(to[key],from[key])}return merged}function initProps$1(instance,rawProps,isStateful,isSSR=false){const props={};const attrs={};instance.propsDefaults=Object.create(null);setFullProps(instance,rawProps,props,attrs);for(const key in instance.propsOptions[0]){if(!(key in props)){props[key]=void 0}}if(isStateful){instance.props=isSSR?props:shallowReactive(props)}else{if(!instance.type.props){instance.props=attrs}else{instance.props=props}}instance.attrs=attrs}function updateProps(instance,rawProps,rawPrevProps,optimized){const{props:props,attrs:attrs,vnode:{patchFlag:patchFlag}}=instance;const rawCurrentProps=toRaw(props);const[options]=instance.propsOptions;let hasAttrsChanged=false;if(patchFlag>0&&!(patchFlag&16)){if(patchFlag&8){const propsToUpdate=instance.vnode.dynamicProps;for(let i2=0;i2<propsToUpdate.length;i2++){let key=propsToUpdate[i2];if(isEmitListener(instance.emitsOptions,key)){continue}const value=rawProps[key];if(options){if(hasOwn$3(attrs,key)){if(value!==attrs[key]){attrs[key]=value;hasAttrsChanged=true}}else{const camelizedKey=camelize(key);props[camelizedKey]=resolvePropValue(options,rawCurrentProps,camelizedKey,value,instance,false)}}else{if(value!==attrs[key]){attrs[key]=value;hasAttrsChanged=true}}}}}else{if(setFullProps(instance,rawProps,props,attrs)){hasAttrsChanged=true}let kebabKey;for(const key in rawCurrentProps){if(!rawProps||!hasOwn$3(rawProps,key)&&((kebabKey=hyphenate(key))===key||!hasOwn$3(rawProps,kebabKey))){if(options){if(rawPrevProps&&(rawPrevProps[key]!==void 0||rawPrevProps[kebabKey]!==void 0)){props[key]=resolvePropValue(options,rawCurrentProps,key,void 0,instance,true)}}else{delete props[key]}}}if(attrs!==rawCurrentProps){for(const key in attrs){if(!rawProps||!hasOwn$3(rawProps,key)&&true){delete attrs[key];hasAttrsChanged=true}}}}if(hasAttrsChanged){trigger(instance,"set","$attrs")}}function setFullProps(instance,rawProps,props,attrs){const[options,needCastKeys]=instance.propsOptions;let hasAttrsChanged=false;let rawCastValues;if(rawProps){for(let key in rawProps){if(isReservedProp(key)){continue}const value=rawProps[key];let camelKey;if(options&&hasOwn$3(options,camelKey=camelize(key))){if(!needCastKeys||!needCastKeys.includes(camelKey)){props[camelKey]=value}else{(rawCastValues||(rawCastValues={}))[camelKey]=value}}else if(!isEmitListener(instance.emitsOptions,key)){if(!(key in attrs)||value!==attrs[key]){attrs[key]=value;hasAttrsChanged=true}}}}if(needCastKeys){const rawCurrentProps=toRaw(props);const castValues=rawCastValues||EMPTY_OBJ;for(let i2=0;i2<needCastKeys.length;i2++){const key=needCastKeys[i2];props[key]=resolvePropValue(options,rawCurrentProps,key,castValues[key],instance,!hasOwn$3(castValues,key))}}return hasAttrsChanged}function resolvePropValue(options,props,key,value,instance,isAbsent){const opt=options[key];if(opt!=null){const hasDefault=hasOwn$3(opt,"default");if(hasDefault&&value===void 0){const defaultValue=opt.default;if(opt.type!==Function&&!opt.skipFactory&&isFunction$2(defaultValue)){const{propsDefaults:propsDefaults}=instance;if(key in propsDefaults){value=propsDefaults[key]}else{const reset=setCurrentInstance(instance);value=propsDefaults[key]=defaultValue.call(null,props);reset()}}else{value=defaultValue}}if(opt[0]){if(isAbsent&&!hasDefault){value=false}else if(opt[1]&&(value===""||value===hyphenate(key))){value=true}}}return value}function normalizePropsOptions(comp,appContext,asMixin=false){const cache2=appContext.propsCache;const cached=cache2.get(comp);if(cached){return cached}const raw=comp.props;const normalized={};const needCastKeys=[];let hasExtends=false;if(!isFunction$2(comp)){const extendProps=raw2=>{hasExtends=true;const[props,keys]=normalizePropsOptions(raw2,appContext,true);extend(normalized,props);if(keys)needCastKeys.push(...keys)};if(!asMixin&&appContext.mixins.length){appContext.mixins.forEach(extendProps)}if(comp.extends){extendProps(comp.extends)}if(comp.mixins){comp.mixins.forEach(extendProps)}}if(!raw&&!hasExtends){if(isObject$4(comp)){cache2.set(comp,EMPTY_ARR)}return EMPTY_ARR}if(isArray$3(raw)){for(let i2=0;i2<raw.length;i2++){const normalizedKey=camelize(raw[i2]);if(validatePropName(normalizedKey)){normalized[normalizedKey]=EMPTY_OBJ}}}else if(raw){for(const key in raw){const normalizedKey=camelize(key);if(validatePropName(normalizedKey)){const opt=raw[key];const prop=normalized[normalizedKey]=isArray$3(opt)||isFunction$2(opt)?{type:opt}:extend({},opt);if(prop){const booleanIndex=getTypeIndex(Boolean,prop.type);const stringIndex=getTypeIndex(String,prop.type);prop[0]=booleanIndex>-1;prop[1]=stringIndex<0||booleanIndex<stringIndex;if(booleanIndex>-1||hasOwn$3(prop,"default")){needCastKeys.push(normalizedKey)}}}}}const res=[normalized,needCastKeys];if(isObject$4(comp)){cache2.set(comp,res)}return res}function validatePropName(key){if(key[0]!=="$"&&!isReservedProp(key)){return true}return false}function getType(ctor){if(ctor===null){return"null"}if(typeof ctor==="function"){return ctor.name||""}else if(typeof ctor==="object"){const name=ctor.constructor&&ctor.constructor.name;return name||""}return""}function isSameType(a2,b2){return getType(a2)===getType(b2)}function getTypeIndex(type,expectedTypes){if(isArray$3(expectedTypes)){return expectedTypes.findIndex((t2=>isSameType(t2,type)))}else if(isFunction$2(expectedTypes)){return isSameType(expectedTypes,type)?0:-1}return-1}const queuePostRenderEffect$1=queuePostFlushCb;const Fragment=Symbol.for("v-fgt");const InternalObjectKey=`__vInternal`;function guardReactiveProps(props){if(!props)return null;return isProxy(props)||InternalObjectKey in props?extend({},props):props}const emptyAppContext=createAppContext();let uid=0;function createComponentInstance(vnode,parent,suspense){const type=vnode.type;const appContext=(parent?parent.appContext:vnode.appContext)||emptyAppContext;const instance={uid:uid++,vnode:vnode,type:type,parent:parent,appContext:appContext,root:null,next:null,subTree:null,effect:null,update:null,scope:new EffectScope(true),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:parent?parent.provides:Object.create(appContext.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:normalizePropsOptions(type,appContext),emitsOptions:normalizeEmitsOptions(type,appContext),emit:null,emitted:null,propsDefaults:EMPTY_OBJ,inheritAttrs:type.inheritAttrs,ctx:EMPTY_OBJ,data:EMPTY_OBJ,props:EMPTY_OBJ,attrs:EMPTY_OBJ,slots:EMPTY_OBJ,refs:EMPTY_OBJ,setupState:EMPTY_OBJ,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:suspense,suspenseId:suspense?suspense.pendingId:0,asyncDep:null,asyncResolved:false,isMounted:false,isUnmounted:false,isDeactivated:false,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};{instance.ctx={_:instance}}instance.root=parent?parent.root:instance;instance.emit=emit.bind(null,instance);if(vnode.ce){vnode.ce(instance)}return instance}let currentInstance=null;const getCurrentInstance=()=>currentInstance||currentRenderingInstance;let internalSetCurrentInstance;let setInSSRSetupState;{internalSetCurrentInstance=i2=>{currentInstance=i2};setInSSRSetupState=v2=>{isInSSRComponentSetup=v2}}const setCurrentInstance=instance=>{const prev=currentInstance;internalSetCurrentInstance(instance);instance.scope.on();return()=>{instance.scope.off();internalSetCurrentInstance(prev)}};const unsetCurrentInstance=()=>{currentInstance&&currentInstance.scope.off();internalSetCurrentInstance(null)};function isStatefulComponent(instance){return instance.vnode.shapeFlag&4}let isInSSRComponentSetup=false;function setupComponent(instance,isSSR=false){isSSR&&setInSSRSetupState(isSSR);const{props:props}=instance.vnode;const isStateful=isStatefulComponent(instance);initProps$1(instance,props,isStateful,isSSR);const setupResult=isStateful?setupStatefulComponent(instance):void 0;isSSR&&setInSSRSetupState(false);return setupResult}function setupStatefulComponent(instance,isSSR){const Component2=instance.type;instance.accessCache=Object.create(null);instance.proxy=markRaw(new Proxy(instance.ctx,PublicInstanceProxyHandlers));const{setup:setup}=Component2;if(setup){const setupContext=instance.setupContext=setup.length>1?createSetupContext(instance):null;const reset=setCurrentInstance(instance);pauseTracking();const setupResult=callWithErrorHandling(setup,instance,0,[instance.props,setupContext]);resetTracking();reset();if(isPromise(setupResult)){setupResult.then(unsetCurrentInstance,unsetCurrentInstance)}else{handleSetupResult(instance,setupResult)}}else{finishComponentSetup(instance)}}function handleSetupResult(instance,setupResult,isSSR){if(isFunction$2(setupResult)){{instance.render=setupResult}}else if(isObject$4(setupResult)){instance.setupState=proxyRefs(setupResult)}else;finishComponentSetup(instance)}function finishComponentSetup(instance,isSSR,skipOptions){const Component2=instance.type;if(!instance.render){instance.render=Component2.render||NOOP}{const reset=setCurrentInstance(instance);pauseTracking();try{applyOptions$1(instance)}finally{resetTracking();reset()}}}function getAttrsProxy(instance){return instance.attrsProxy||(instance.attrsProxy=new Proxy(instance.attrs,{get(target,key){track(instance,"get","$attrs");return target[key]}}))}function createSetupContext(instance){const expose=exposed=>{instance.exposed=exposed||{}};{return{get attrs(){return getAttrsProxy(instance)},slots:instance.slots,emit:instance.emit,expose:expose}}}function getExposeProxy(instance){if(instance.exposed){return instance.exposeProxy||(instance.exposeProxy=new Proxy(proxyRefs(markRaw(instance.exposed)),{get(target,key){if(key in target){return target[key]}return instance.proxy[key]},has(target,key){return key in target||key in publicPropertiesMap}}))}}function getComponentName(Component2,includeInferred=true){return isFunction$2(Component2)?Component2.displayName||Component2.name:Component2.name||includeInferred&&Component2.__name}const computed=(getterOrOptions,debugOptions)=>{const c2=computed$1(getterOrOptions,debugOptions,isInSSRComponentSetup);return c2};const version="3.4.21";function unwrapper(target){return unref(target)}const ARRAYTYPE="[object Array]";const OBJECTTYPE="[object Object]";function diff(current,pre){const result={};syncKeys(current,pre);_diff(current,pre,"",result);return result}function syncKeys(current,pre){current=unwrapper(current);if(current===pre)return;const rootCurrentType=toTypeString$2(current);const rootPreType=toTypeString$2(pre);if(rootCurrentType==OBJECTTYPE&&rootPreType==OBJECTTYPE){for(let key in pre){const currentValue=current[key];if(currentValue===void 0){current[key]=null}else{syncKeys(currentValue,pre[key])}}}else if(rootCurrentType==ARRAYTYPE&&rootPreType==ARRAYTYPE){if(current.length>=pre.length){pre.forEach(((item,index2)=>{syncKeys(current[index2],item)}))}}}function _diff(current,pre,path,result){current=unwrapper(current);if(current===pre)return;const rootCurrentType=toTypeString$2(current);const rootPreType=toTypeString$2(pre);if(rootCurrentType==OBJECTTYPE){if(rootPreType!=OBJECTTYPE||Object.keys(current).length<Object.keys(pre).length){setResult(result,path,current)}else{for(let key in current){const currentValue=unwrapper(current[key]);const preValue=pre[key];const currentType=toTypeString$2(currentValue);const preType=toTypeString$2(preValue);if(currentType!=ARRAYTYPE&&currentType!=OBJECTTYPE){if(currentValue!=preValue){setResult(result,(path==""?"":path+".")+key,currentValue)}}else if(currentType==ARRAYTYPE){if(preType!=ARRAYTYPE){setResult(result,(path==""?"":path+".")+key,currentValue)}else{if(currentValue.length<preValue.length){setResult(result,(path==""?"":path+".")+key,currentValue)}else{currentValue.forEach(((item,index2)=>{_diff(item,preValue[index2],(path==""?"":path+".")+key+"["+index2+"]",result)}))}}}else if(currentType==OBJECTTYPE){if(preType!=OBJECTTYPE||Object.keys(currentValue).length<Object.keys(preValue).length){setResult(result,(path==""?"":path+".")+key,currentValue)}else{for(let subKey in currentValue){_diff(currentValue[subKey],preValue[subKey],(path==""?"":path+".")+key+"."+subKey,result)}}}}}}else if(rootCurrentType==ARRAYTYPE){if(rootPreType!=ARRAYTYPE){setResult(result,path,current)}else{if(current.length<pre.length){setResult(result,path,current)}else{current.forEach(((item,index2)=>{_diff(item,pre[index2],path+"["+index2+"]",result)}))}}}else{setResult(result,path,current)}}function setResult(result,k2,v2){result[k2]=v2}function hasComponentEffect(instance){return queue.includes(instance.update)}function flushCallbacks(instance){const ctx=instance.ctx;const callbacks=ctx.__next_tick_callbacks;if(callbacks&&callbacks.length){const copies=callbacks.slice(0);callbacks.length=0;for(let i2=0;i2<copies.length;i2++){copies[i2]()}}}function nextTick(instance,fn){const ctx=instance.ctx;if(!ctx.__next_tick_pending&&!hasComponentEffect(instance)){return nextTick$1(fn&&fn.bind(instance.proxy))}let _resolve;if(!ctx.__next_tick_callbacks){ctx.__next_tick_callbacks=[]}ctx.__next_tick_callbacks.push((()=>{if(fn){callWithErrorHandling(fn.bind(instance.proxy),instance,14)}else if(_resolve){_resolve(instance.proxy)}}));return new Promise((resolve2=>{_resolve=resolve2}))}function clone(src,seen){src=unwrapper(src);const type=typeof src;if(type==="object"&&src!==null){let copy=seen.get(src);if(typeof copy!=="undefined"){return copy}if(isArray$3(src)){const len=src.length;copy=new Array(len);seen.set(src,copy);for(let i2=0;i2<len;i2++){copy[i2]=clone(src[i2],seen)}}else{copy={};seen.set(src,copy);for(const name in src){if(hasOwn$3(src,name)){copy[name]=clone(src[name],seen)}}}return copy}if(type!=="symbol"){return src}}function deepCopy$1(src){return clone(src,typeof WeakMap!=="undefined"?new WeakMap:new Map)}function getMPInstanceData(instance,keys){const data=instance.data;const ret=Object.create(null);keys.forEach((key=>{ret[key]=data[key]}));return ret}function patch(instance,data,oldData){if(!data){return}data=deepCopy$1(data);const ctx=instance.ctx;const mpType=ctx.mpType;if(mpType==="page"||mpType==="component"){data.r0=1;const mpInstance=ctx.$scope;const keys=Object.keys(data);const diffData=diff(data,getMPInstanceData(mpInstance,keys));if(Object.keys(diffData).length){ctx.__next_tick_pending=true;mpInstance.setData(diffData,(()=>{ctx.__next_tick_pending=false;flushCallbacks(instance)}));flushPreFlushCbs()}else{flushCallbacks(instance)}}}function initAppConfig(appConfig2){appConfig2.globalProperties.$nextTick=function $nextTick(fn){return nextTick(this.$,fn)}}function onApplyOptions(options,instance,publicThis){instance.appContext.config.globalProperties.$applyOptions(options,instance,publicThis);const computedOptions=options.computed;if(computedOptions){const keys=Object.keys(computedOptions);if(keys.length){const ctx=instance.ctx;if(!ctx.$computedKeys){ctx.$computedKeys=[]}ctx.$computedKeys.push(...keys)}}delete instance.ctx.$onApplyOptions}function setRef$1(instance,isUnmount=false){const{setupState:setupState,$templateRefs:$templateRefs,ctx:{$scope:$scope,$mpPlatform:$mpPlatform}}=instance;if($mpPlatform==="mp-alipay"){return}if(!$templateRefs||!$scope){return}if(isUnmount){return $templateRefs.forEach((templateRef=>setTemplateRef(templateRef,null,setupState)))}const check=$mpPlatform==="mp-baidu"||$mpPlatform==="mp-toutiao";const doSetByRefs=refs=>{const mpComponents=($scope.selectAllComponents(".r")||[]).concat($scope.selectAllComponents(".r-i-f")||[]);return refs.filter((templateRef=>{const refValue=findComponentPublicInstance(mpComponents,templateRef.i);if(check&&refValue===null){return true}setTemplateRef(templateRef,refValue,setupState);return false}))};const doSet=()=>{const refs=doSetByRefs($templateRefs);if(refs.length&&instance.proxy&&instance.proxy.$scope){instance.proxy.$scope.setData({r1:1},(()=>{doSetByRefs(refs)}))}};if($scope._$setRef){$scope._$setRef(doSet)}else{nextTick(instance,doSet)}}function toSkip(value){if(isObject$4(value)){markRaw(value)}return value}function findComponentPublicInstance(mpComponents,id2){const mpInstance=mpComponents.find((com=>com&&(com.properties||com.props).uI===id2));if(mpInstance){const vm=mpInstance.$vm;if(vm){return getExposeProxy(vm.$)||vm}return toSkip(mpInstance)}return null}function setTemplateRef({r:r2,f:f2},refValue,setupState){if(isFunction$2(r2)){r2(refValue,{})}else{const _isString=isString$2(r2);const _isRef=isRef(r2);if(_isString||_isRef){if(f2){if(!_isRef){return}if(!isArray$3(r2.value)){r2.value=[]}const existing=r2.value;if(existing.indexOf(refValue)===-1){existing.push(refValue);if(!refValue){return}onBeforeUnmount((()=>remove(existing,refValue)),refValue.$)}}else if(_isString){if(hasOwn$3(setupState,r2)){setupState[r2]=refValue}}else if(isRef(r2)){r2.value=refValue}else;}}}const queuePostRenderEffect=queuePostFlushCb;function mountComponent(initialVNode,options){const instance=initialVNode.component=createComponentInstance(initialVNode,options.parentComponent,null);{instance.ctx.$onApplyOptions=onApplyOptions;instance.ctx.$children=[]}if(options.mpType==="app"){instance.render=NOOP}if(options.onBeforeSetup){options.onBeforeSetup(instance,options)}setupComponent(instance);{if(options.parentComponent&&instance.proxy){options.parentComponent.ctx.$children.push(getExposeProxy(instance)||instance.proxy)}}setupRenderEffect(instance);return instance.proxy}const getFunctionalFallthrough=attrs=>{let res;for(const key in attrs){if(key==="class"||key==="style"||isOn(key)){(res||(res={}))[key]=attrs[key]}}return res};function renderComponentRoot(instance){const{type:Component2,vnode:vnode,proxy:proxy,withProxy:withProxy,props:props,propsOptions:[propsOptions],slots:slots,attrs:attrs,emit:emit2,render:render,renderCache:renderCache,data:data,setupState:setupState,ctx:ctx,uid:uid2,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:pruneComponentPropsCache2}}}},inheritAttrs:inheritAttrs}=instance;instance.$templateRefs=[];instance.$ei=0;pruneComponentPropsCache2(uid2);instance.__counter=instance.__counter===0?1:0;let result;const prev=setCurrentRenderingInstance(instance);try{if(vnode.shapeFlag&4){fallthroughAttrs(inheritAttrs,props,propsOptions,attrs);const proxyToUse=withProxy||proxy;result=render.call(proxyToUse,proxyToUse,renderCache,props,setupState,data,ctx)}else{fallthroughAttrs(inheritAttrs,props,propsOptions,Component2.props?attrs:getFunctionalFallthrough(attrs));const render2=Component2;result=render2.length>1?render2(props,{attrs:attrs,slots:slots,emit:emit2}):render2(props,null)}}catch(err){handleError(err,instance,1);result=false}setRef$1(instance);setCurrentRenderingInstance(prev);return result}function fallthroughAttrs(inheritAttrs,props,propsOptions,fallthroughAttrs2){if(props&&fallthroughAttrs2&&inheritAttrs!==false){const keys=Object.keys(fallthroughAttrs2).filter((key=>key!=="class"&&key!=="style"));if(!keys.length){return}if(propsOptions&&keys.some(isModelListener)){keys.forEach((key=>{if(!isModelListener(key)||!(key.slice(9)in propsOptions)){props[key]=fallthroughAttrs2[key]}}))}else{keys.forEach((key=>props[key]=fallthroughAttrs2[key]))}}}const updateComponentPreRender=instance=>{pauseTracking();flushPreFlushCbs();resetTracking()};function componentUpdateScopedSlotsFn(){const scopedSlotsData=this.$scopedSlotsData;if(!scopedSlotsData||scopedSlotsData.length===0){return}const mpInstance=this.ctx.$scope;const oldData=mpInstance.data;const diffData=Object.create(null);scopedSlotsData.forEach((({path:path,index:index2,data:data})=>{const oldScopedSlotData=getValueByDataPath(oldData,path);const diffPath=isString$2(index2)?`${path}.${index2}`:`${path}[${index2}]`;if(typeof oldScopedSlotData==="undefined"||typeof oldScopedSlotData[index2]==="undefined"){diffData[diffPath]=data}else{const diffScopedSlotData=diff(data,oldScopedSlotData[index2]);Object.keys(diffScopedSlotData).forEach((name=>{diffData[diffPath+"."+name]=diffScopedSlotData[name]}))}}));scopedSlotsData.length=0;if(Object.keys(diffData).length){mpInstance.setData(diffData)}}function toggleRecurse({effect:effect2,update:update},allowed){effect2.allowRecurse=update.allowRecurse=allowed}function setupRenderEffect(instance){const updateScopedSlots=componentUpdateScopedSlotsFn.bind(instance);instance.$updateScopedSlots=()=>nextTick$1((()=>queueJob(updateScopedSlots)));const componentUpdateFn=()=>{if(!instance.isMounted){onBeforeUnmount((()=>{setRef$1(instance,true)}),instance);patch(instance,renderComponentRoot(instance))}else{const{next:next,bu:bu,u:u2}=instance;toggleRecurse(instance,false);updateComponentPreRender();if(bu){invokeArrayFns$1(bu)}toggleRecurse(instance,true);patch(instance,renderComponentRoot(instance));if(u2){queuePostRenderEffect(u2)}}};const effect2=instance.effect=new ReactiveEffect2(componentUpdateFn,NOOP,(()=>queueJob(update)),instance.scope);const update=instance.update=()=>{if(effect2.dirty){effect2.run()}};update.id=instance.uid;toggleRecurse(instance,true);update()}function unmountComponent(instance){const{bum:bum,scope:scope,update:update,um:um}=instance;if(bum){invokeArrayFns$1(bum)}scope.stop();if(update){update.active=false}if(um){queuePostRenderEffect(um)}queuePostRenderEffect((()=>{instance.isUnmounted=true}))}const oldCreateApp=createAppAPI();function getTarget(){if(typeof window!=="undefined"){return window}if(typeof globalThis!=="undefined"){return globalThis}if(typeof global!=="undefined"){return global}if(typeof my!=="undefined"){return my}}function createVueApp(rootComponent,rootProps=null){const target=getTarget();target.__VUE__=true;const app=oldCreateApp(rootComponent,rootProps);const appContext=app._context;initAppConfig(appContext.config);const createVNode2=initialVNode=>{initialVNode.appContext=appContext;initialVNode.shapeFlag=6;return initialVNode};const createComponent2=function createComponent22(initialVNode,options){return mountComponent(createVNode2(initialVNode),options)};const destroyComponent=function destroyComponent2(component){return component&&unmountComponent(component.$)};app.mount=function mount(){rootComponent.render=NOOP;const instance=mountComponent(createVNode2({type:rootComponent}),{mpType:"app",mpInstance:null,parentComponent:null,slots:[],props:null});app._instance=instance.$;instance.$app=app;instance.$createComponent=createComponent2;instance.$destroyComponent=destroyComponent;appContext.$appInstance=instance;return instance};app.unmount=function unmount(){};return app}function createVNode(){}function injectLifecycleHook(name,hook,publicThis,instance){if(isFunction$2(hook)){injectHook(name,hook.bind(publicThis),instance)}}function initHooks$1(options,instance,publicThis){const mpType=options.mpType||publicThis.$mpType;if(!mpType||mpType==="component"){return}Object.keys(options).forEach((name=>{if(isUniLifecycleHook(name,options[name],false)){const hooks=options[name];if(isArray$3(hooks)){hooks.forEach((hook=>injectLifecycleHook(name,hook,publicThis,instance)))}else{injectLifecycleHook(name,hooks,publicThis,instance)}}}))}function applyOptions(options,instance,publicThis){initHooks$1(options,instance,publicThis)}function set(target,key,val){return target[key]=val}function $callMethod(method,...args){const fn=this[method];if(fn){return fn(...args)}console.error(`method ${method} not found`);return null}function createErrorHandler(app){return function errorHandler(err,instance,_info){if(!instance){throw err}const appInstance=app._instance;if(!appInstance||!appInstance.proxy){throw err}{appInstance.proxy.$callHook(ON_ERROR,err)}}}function mergeAsArray(to,from){return to?[...new Set([].concat(to,from))]:from}function initOptionMergeStrategies(optionMergeStrategies){UniLifecycleHooks.forEach((name=>{optionMergeStrategies[name]=mergeAsArray}))}let realAtob;const b64="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";const b64re=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;if(typeof atob!=="function"){realAtob=function(str){str=String(str).replace(/[\t\n\f\r ]+/g,"");if(!b64re.test(str)){throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.")}str+="==".slice(2-(str.length&3));var bitmap;var result="";var r1;var r2;var i2=0;for(;i2<str.length;){bitmap=b64.indexOf(str.charAt(i2++))<<18|b64.indexOf(str.charAt(i2++))<<12|(r1=b64.indexOf(str.charAt(i2++)))<<6|(r2=b64.indexOf(str.charAt(i2++)));result+=r1===64?String.fromCharCode(bitmap>>16&255):r2===64?String.fromCharCode(bitmap>>16&255,bitmap>>8&255):String.fromCharCode(bitmap>>16&255,bitmap>>8&255,bitmap&255)}return result}}else{realAtob=atob}function b64DecodeUnicode(str){return decodeURIComponent(realAtob(str).split("").map((function(c2){return"%"+("00"+c2.charCodeAt(0).toString(16)).slice(-2)})).join(""))}function getCurrentUserInfo(){const token=index.getStorageSync("uni_id_token")||"";const tokenArr=token.split(".");if(!token||tokenArr.length!==3){return{uid:null,role:[],permission:[],tokenExpired:0}}let userInfo;try{userInfo=JSON.parse(b64DecodeUnicode(tokenArr[1]))}catch(error){throw new Error("获取当前用户信息出错，详细错误信息为："+error.message)}userInfo.tokenExpired=userInfo.exp*1e3;delete userInfo.exp;delete userInfo.iat;return userInfo}function uniIdMixin(globalProperties){globalProperties.uniIDHasRole=function(roleId){const{role:role}=getCurrentUserInfo();return role.indexOf(roleId)>-1};globalProperties.uniIDHasPermission=function(permissionId){const{permission:permission}=getCurrentUserInfo();return this.uniIDHasRole("admin")||permission.indexOf(permissionId)>-1};globalProperties.uniIDTokenValid=function(){const{tokenExpired:tokenExpired}=getCurrentUserInfo();return tokenExpired>Date.now()}}function initApp(app){const appConfig2=app._context.config;appConfig2.errorHandler=invokeCreateErrorHandler(app,createErrorHandler);initOptionMergeStrategies(appConfig2.optionMergeStrategies);const globalProperties=appConfig2.globalProperties;{uniIdMixin(globalProperties)}{globalProperties.$set=set;globalProperties.$applyOptions=applyOptions;globalProperties.$callMethod=$callMethod}{index.invokeCreateVueAppHook(app)}}const propsCaches=Object.create(null);function renderProps(props){const{uid:uid2,__counter:__counter}=getCurrentInstance();const propsId=(propsCaches[uid2]||(propsCaches[uid2]=[])).push(guardReactiveProps(props))-1;return uid2+","+propsId+","+__counter}function pruneComponentPropsCache(uid2){delete propsCaches[uid2]}function findComponentPropsData(up){if(!up){return}const[uid2,propsId]=up.split(",");if(!propsCaches[uid2]){return}return propsCaches[uid2][parseInt(propsId)]}var plugin={install(app){initApp(app);app.config.globalProperties.pruneComponentPropsCache=pruneComponentPropsCache;const oldMount=app.mount;app.mount=function mount(rootContainer){const instance=oldMount.call(app,rootContainer);const createApp2=getCreateApp();if(createApp2){createApp2(instance)}else{if(typeof createMiniProgramApp!=="undefined"){createMiniProgramApp(instance)}}return instance}}};function getCreateApp(){const method="createApp";if(typeof global!=="undefined"&&typeof global[method]!=="undefined"){return global[method]}else if(typeof my!=="undefined"){return my[method]}}function vOn(value,key){const instance=getCurrentInstance();const ctx=instance.ctx;const extraKey="";const name="e"+instance.$ei+++extraKey;const mpInstance=ctx.$scope;if(!value){delete mpInstance[name];return name}const existingInvoker=mpInstance[name];if(existingInvoker){existingInvoker.value=value}else{mpInstance[name]=createInvoker(value,instance)}return name}function createInvoker(initialValue,instance){const invoker=e2=>{patchMPEvent(e2);let args=[e2];if(e2.detail&&e2.detail.__args__){args=e2.detail.__args__}const eventValue=invoker.value;const invoke=()=>callWithAsyncErrorHandling(patchStopImmediatePropagation(e2,eventValue),instance,5,args);const eventTarget=e2.target;const eventSync=eventTarget?eventTarget.dataset?String(eventTarget.dataset.eventsync)==="true":false:false;if(bubbles.includes(e2.type)&&!eventSync){setTimeout(invoke)}else{const res=invoke();if(e2.type==="input"&&(isArray$3(res)||isPromise(res))){return}return res}};invoker.value=initialValue;return invoker}const bubbles=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];function patchMPEvent(event){if(event.type&&event.target){event.preventDefault=NOOP;event.stopPropagation=NOOP;event.stopImmediatePropagation=NOOP;if(!hasOwn$3(event,"detail")){event.detail={}}if(hasOwn$3(event,"markerId")){event.detail=typeof event.detail==="object"?event.detail:{};event.detail.markerId=event.markerId}if(isPlainObject$3(event.detail)&&hasOwn$3(event.detail,"checked")&&!hasOwn$3(event.detail,"value")){event.detail.value=event.detail.checked}if(isPlainObject$3(event.detail)){event.target=extend({},event.target,event.detail)}}}function patchStopImmediatePropagation(e2,value){if(isArray$3(value)){const originalStop=e2.stopImmediatePropagation;e2.stopImmediatePropagation=()=>{originalStop&&originalStop.call(e2);e2._stopped=true};return value.map((fn=>e3=>!e3._stopped&&fn(e3)))}else{return value}}function vFor(source,renderItem){let ret;if(isArray$3(source)||isString$2(source)){ret=new Array(source.length);for(let i2=0,l2=source.length;i2<l2;i2++){ret[i2]=renderItem(source[i2],i2,i2)}}else if(typeof source==="number"){ret=new Array(source);for(let i2=0;i2<source;i2++){ret[i2]=renderItem(i2+1,i2,i2)}}else if(isObject$4(source)){if(source[Symbol.iterator]){ret=Array.from(source,((item,i2)=>renderItem(item,i2,i2)))}else{const keys=Object.keys(source);ret=new Array(keys.length);for(let i2=0,l2=keys.length;i2<l2;i2++){const key=keys[i2];ret[i2]=renderItem(source[key],key,i2)}}}else{ret=[]}return ret}function stringifyStyle(value){if(isString$2(value)){return value}return stringify(normalizeStyle(value))}function stringify(styles){let ret="";if(!styles||isString$2(styles)){return ret}for(const key in styles){ret+=`${key.startsWith(`--`)?key:hyphenate(key)}:${styles[key]};`}return ret}const o$1=(value,key)=>vOn(value);const f$1=(source,renderItem)=>vFor(source,renderItem);const s$1=value=>stringifyStyle(value);const e$1=(target,...sources)=>extend(target,...sources);const h$1=str=>hyphenate(str);const n$1=value=>normalizeClass(value);const t$1=val=>toDisplayString$1(val);const p$1=props=>renderProps(props);function createApp$1(rootComponent,rootProps=null){rootComponent&&(rootComponent.mpType="app");return createVueApp(rootComponent,rootProps).use(plugin)}const createSSRApp=createApp$1;const MP_METHODS=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function createEmitFn(oldEmit,ctx){return function emit2(event,...args){const scope=ctx.$scope;if(scope&&event){const detail={__args__:args};{scope.triggerEvent(event,detail)}}{const props=scope.props;if(props&&props[`on${capitalize(event)}`]){return}}return oldEmit.apply(this,[event,...args])}}function initBaseInstance(instance,options){const ctx=instance.ctx;ctx.mpType=options.mpType;ctx.$mpType=options.mpType;ctx.$mpPlatform="mp-alipay";ctx.$scope=options.mpInstance;ctx.$mp={};{ctx._self={}}instance.slots={};if(isArray$3(options.slots)&&options.slots.length){options.slots.forEach((name=>{instance.slots[name]=true}));if(instance.slots[SLOT_DEFAULT_NAME]){instance.slots.default=true}}ctx.getOpenerEventChannel=function(){{if(my.canIUse("getOpenerEventChannel"))return options.mpInstance.getOpenerEventChannel()}if(!this.__eventChannel__){this.__eventChannel__=new EventChannel}return this.__eventChannel__};ctx.$hasHook=hasHook;ctx.$callHook=callHook;instance.emit=createEmitFn(instance.emit,ctx)}function initComponentInstance(instance,options){initBaseInstance(instance,options);const ctx=instance.ctx;MP_METHODS.forEach((method=>{ctx[method]=function(...args){const mpInstance=ctx.$scope;if(mpInstance&&mpInstance[method]){return mpInstance[method].apply(mpInstance,args)}{return my[method]&&my[method].apply(my,args)}}}))}function initMocks(instance,mpInstance,mocks2){const ctx=instance.ctx;mocks2.forEach((mock=>{if(hasOwn$3(mpInstance,mock)){instance[mock]=ctx[mock]=mpInstance[mock]}}))}function hasHook(name){const hooks=this.$[name];if(hooks&&hooks.length){return true}return false}function callHook(name,args){if(name==="mounted"){callHook.call(this,"bm");this.$.isMounted=true;name="m"}{if(name==="onLoad"&&args&&args.__id__&&isFunction$2(my.getEventChannel)){this.__eventChannel__=my.getEventChannel(args.__id__);delete args.__id__}}const hooks=this.$[name];return hooks&&invokeArrayFns(hooks,args)}const PAGE_INIT_HOOKS=[ON_LOAD,ON_SHOW,ON_HIDE,ON_UNLOAD,ON_RESIZE,ON_TAB_ITEM_TAP,ON_REACH_BOTTOM,ON_PULL_DOWN_REFRESH,ON_ADD_TO_FAVORITES];function findHooks(vueOptions,hooks=new Set){if(vueOptions){Object.keys(vueOptions).forEach((name=>{if(isUniLifecycleHook(name,vueOptions[name])){hooks.add(name)}}));{const{extends:extendsOptions,mixins:mixins}=vueOptions;if(mixins){mixins.forEach((mixin=>findHooks(mixin,hooks)))}if(extendsOptions){findHooks(extendsOptions,hooks)}}}return hooks}function initHook(mpOptions,hook,excludes){if(excludes.indexOf(hook)===-1&&!hasOwn$3(mpOptions,hook)){mpOptions[hook]=function(args){return this.$vm&&this.$vm.$callHook(hook,args)}}}const EXCLUDE_HOOKS=[ON_READY];function initHooks(mpOptions,hooks,excludes=EXCLUDE_HOOKS){hooks.forEach((hook=>initHook(mpOptions,hook,excludes)))}function initUnknownHooks(mpOptions,vueOptions,excludes=EXCLUDE_HOOKS){findHooks(vueOptions).forEach((hook=>initHook(mpOptions,hook,excludes)))}function initRuntimeHooks(mpOptions,runtimeHooks){if(!runtimeHooks){return}const hooks=Object.keys(MINI_PROGRAM_PAGE_RUNTIME_HOOKS);hooks.forEach((hook=>{if(runtimeHooks&MINI_PROGRAM_PAGE_RUNTIME_HOOKS[hook]){initHook(mpOptions,hook,[])}}))}const HOOKS=[ON_SHOW,ON_HIDE,ON_ERROR,ON_THEME_CHANGE,ON_PAGE_NOT_FOUND,ON_UNHANDLE_REJECTION];function parseApp(instance,parseAppOptions2){const internalInstance=instance.$;const appOptions={globalData:instance.$options&&instance.$options.globalData||{},$vm:instance,onLaunch(options){this.$vm=instance;const ctx=internalInstance.ctx;if(this.$vm&&ctx.$scope){return}initBaseInstance(internalInstance,{mpType:"app",mpInstance:this,slots:[]});ctx.globalData=this.globalData;instance.$callHook(ON_LAUNCH,options)}};const{onError:onError}=internalInstance;if(onError){internalInstance.appContext.config.errorHandler=err=>{instance.$callHook(ON_ERROR,err)}}initLocale(instance);const vueOptions=instance.$.type;initHooks(appOptions,HOOKS);initUnknownHooks(appOptions,vueOptions);{const methods=vueOptions.methods;methods&&extend(appOptions,methods)}if(parseAppOptions2){parseAppOptions2.parse(appOptions)}return appOptions}function initCreateApp(parseAppOptions2){return function createApp2(vm){return App(parseApp(vm,parseAppOptions2))}}function initCreateSubpackageApp(parseAppOptions2){return function createApp2(vm){const appOptions=parseApp(vm,parseAppOptions2);const app=isFunction$2(getApp)&&getApp({allowDefault:true});if(!app)return;vm.$.ctx.$scope=app;const globalData=app.globalData;if(globalData){Object.keys(appOptions.globalData).forEach((name=>{if(!hasOwn$3(globalData,name)){globalData[name]=appOptions.globalData[name]}}))}Object.keys(appOptions).forEach((name=>{if(!hasOwn$3(app,name)){app[name]=appOptions[name]}}));initAppLifecycle(appOptions,vm)}}function initAppLifecycle(appOptions,vm){if(isFunction$2(appOptions.onLaunch)){const args=my.getLaunchOptionsSync&&my.getLaunchOptionsSync();appOptions.onLaunch(args)}if(isFunction$2(appOptions.onShow)&&my.onAppShow){my.onAppShow((args=>{vm.$callHook("onShow",args)}))}if(isFunction$2(appOptions.onHide)&&my.onAppHide){my.onAppHide((args=>{vm.$callHook("onHide",args)}))}}function initLocale(appVm){const locale=ref(normalizeLocale(my.getSystemInfoSync().language)||LOCALE_EN);Object.defineProperty(appVm,"$locale",{get(){return locale.value},set(v2){locale.value=v2}})}function initVueIds(vueIds,mpInstance){if(!vueIds){return}const ids=vueIds.split(",");const len=ids.length;if(len===1){mpInstance._$vueId=ids[0]}else if(len===2){mpInstance._$vueId=ids[0];mpInstance._$vuePid=ids[1]}}function initWxsCallMethods(methods,wxsCallMethods){if(!isArray$3(wxsCallMethods)){return}wxsCallMethods.forEach((callMethod=>{methods[callMethod]=function(args){return this.$vm[callMethod](args)}}))}function findVmByVueId(instance,vuePid){const $children=instance.$children;for(let i2=$children.length-1;i2>=0;i2--){const childVm=$children[i2];if(childVm.$scope._$vueId===vuePid){return childVm}}let parentVm;for(let i2=$children.length-1;i2>=0;i2--){parentVm=findVmByVueId($children[i2],vuePid);if(parentVm){return parentVm}}}const builtInProps=["eO","uR","uRIF","uI","uT","uP","uS"];function initDefaultProps(options,isBehavior=false){const properties={};if(!isBehavior){builtInProps.forEach((name=>{properties[name]={type:null,value:""}}));properties.uS={type:null,value:[],observer:function(newVal){const $slots=Object.create(null);newVal&&newVal.forEach((slotName=>{$slots[slotName]=true}));this.setData({$slots:$slots})}}}if(options.behaviors){if(options.behaviors.includes("my://form-field")){if(!options.properties||!options.properties.name){properties.name={type:null,value:""}}if(!options.properties||!options.properties.value){properties.value={type:null,value:""}}}}return properties}function initVirtualHostProps(options){const properties={};{{properties.virtualHostStyle={type:null,value:""};properties.virtualHostClass={type:null,value:""}}}return properties}function initProps(mpComponentOptions){if(!mpComponentOptions.properties){mpComponentOptions.properties={}}extend(mpComponentOptions.properties,initDefaultProps(mpComponentOptions),initVirtualHostProps())}function findPropsData(properties,isPage){return(isPage?findPagePropsData(properties):findComponentPropsData(properties.uP))||{}}function findPagePropsData(properties){const propsData={};if(isPlainObject$3(properties)){Object.keys(properties).forEach((name=>{if(builtInProps.indexOf(name)===-1){propsData[name]=properties[name]}}))}return propsData}function initData(_2){return{}}function updateMiniProgramComponentProperties(up,mpInstance){const prevProps=mpInstance.props;const nextProps=findComponentPropsData(up)||{};if(hasPropsChanged(prevProps,nextProps,false)){mpInstance.setData(nextProps)}}function updateComponentProps(up,instance){const prevProps=toRaw(instance.props);const nextProps=findComponentPropsData(up)||{};if(hasPropsChanged(prevProps,nextProps)){updateProps(instance,nextProps,prevProps);if(hasQueueJob(instance.update)){invalidateJob(instance.update)}{instance.update()}}}function hasPropsChanged(prevProps,nextProps,checkLen=true){const nextKeys=Object.keys(nextProps);if(checkLen&&nextKeys.length!==Object.keys(prevProps).length){return true}for(let i2=0;i2<nextKeys.length;i2++){const key=nextKeys[i2];if(nextProps[key]!==prevProps[key]){return true}}return false}function initBehaviors(vueOptions){const vueBehaviors=vueOptions.behaviors;let vueProps=vueOptions.props;if(!vueProps){vueOptions.props=vueProps=[]}const behaviors=[];if(isArray$3(vueBehaviors)){vueBehaviors.forEach((behavior=>{behaviors.push(behavior.replace("uni://","my://"));if(behavior==="uni://form-field"){if(isArray$3(vueProps)){vueProps.push("name");vueProps.push("modelValue")}else{vueProps.name={type:String,default:""};vueProps.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}}}}))}return behaviors}let $createComponentFn;let $destroyComponentFn;function getAppVm(){return getApp().$vm}function $createComponent(initialVNode,options){if(!$createComponentFn){$createComponentFn=getAppVm().$createComponent}const proxy=$createComponentFn(initialVNode,options);return getExposeProxy(proxy.$)||proxy}function $destroyComponent(instance){if(!$destroyComponentFn){$destroyComponentFn=getAppVm().$destroyComponent}return $destroyComponentFn(instance)}function initCreatePluginApp(parseAppOptions2){return function createApp2(vm){initAppLifecycle(parseApp(vm,parseAppOptions2),vm)}}function handleLink$1(event){const detail=event.detail||event.value;const vuePid=detail.vuePid;let parentVm;if(vuePid){parentVm=findVmByVueId(this.$vm,vuePid)}if(!parentVm){parentVm=this.$vm}detail.parent=parentVm}const isComponent2=my.canIUse("component2");const mocks=["$id"];function initRelation(mpInstance,detail){mpInstance.props.onVI(detail)}function initSpecialMethods(mpInstance){if(!mpInstance.$vm){return}let path=mpInstance.is||mpInstance.route;if(!path){return}if(path.indexOf("/")===0){path=path.slice(1)}const specialMethods=my.specialMethods&&my.specialMethods[path];if(specialMethods){specialMethods.forEach((method=>{if(isFunction$2(mpInstance.$vm[method])){mpInstance[method]=function(event){if(hasOwn$3(event,"markerId")){event.detail=typeof event.detail==="object"?event.detail:{};event.detail.markerId=event.markerId}mpInstance.$vm[method](event)}}}))}}function initChildVues(mpInstance){if(!mpInstance.$vm){return}const childVues=mpInstance._$childVues;if(childVues){childVues.forEach((relationOptions=>{handleLink$1.call(mpInstance,{detail:relationOptions});const{mpInstance:childMPInstance,createComponent:createComponent2}=relationOptions;childMPInstance.$vm=createComponent2(relationOptions.parent);initSpecialMethods(childMPInstance);if(relationOptions.parent){handleRef.call(relationOptions.parent.$scope,childMPInstance)}initChildVues(childMPInstance);childMPInstance.$vm.$callHook("mounted");childMPInstance.$vm.$callHook(ON_READY)}))}delete mpInstance._$childVues}function handleRef(ref2){if(!ref2){return}const refName=ref2.props.uR;const refInForName=ref2.props.uRIF;if(!refName&&!refInForName){return}const instance=this.$vm.$;const refs=instance.refs===EMPTY_OBJ?instance.refs={}:instance.refs;const{setupState:setupState}=instance;const refValue=ref2.$vm;if(refName){if(isString$2(refName)){refs[refName]=refValue;if(hasOwn$3(setupState,refName)){setupState[refName]=refValue}}else{setRef(refName,refValue,refs,setupState)}}else if(refInForName){if(isString$2(refInForName)){(refs[refInForName]||(refs[refInForName]=[])).push(refValue)}else{setRef(refInForName,refValue,refs,setupState)}}}function isTemplateRef(opts){return!!(opts&&opts.r)}function setRef(ref2,refValue,refs,setupState){if(isRef(ref2)){ref2.value=refValue}else if(isFunction$2(ref2)){const templateRef=ref2(refValue,refs);if(isTemplateRef(templateRef)){setTemplateRef(templateRef,refValue,setupState)}}}function triggerEvent(type,detail){const handler=this.props[customizeEvent("on-"+type)];if(!handler){return}const target={dataset:{}};handler({type:customizeEvent(type),target:target,currentTarget:target,detail:detail})}function initPropsObserver(componentOptions){const observe=function observe2(props){const nextProps=isComponent2?props:this.props;const up=nextProps.uP;if(!up){return}if(this.$vm){updateComponentProps(up,this.$vm.$)}else if(this.props.uT==="m"){updateMiniProgramComponentProperties(up,this)}};if(isComponent2){componentOptions.deriveDataFromProps=observe}else{componentOptions.didUpdate=observe}}const handleLink=function(){if(isComponent2){return function handleLink2(detail){return handleLink$1.call(this,{detail:detail})}}return function handleLink2(detail){if(this.$vm&&this.$vm.$.isMounted){return handleLink$1.call(this,{detail:detail})}(this._$childVues||(this._$childVues=[])).unshift(detail)}}();function createVueComponent(mpType,mpInstance,vueOptions,parent){return $createComponent({type:vueOptions,props:findPropsData(mpInstance.props,mpType==="page")},{mpType:mpType,mpInstance:mpInstance,slots:mpInstance.props.uS||{},parentComponent:parent&&parent.$,onBeforeSetup(instance,options){initMocks(instance,mpInstance,mocks);initComponentInstance(instance,options)}})}const MPComponent=Component;Component=function(options){const isVueComponent=options.props&&typeof options.props.uP!=="undefined";if(!isVueComponent){initPropsObserver(options)}return MPComponent(options)};function onAliAuthError(method,$event){$event.type="getphonenumber";$event.detail.errMsg="getPhoneNumber:fail Error: "+$event.detail.errorMessage;method($event)}function onAliGetAuthorize(method,$event){my.getPhoneNumber({success:res=>{$event.type="getphonenumber";const response=JSON.parse(res.response);$event.detail.errMsg="getPhoneNumber:ok";$event.detail.encryptedData=response.response;$event.detail.sign=response.sign;method($event)},fail:res=>{$event.type="getphonenumber";$event.detail.errMsg="getPhoneNumber:fail Error: "+JSON.stringify(res);method($event)}})}function parse$1(appOptions){const oldOnLaunch=appOptions.onLaunch;appOptions.onLaunch=function onLaunch(options){oldOnLaunch.call(this,options);if(!this.$vm){return}const globalProperties=this.$vm.$app.config.globalProperties;if(!globalProperties.$onAliAuthError){globalProperties.$onAliAuthError=onAliAuthError;globalProperties.$onAliGetAuthorize=onAliGetAuthorize}}}var parseAppOptions=Object.freeze({__proto__:null,parse:parse$1});function initCreatePage(){return function createPage2(vueOptions){vueOptions=vueOptions.default||vueOptions;const pageOptions={onLoad(query){this.options=query;this.$page={fullPath:addLeadingSlash(this.route+stringifyQuery(query))};this.props=query;this.$vm=createVueComponent("page",this,vueOptions);initSpecialMethods(this);this.$vm.$callHook(ON_LOAD,query)},onShow(){this.$vm.$callHook(ON_SHOW)},onReady(){initChildVues(this);this.$vm.$callHook("mounted");this.$vm.$callHook(ON_READY)},onUnload(){if(this.$vm){this.$vm.$callHook(ON_UNLOAD);$destroyComponent(this.$vm)}},events:{onBack(){this.$vm.$callHook(ON_BACK_PRESS)},onKeyboardHeight:res=>{my.$emit("uni:keyboardHeightChange",res)}},__r:handleRef,__l:handleLink};if(isPlainObject$3(vueOptions.events)){extend(pageOptions.events,vueOptions.events)}{pageOptions.data=initData()}initHooks(pageOptions,PAGE_INIT_HOOKS);initUnknownHooks(pageOptions,vueOptions);initRuntimeHooks(pageOptions,vueOptions.__runtimeHooks);initWxsCallMethods(pageOptions,vueOptions.wxsCallMethods);return Page(pageOptions)}}function initComponentProps(_rawProps){const propertiesOptions={properties:{}};initProps(propertiesOptions);const properties=propertiesOptions.properties;const props={onVI:function(){}};Object.keys(properties).forEach((key=>{if(key!=="uS"){props[key]=properties[key].value}}));return props}function initVm(mpInstance,createComponent2){if(mpInstance.$vm){return}const properties=mpInstance.props;initVueIds(properties.uI,mpInstance);const relationOptions={vuePid:mpInstance._$vuePid,mpInstance:mpInstance,createComponent:createComponent2};if(isComponent2){initRelation(mpInstance,relationOptions);mpInstance.$vm=createComponent2(relationOptions.parent)}else{initRelation(mpInstance,relationOptions);if(relationOptions.parent){mpInstance.$vm=createComponent2(relationOptions.parent);handleRef.call(relationOptions.parent.$scope,mpInstance);initChildVues(mpInstance);mpInstance.$vm.$callHook("mounted")}}}function initCreateComponent(){return function createComponent2(vueOptions){vueOptions=vueOptions.default||vueOptions;const mpComponentOptions={props:initComponentProps(vueOptions.props),didMount(){const createComponent3=parent=>createVueComponent("component",this,vueOptions,parent);if(my.dd){setTimeout((()=>{initVm(this,createComponent3)}),4)}else{initVm(this,createComponent3)}initSpecialMethods(this);if(isComponent2){this.$vm.$callHook("mounted")}},didUnmount(){if(this.$vm){pruneComponentPropsCache(this.$vm.$.uid);$destroyComponent(this.$vm)}},methods:{__r:handleRef,__l:handleLink,triggerEvent:triggerEvent}};if(vueOptions.options){mpComponentOptions.options=vueOptions.options}{mpComponentOptions.data=initData();mpComponentOptions.mixins=initBehaviors(vueOptions)}if(isComponent2){mpComponentOptions.onInit=function onInit(){initVm(this,(parent=>createVueComponent("component",this,vueOptions,parent)))}}initPropsObserver(mpComponentOptions);initWxsCallMethods(mpComponentOptions.methods,vueOptions.wxsCallMethods);return Component(mpComponentOptions)}}const createApp=initCreateApp(parseAppOptions);const createPage=initCreatePage();const createComponent=initCreateComponent();const createPluginApp=initCreatePluginApp(parseAppOptions);const createSubpackageApp=initCreateSubpackageApp(parseAppOptions);my.EventChannel=EventChannel;my.createApp=createApp;my.createPage=createPage;my.createComponent=createComponent;my.createPluginApp=createPluginApp;my.createSubpackageApp=createSubpackageApp;var isVue2=false;
/*!
 * pinia v2.2.2
 * (c) 2024 Eduardo San Martin Morote
 * @license MIT
 */let activePinia;const setActivePinia=pinia=>activePinia=pinia;const getActivePinia=()=>hasInjectionContext()&&inject(piniaSymbol)||activePinia;const piniaSymbol=Symbol();function isPlainObject$2(o2){return o2&&typeof o2==="object"&&Object.prototype.toString.call(o2)==="[object Object]"&&typeof o2.toJSON!=="function"}var MutationType;(function(MutationType2){MutationType2["direct"]="direct";MutationType2["patchObject"]="patch object";MutationType2["patchFunction"]="patch function"})(MutationType||(MutationType={}));const IS_CLIENT=typeof window!=="undefined";function createPinia(){const scope=effectScope(true);const state=scope.run((()=>ref({})));let _p=[];let toBeInstalled=[];const pinia=markRaw({install(app){setActivePinia(pinia);{pinia._a=app;app.provide(piniaSymbol,pinia);app.config.globalProperties.$pinia=pinia;toBeInstalled.forEach((plugin2=>_p.push(plugin2)));toBeInstalled=[]}},use(plugin2){if(!this._a&&!isVue2){toBeInstalled.push(plugin2)}else{_p.push(plugin2)}return this},_p:_p,_a:null,_e:scope,_s:new Map,state:state});return pinia}function disposePinia(pinia){pinia._e.stop();pinia._s.clear();pinia._p.splice(0);pinia.state.value={};pinia._a=null}function acceptHMRUpdate(initialUseStore,hot){{return()=>{}}}const noop=()=>{};function addSubscription(subscriptions,callback,detached,onCleanup=noop){subscriptions.push(callback);const removeSubscription=()=>{const idx=subscriptions.indexOf(callback);if(idx>-1){subscriptions.splice(idx,1);onCleanup()}};if(!detached&&getCurrentScope()){onScopeDispose(removeSubscription)}return removeSubscription}function triggerSubscriptions(subscriptions,...args){subscriptions.slice().forEach((callback=>{callback(...args)}))}const fallbackRunWithContext=fn=>fn();const ACTION_MARKER=Symbol();const ACTION_NAME=Symbol();function mergeReactiveObjects(target,patchToApply){if(target instanceof Map&&patchToApply instanceof Map){patchToApply.forEach(((value,key)=>target.set(key,value)))}else if(target instanceof Set&&patchToApply instanceof Set){patchToApply.forEach(target.add,target)}for(const key in patchToApply){if(!patchToApply.hasOwnProperty(key))continue;const subPatch=patchToApply[key];const targetValue=target[key];if(isPlainObject$2(targetValue)&&isPlainObject$2(subPatch)&&target.hasOwnProperty(key)&&!isRef(subPatch)&&!isReactive(subPatch)){target[key]=mergeReactiveObjects(targetValue,subPatch)}else{target[key]=subPatch}}return target}const skipHydrateSymbol=Symbol();function skipHydrate(obj){return Object.defineProperty(obj,skipHydrateSymbol,{})}function shouldHydrate(obj){return!isPlainObject$2(obj)||!obj.hasOwnProperty(skipHydrateSymbol)}const{assign:assign$1}=Object;function isComputed(o2){return!!(isRef(o2)&&o2.effect)}function createOptionsStore(id2,options,pinia,hot){const{state:state,actions:actions,getters:getters}=options;const initialState=pinia.state.value[id2];let store;function setup(){if(!initialState&&true){{pinia.state.value[id2]=state?state():{}}}const localState=toRefs(pinia.state.value[id2]);return assign$1(localState,actions,Object.keys(getters||{}).reduce(((computedGetters,name)=>{computedGetters[name]=markRaw(computed((()=>{setActivePinia(pinia);const store2=pinia._s.get(id2);return getters[name].call(store2,store2)})));return computedGetters}),{}))}store=createSetupStore(id2,setup,options,pinia,hot,true);return store}function createSetupStore($id,setup,options={},pinia,hot,isOptionsStore){let scope;const optionsForPlugin=assign$1({actions:{}},options);const $subscribeOptions={deep:true};let isListening;let isSyncListening;let subscriptions=[];let actionSubscriptions=[];let debuggerEvents;const initialState=pinia.state.value[$id];if(!isOptionsStore&&!initialState&&true){{pinia.state.value[$id]={}}}ref({});let activeListener;function $patch(partialStateOrMutator){let subscriptionMutation;isListening=isSyncListening=false;if(typeof partialStateOrMutator==="function"){partialStateOrMutator(pinia.state.value[$id]);subscriptionMutation={type:MutationType.patchFunction,storeId:$id,events:debuggerEvents}}else{mergeReactiveObjects(pinia.state.value[$id],partialStateOrMutator);subscriptionMutation={type:MutationType.patchObject,payload:partialStateOrMutator,storeId:$id,events:debuggerEvents}}const myListenerId=activeListener=Symbol();nextTick$1().then((()=>{if(activeListener===myListenerId){isListening=true}}));isSyncListening=true;triggerSubscriptions(subscriptions,subscriptionMutation,pinia.state.value[$id])}const $reset=isOptionsStore?function $reset2(){const{state:state}=options;const newState=state?state():{};this.$patch(($state=>{assign$1($state,newState)}))}:noop;function $dispose(){scope.stop();subscriptions=[];actionSubscriptions=[];pinia._s.delete($id)}const action=(fn,name="")=>{if(ACTION_MARKER in fn){fn[ACTION_NAME]=name;return fn}const wrappedAction=function(){setActivePinia(pinia);const args=Array.from(arguments);const afterCallbackList=[];const onErrorCallbackList=[];function after(callback){afterCallbackList.push(callback)}function onError(callback){onErrorCallbackList.push(callback)}triggerSubscriptions(actionSubscriptions,{args:args,name:wrappedAction[ACTION_NAME],store:store,after:after,onError:onError});let ret;try{ret=fn.apply(this&&this.$id===$id?this:store,args)}catch(error){triggerSubscriptions(onErrorCallbackList,error);throw error}if(ret instanceof Promise){return ret.then((value=>{triggerSubscriptions(afterCallbackList,value);return value})).catch((error=>{triggerSubscriptions(onErrorCallbackList,error);return Promise.reject(error)}))}triggerSubscriptions(afterCallbackList,ret);return ret};wrappedAction[ACTION_MARKER]=true;wrappedAction[ACTION_NAME]=name;return wrappedAction};const partialStore={_p:pinia,$id:$id,$onAction:addSubscription.bind(null,actionSubscriptions),$patch:$patch,$reset:$reset,$subscribe(callback,options2={}){const removeSubscription=addSubscription(subscriptions,callback,options2.detached,(()=>stopWatcher()));const stopWatcher=scope.run((()=>watch((()=>pinia.state.value[$id]),(state=>{if(options2.flush==="sync"?isSyncListening:isListening){callback({storeId:$id,type:MutationType.direct,events:debuggerEvents},state)}}),assign$1({},$subscribeOptions,options2))));return removeSubscription},$dispose:$dispose};const store=reactive(partialStore);pinia._s.set($id,store);const runWithContext=pinia._a&&pinia._a.runWithContext||fallbackRunWithContext;const setupStore=runWithContext((()=>pinia._e.run((()=>(scope=effectScope()).run((()=>setup({action:action})))))));for(const key in setupStore){const prop=setupStore[key];if(isRef(prop)&&!isComputed(prop)||isReactive(prop)){if(!isOptionsStore){if(initialState&&shouldHydrate(prop)){if(isRef(prop)){prop.value=initialState[key]}else{mergeReactiveObjects(prop,initialState[key])}}{pinia.state.value[$id][key]=prop}}}else if(typeof prop==="function"){const actionValue=action(prop,key);{setupStore[key]=actionValue}optionsForPlugin.actions[key]=prop}else;}{assign$1(store,setupStore);assign$1(toRaw(store),setupStore)}Object.defineProperty(store,"$state",{get:()=>pinia.state.value[$id],set:state=>{$patch(($state=>{assign$1($state,state)}))}});pinia._p.forEach((extender=>{{assign$1(store,scope.run((()=>extender({store:store,app:pinia._a,pinia:pinia,options:optionsForPlugin}))))}}));if(initialState&&isOptionsStore&&options.hydrate){options.hydrate(store.$state,initialState)}isListening=true;isSyncListening=true;return store}function defineStore(idOrOptions,setup,setupOptions){let id2;let options;const isSetupStore=typeof setup==="function";if(typeof idOrOptions==="string"){id2=idOrOptions;options=isSetupStore?setupOptions:setup}else{options=idOrOptions;id2=idOrOptions.id}function useStore(pinia,hot){const hasContext=hasInjectionContext();pinia=pinia||(hasContext?inject(piniaSymbol,null):null);if(pinia)setActivePinia(pinia);pinia=activePinia;if(!pinia._s.has(id2)){if(isSetupStore){createSetupStore(id2,setup,options,pinia)}else{createOptionsStore(id2,options,pinia)}}const store=pinia._s.get(id2);return store}useStore.$id=id2;return useStore}let mapStoreSuffix="Store";function setMapStoreSuffix(suffix){mapStoreSuffix=suffix}function mapStores(...stores){return stores.reduce(((reduced,useStore)=>{reduced[useStore.$id+mapStoreSuffix]=function(){return useStore(this.$pinia)};return reduced}),{})}function mapState(useStore,keysOrMapper){return Array.isArray(keysOrMapper)?keysOrMapper.reduce(((reduced,key)=>{reduced[key]=function(){return useStore(this.$pinia)[key]};return reduced}),{}):Object.keys(keysOrMapper).reduce(((reduced,key)=>{reduced[key]=function(){const store=useStore(this.$pinia);const storeKey=keysOrMapper[key];return typeof storeKey==="function"?storeKey.call(this,store):store[storeKey]};return reduced}),{})}const mapGetters=mapState;function mapActions(useStore,keysOrMapper){return Array.isArray(keysOrMapper)?keysOrMapper.reduce(((reduced,key)=>{reduced[key]=function(...args){return useStore(this.$pinia)[key](...args)};return reduced}),{}):Object.keys(keysOrMapper).reduce(((reduced,key)=>{reduced[key]=function(...args){return useStore(this.$pinia)[keysOrMapper[key]](...args)};return reduced}),{})}function mapWritableState(useStore,keysOrMapper){return Array.isArray(keysOrMapper)?keysOrMapper.reduce(((reduced,key)=>{reduced[key]={get(){return useStore(this.$pinia)[key]},set(value){return useStore(this.$pinia)[key]=value}};return reduced}),{}):Object.keys(keysOrMapper).reduce(((reduced,key)=>{reduced[key]={get(){return useStore(this.$pinia)[keysOrMapper[key]]},set(value){return useStore(this.$pinia)[keysOrMapper[key]]=value}};return reduced}),{})}function storeToRefs(store){{store=toRaw(store);const refs={};for(const key in store){const value=store[key];if(isRef(value)||isReactive(value)){refs[key]=toRef(store,key)}}return refs}}const PiniaVuePlugin=function(_Vue){_Vue.mixin({beforeCreate(){const options=this.$options;if(options.pinia){const pinia=options.pinia;if(!this._provided){const provideCache={};Object.defineProperty(this,"_provided",{get:()=>provideCache,set:v2=>Object.assign(provideCache,v2)})}this._provided[piniaSymbol]=pinia;if(!this.$pinia){this.$pinia=pinia}pinia._a=this;if(IS_CLIENT){setActivePinia(pinia)}}else if(!this.$pinia&&options.parent&&options.parent.$pinia){this.$pinia=options.parent.$pinia}},destroyed(){delete this._pStores}})};const Pinia=Object.freeze(Object.defineProperty({__proto__:null,get MutationType(){return MutationType},PiniaVuePlugin:PiniaVuePlugin,acceptHMRUpdate:acceptHMRUpdate,createPinia:createPinia,defineStore:defineStore,disposePinia:disposePinia,getActivePinia:getActivePinia,mapActions:mapActions,mapGetters:mapGetters,mapState:mapState,mapStores:mapStores,mapWritableState:mapWritableState,setActivePinia:setActivePinia,setMapStoreSuffix:setMapStoreSuffix,skipHydrate:skipHydrate,storeToRefs:storeToRefs},Symbol.toStringTag,{value:"Module"}));var commonjsGlobal=typeof globalThis!=="undefined"?globalThis:typeof window!=="undefined"?window:typeof global!=="undefined"?global:typeof self!=="undefined"?self:{};function getDefaultExportFromCjs(x2){return x2&&x2.__esModule&&Object.prototype.hasOwnProperty.call(x2,"default")?x2["default"]:x2}function getAugmentedNamespace(n2){if(n2.__esModule)return n2;var f2=n2.default;if(typeof f2=="function"){var a2=function a3(){if(this instanceof a3){return Reflect.construct(f2,arguments,this.constructor)}return f2.apply(this,arguments)};a2.prototype=f2.prototype}else a2={};Object.defineProperty(a2,"__esModule",{value:true});Object.keys(n2).forEach((function(k2){var d2=Object.getOwnPropertyDescriptor(n2,k2);Object.defineProperty(a2,k2,d2.get?d2:{enumerable:true,get:function(){return n2[k2]}})}));return a2}var cryptoJs={exports:{}};function commonjsRequire(path){throw new Error('Could not dynamically require "'+path+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var core={exports:{}};const __viteBrowserExternal={};const __viteBrowserExternal$1=Object.freeze(Object.defineProperty({__proto__:null,default:__viteBrowserExternal},Symbol.toStringTag,{value:"Module"}));const require$$0=getAugmentedNamespace(__viteBrowserExternal$1);var hasRequiredCore;function requireCore(){if(hasRequiredCore)return core.exports;hasRequiredCore=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory()}})(commonjsGlobal,(function(){var CryptoJS2=CryptoJS2||function(Math2,undefined$1){var crypto;if(typeof window!=="undefined"&&window.crypto){crypto=window.crypto}if(typeof self!=="undefined"&&self.crypto){crypto=self.crypto}if(typeof globalThis!=="undefined"&&globalThis.crypto){crypto=globalThis.crypto}if(!crypto&&typeof window!=="undefined"&&window.msCrypto){crypto=window.msCrypto}if(!crypto&&typeof commonjsGlobal!=="undefined"&&commonjsGlobal.crypto){crypto=commonjsGlobal.crypto}if(!crypto&&typeof commonjsRequire==="function"){try{crypto=require$$0}catch(err){}}var cryptoSecureRandomInt=function(){if(crypto){if(typeof crypto.getRandomValues==="function"){try{return crypto.getRandomValues(new Uint32Array(1))[0]}catch(err){}}if(typeof crypto.randomBytes==="function"){try{return crypto.randomBytes(4).readInt32LE()}catch(err){}}}throw new Error("Native crypto module could not be used to get secure random number.")};var create=Object.create||function(){function F2(){}return function(obj){var subtype;F2.prototype=obj;subtype=new F2;F2.prototype=null;return subtype}}();var C2={};var C_lib=C2.lib={};var Base=C_lib.Base=function(){return{extend:function(overrides){var subtype=create(this);if(overrides){subtype.mixIn(overrides)}if(!subtype.hasOwnProperty("init")||this.init===subtype.init){subtype.init=function(){subtype.$super.init.apply(this,arguments)}}subtype.init.prototype=subtype;subtype.$super=this;return subtype},create:function(){var instance=this.extend();instance.init.apply(instance,arguments);return instance},init:function(){},mixIn:function(properties){for(var propertyName in properties){if(properties.hasOwnProperty(propertyName)){this[propertyName]=properties[propertyName]}}if(properties.hasOwnProperty("toString")){this.toString=properties.toString}},clone:function(){return this.init.prototype.extend(this)}}}();var WordArray=C_lib.WordArray=Base.extend({init:function(words,sigBytes){words=this.words=words||[];if(sigBytes!=undefined$1){this.sigBytes=sigBytes}else{this.sigBytes=words.length*4}},toString:function(encoder){return(encoder||Hex).stringify(this)},concat:function(wordArray){var thisWords=this.words;var thatWords=wordArray.words;var thisSigBytes=this.sigBytes;var thatSigBytes=wordArray.sigBytes;this.clamp();if(thisSigBytes%4){for(var i2=0;i2<thatSigBytes;i2++){var thatByte=thatWords[i2>>>2]>>>24-i2%4*8&255;thisWords[thisSigBytes+i2>>>2]|=thatByte<<24-(thisSigBytes+i2)%4*8}}else{for(var j2=0;j2<thatSigBytes;j2+=4){thisWords[thisSigBytes+j2>>>2]=thatWords[j2>>>2]}}this.sigBytes+=thatSigBytes;return this},clamp:function(){var words=this.words;var sigBytes=this.sigBytes;words[sigBytes>>>2]&=4294967295<<32-sigBytes%4*8;words.length=Math2.ceil(sigBytes/4)},clone:function(){var clone2=Base.clone.call(this);clone2.words=this.words.slice(0);return clone2},random:function(nBytes){var words=[];for(var i2=0;i2<nBytes;i2+=4){words.push(cryptoSecureRandomInt())}return new WordArray.init(words,nBytes)}});var C_enc=C2.enc={};var Hex=C_enc.Hex={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var hexChars=[];for(var i2=0;i2<sigBytes;i2++){var bite=words[i2>>>2]>>>24-i2%4*8&255;hexChars.push((bite>>>4).toString(16));hexChars.push((bite&15).toString(16))}return hexChars.join("")},parse:function(hexStr){var hexStrLength=hexStr.length;var words=[];for(var i2=0;i2<hexStrLength;i2+=2){words[i2>>>3]|=parseInt(hexStr.substr(i2,2),16)<<24-i2%8*4}return new WordArray.init(words,hexStrLength/2)}};var Latin1=C_enc.Latin1={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var latin1Chars=[];for(var i2=0;i2<sigBytes;i2++){var bite=words[i2>>>2]>>>24-i2%4*8&255;latin1Chars.push(String.fromCharCode(bite))}return latin1Chars.join("")},parse:function(latin1Str){var latin1StrLength=latin1Str.length;var words=[];for(var i2=0;i2<latin1StrLength;i2++){words[i2>>>2]|=(latin1Str.charCodeAt(i2)&255)<<24-i2%4*8}return new WordArray.init(words,latin1StrLength)}};var Utf8=C_enc.Utf8={stringify:function(wordArray){try{return decodeURIComponent(escape(Latin1.stringify(wordArray)))}catch(e2){throw new Error("Malformed UTF-8 data")}},parse:function(utf8Str){return Latin1.parse(unescape(encodeURIComponent(utf8Str)))}};var BufferedBlockAlgorithm=C_lib.BufferedBlockAlgorithm=Base.extend({reset:function(){this._data=new WordArray.init;this._nDataBytes=0},_append:function(data){if(typeof data=="string"){data=Utf8.parse(data)}this._data.concat(data);this._nDataBytes+=data.sigBytes},_process:function(doFlush){var processedWords;var data=this._data;var dataWords=data.words;var dataSigBytes=data.sigBytes;var blockSize=this.blockSize;var blockSizeBytes=blockSize*4;var nBlocksReady=dataSigBytes/blockSizeBytes;if(doFlush){nBlocksReady=Math2.ceil(nBlocksReady)}else{nBlocksReady=Math2.max((nBlocksReady|0)-this._minBufferSize,0)}var nWordsReady=nBlocksReady*blockSize;var nBytesReady=Math2.min(nWordsReady*4,dataSigBytes);if(nWordsReady){for(var offset=0;offset<nWordsReady;offset+=blockSize){this._doProcessBlock(dataWords,offset)}processedWords=dataWords.splice(0,nWordsReady);data.sigBytes-=nBytesReady}return new WordArray.init(processedWords,nBytesReady)},clone:function(){var clone2=Base.clone.call(this);clone2._data=this._data.clone();return clone2},_minBufferSize:0});C_lib.Hasher=BufferedBlockAlgorithm.extend({cfg:Base.extend(),init:function(cfg){this.cfg=this.cfg.extend(cfg);this.reset()},reset:function(){BufferedBlockAlgorithm.reset.call(this);this._doReset()},update:function(messageUpdate){this._append(messageUpdate);this._process();return this},finalize:function(messageUpdate){if(messageUpdate){this._append(messageUpdate)}var hash=this._doFinalize();return hash},blockSize:512/32,_createHelper:function(hasher){return function(message,cfg){return new hasher.init(cfg).finalize(message)}},_createHmacHelper:function(hasher){return function(message,key){return new C_algo.HMAC.init(hasher,key).finalize(message)}}});var C_algo=C2.algo={};return C2}(Math);return CryptoJS2}))})(core);return core.exports}var x64Core={exports:{}};var hasRequiredX64Core;function requireX64Core(){if(hasRequiredX64Core)return x64Core.exports;hasRequiredX64Core=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(undefined$1){var C2=CryptoJS2;var C_lib=C2.lib;var Base=C_lib.Base;var X32WordArray=C_lib.WordArray;var C_x64=C2.x64={};C_x64.Word=Base.extend({init:function(high,low){this.high=high;this.low=low}});C_x64.WordArray=Base.extend({init:function(words,sigBytes){words=this.words=words||[];if(sigBytes!=undefined$1){this.sigBytes=sigBytes}else{this.sigBytes=words.length*8}},toX32:function(){var x64Words=this.words;var x64WordsLength=x64Words.length;var x32Words=[];for(var i2=0;i2<x64WordsLength;i2++){var x64Word=x64Words[i2];x32Words.push(x64Word.high);x32Words.push(x64Word.low)}return X32WordArray.create(x32Words,this.sigBytes)},clone:function(){var clone2=Base.clone.call(this);var words=clone2.words=this.words.slice(0);var wordsLength=words.length;for(var i2=0;i2<wordsLength;i2++){words[i2]=words[i2].clone()}return clone2}})})();return CryptoJS2}))})(x64Core);return x64Core.exports}var libTypedarrays={exports:{}};var hasRequiredLibTypedarrays;function requireLibTypedarrays(){if(hasRequiredLibTypedarrays)return libTypedarrays.exports;hasRequiredLibTypedarrays=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){if(typeof ArrayBuffer!="function"){return}var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var superInit=WordArray.init;var subInit=WordArray.init=function(typedArray){if(typedArray instanceof ArrayBuffer){typedArray=new Uint8Array(typedArray)}if(typedArray instanceof Int8Array||typeof Uint8ClampedArray!=="undefined"&&typedArray instanceof Uint8ClampedArray||typedArray instanceof Int16Array||typedArray instanceof Uint16Array||typedArray instanceof Int32Array||typedArray instanceof Uint32Array||typedArray instanceof Float32Array||typedArray instanceof Float64Array){typedArray=new Uint8Array(typedArray.buffer,typedArray.byteOffset,typedArray.byteLength)}if(typedArray instanceof Uint8Array){var typedArrayByteLength=typedArray.byteLength;var words=[];for(var i2=0;i2<typedArrayByteLength;i2++){words[i2>>>2]|=typedArray[i2]<<24-i2%4*8}superInit.call(this,words,typedArrayByteLength)}else{superInit.apply(this,arguments)}};subInit.prototype=WordArray})();return CryptoJS2.lib.WordArray}))})(libTypedarrays);return libTypedarrays.exports}var encUtf16={exports:{}};var hasRequiredEncUtf16;function requireEncUtf16(){if(hasRequiredEncUtf16)return encUtf16.exports;hasRequiredEncUtf16=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var C_enc=C2.enc;C_enc.Utf16=C_enc.Utf16BE={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var utf16Chars=[];for(var i2=0;i2<sigBytes;i2+=2){var codePoint=words[i2>>>2]>>>16-i2%4*8&65535;utf16Chars.push(String.fromCharCode(codePoint))}return utf16Chars.join("")},parse:function(utf16Str){var utf16StrLength=utf16Str.length;var words=[];for(var i2=0;i2<utf16StrLength;i2++){words[i2>>>1]|=utf16Str.charCodeAt(i2)<<16-i2%2*16}return WordArray.create(words,utf16StrLength*2)}};C_enc.Utf16LE={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var utf16Chars=[];for(var i2=0;i2<sigBytes;i2+=2){var codePoint=swapEndian(words[i2>>>2]>>>16-i2%4*8&65535);utf16Chars.push(String.fromCharCode(codePoint))}return utf16Chars.join("")},parse:function(utf16Str){var utf16StrLength=utf16Str.length;var words=[];for(var i2=0;i2<utf16StrLength;i2++){words[i2>>>1]|=swapEndian(utf16Str.charCodeAt(i2)<<16-i2%2*16)}return WordArray.create(words,utf16StrLength*2)}};function swapEndian(word){return word<<8&4278255360|word>>>8&16711935}})();return CryptoJS2.enc.Utf16}))})(encUtf16);return encUtf16.exports}var encBase64={exports:{}};var hasRequiredEncBase64;function requireEncBase64(){if(hasRequiredEncBase64)return encBase64.exports;hasRequiredEncBase64=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var C_enc=C2.enc;C_enc.Base64={stringify:function(wordArray){var words=wordArray.words;var sigBytes=wordArray.sigBytes;var map=this._map;wordArray.clamp();var base64Chars=[];for(var i2=0;i2<sigBytes;i2+=3){var byte1=words[i2>>>2]>>>24-i2%4*8&255;var byte2=words[i2+1>>>2]>>>24-(i2+1)%4*8&255;var byte3=words[i2+2>>>2]>>>24-(i2+2)%4*8&255;var triplet=byte1<<16|byte2<<8|byte3;for(var j2=0;j2<4&&i2+j2*.75<sigBytes;j2++){base64Chars.push(map.charAt(triplet>>>6*(3-j2)&63))}}var paddingChar=map.charAt(64);if(paddingChar){while(base64Chars.length%4){base64Chars.push(paddingChar)}}return base64Chars.join("")},parse:function(base64Str){var base64StrLength=base64Str.length;var map=this._map;var reverseMap=this._reverseMap;if(!reverseMap){reverseMap=this._reverseMap=[];for(var j2=0;j2<map.length;j2++){reverseMap[map.charCodeAt(j2)]=j2}}var paddingChar=map.charAt(64);if(paddingChar){var paddingIndex=base64Str.indexOf(paddingChar);if(paddingIndex!==-1){base64StrLength=paddingIndex}}return parseLoop(base64Str,base64StrLength,reverseMap)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function parseLoop(base64Str,base64StrLength,reverseMap){var words=[];var nBytes=0;for(var i2=0;i2<base64StrLength;i2++){if(i2%4){var bits1=reverseMap[base64Str.charCodeAt(i2-1)]<<i2%4*2;var bits2=reverseMap[base64Str.charCodeAt(i2)]>>>6-i2%4*2;var bitsCombined=bits1|bits2;words[nBytes>>>2]|=bitsCombined<<24-nBytes%4*8;nBytes++}}return WordArray.create(words,nBytes)}})();return CryptoJS2.enc.Base64}))})(encBase64);return encBase64.exports}var encBase64url={exports:{}};var hasRequiredEncBase64url;function requireEncBase64url(){if(hasRequiredEncBase64url)return encBase64url.exports;hasRequiredEncBase64url=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var C_enc=C2.enc;C_enc.Base64url={stringify:function(wordArray,urlSafe){if(urlSafe===void 0){urlSafe=true}var words=wordArray.words;var sigBytes=wordArray.sigBytes;var map=urlSafe?this._safe_map:this._map;wordArray.clamp();var base64Chars=[];for(var i2=0;i2<sigBytes;i2+=3){var byte1=words[i2>>>2]>>>24-i2%4*8&255;var byte2=words[i2+1>>>2]>>>24-(i2+1)%4*8&255;var byte3=words[i2+2>>>2]>>>24-(i2+2)%4*8&255;var triplet=byte1<<16|byte2<<8|byte3;for(var j2=0;j2<4&&i2+j2*.75<sigBytes;j2++){base64Chars.push(map.charAt(triplet>>>6*(3-j2)&63))}}var paddingChar=map.charAt(64);if(paddingChar){while(base64Chars.length%4){base64Chars.push(paddingChar)}}return base64Chars.join("")},parse:function(base64Str,urlSafe){if(urlSafe===void 0){urlSafe=true}var base64StrLength=base64Str.length;var map=urlSafe?this._safe_map:this._map;var reverseMap=this._reverseMap;if(!reverseMap){reverseMap=this._reverseMap=[];for(var j2=0;j2<map.length;j2++){reverseMap[map.charCodeAt(j2)]=j2}}var paddingChar=map.charAt(64);if(paddingChar){var paddingIndex=base64Str.indexOf(paddingChar);if(paddingIndex!==-1){base64StrLength=paddingIndex}}return parseLoop(base64Str,base64StrLength,reverseMap)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"};function parseLoop(base64Str,base64StrLength,reverseMap){var words=[];var nBytes=0;for(var i2=0;i2<base64StrLength;i2++){if(i2%4){var bits1=reverseMap[base64Str.charCodeAt(i2-1)]<<i2%4*2;var bits2=reverseMap[base64Str.charCodeAt(i2)]>>>6-i2%4*2;var bitsCombined=bits1|bits2;words[nBytes>>>2]|=bitsCombined<<24-nBytes%4*8;nBytes++}}return WordArray.create(words,nBytes)}})();return CryptoJS2.enc.Base64url}))})(encBase64url);return encBase64url.exports}var md5={exports:{}};var hasRequiredMd5;function requireMd5(){if(hasRequiredMd5)return md5.exports;hasRequiredMd5=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(Math2){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_algo=C2.algo;var T2=[];(function(){for(var i2=0;i2<64;i2++){T2[i2]=Math2.abs(Math2.sin(i2+1))*4294967296|0}})();var MD5=C_algo.MD5=Hasher.extend({_doReset:function(){this._hash=new WordArray.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(M2,offset){for(var i2=0;i2<16;i2++){var offset_i=offset+i2;var M_offset_i=M2[offset_i];M2[offset_i]=(M_offset_i<<8|M_offset_i>>>24)&16711935|(M_offset_i<<24|M_offset_i>>>8)&4278255360}var H2=this._hash.words;var M_offset_0=M2[offset+0];var M_offset_1=M2[offset+1];var M_offset_2=M2[offset+2];var M_offset_3=M2[offset+3];var M_offset_4=M2[offset+4];var M_offset_5=M2[offset+5];var M_offset_6=M2[offset+6];var M_offset_7=M2[offset+7];var M_offset_8=M2[offset+8];var M_offset_9=M2[offset+9];var M_offset_10=M2[offset+10];var M_offset_11=M2[offset+11];var M_offset_12=M2[offset+12];var M_offset_13=M2[offset+13];var M_offset_14=M2[offset+14];var M_offset_15=M2[offset+15];var a2=H2[0];var b2=H2[1];var c2=H2[2];var d2=H2[3];a2=FF(a2,b2,c2,d2,M_offset_0,7,T2[0]);d2=FF(d2,a2,b2,c2,M_offset_1,12,T2[1]);c2=FF(c2,d2,a2,b2,M_offset_2,17,T2[2]);b2=FF(b2,c2,d2,a2,M_offset_3,22,T2[3]);a2=FF(a2,b2,c2,d2,M_offset_4,7,T2[4]);d2=FF(d2,a2,b2,c2,M_offset_5,12,T2[5]);c2=FF(c2,d2,a2,b2,M_offset_6,17,T2[6]);b2=FF(b2,c2,d2,a2,M_offset_7,22,T2[7]);a2=FF(a2,b2,c2,d2,M_offset_8,7,T2[8]);d2=FF(d2,a2,b2,c2,M_offset_9,12,T2[9]);c2=FF(c2,d2,a2,b2,M_offset_10,17,T2[10]);b2=FF(b2,c2,d2,a2,M_offset_11,22,T2[11]);a2=FF(a2,b2,c2,d2,M_offset_12,7,T2[12]);d2=FF(d2,a2,b2,c2,M_offset_13,12,T2[13]);c2=FF(c2,d2,a2,b2,M_offset_14,17,T2[14]);b2=FF(b2,c2,d2,a2,M_offset_15,22,T2[15]);a2=GG(a2,b2,c2,d2,M_offset_1,5,T2[16]);d2=GG(d2,a2,b2,c2,M_offset_6,9,T2[17]);c2=GG(c2,d2,a2,b2,M_offset_11,14,T2[18]);b2=GG(b2,c2,d2,a2,M_offset_0,20,T2[19]);a2=GG(a2,b2,c2,d2,M_offset_5,5,T2[20]);d2=GG(d2,a2,b2,c2,M_offset_10,9,T2[21]);c2=GG(c2,d2,a2,b2,M_offset_15,14,T2[22]);b2=GG(b2,c2,d2,a2,M_offset_4,20,T2[23]);a2=GG(a2,b2,c2,d2,M_offset_9,5,T2[24]);d2=GG(d2,a2,b2,c2,M_offset_14,9,T2[25]);c2=GG(c2,d2,a2,b2,M_offset_3,14,T2[26]);b2=GG(b2,c2,d2,a2,M_offset_8,20,T2[27]);a2=GG(a2,b2,c2,d2,M_offset_13,5,T2[28]);d2=GG(d2,a2,b2,c2,M_offset_2,9,T2[29]);c2=GG(c2,d2,a2,b2,M_offset_7,14,T2[30]);b2=GG(b2,c2,d2,a2,M_offset_12,20,T2[31]);a2=HH(a2,b2,c2,d2,M_offset_5,4,T2[32]);d2=HH(d2,a2,b2,c2,M_offset_8,11,T2[33]);c2=HH(c2,d2,a2,b2,M_offset_11,16,T2[34]);b2=HH(b2,c2,d2,a2,M_offset_14,23,T2[35]);a2=HH(a2,b2,c2,d2,M_offset_1,4,T2[36]);d2=HH(d2,a2,b2,c2,M_offset_4,11,T2[37]);c2=HH(c2,d2,a2,b2,M_offset_7,16,T2[38]);b2=HH(b2,c2,d2,a2,M_offset_10,23,T2[39]);a2=HH(a2,b2,c2,d2,M_offset_13,4,T2[40]);d2=HH(d2,a2,b2,c2,M_offset_0,11,T2[41]);c2=HH(c2,d2,a2,b2,M_offset_3,16,T2[42]);b2=HH(b2,c2,d2,a2,M_offset_6,23,T2[43]);a2=HH(a2,b2,c2,d2,M_offset_9,4,T2[44]);d2=HH(d2,a2,b2,c2,M_offset_12,11,T2[45]);c2=HH(c2,d2,a2,b2,M_offset_15,16,T2[46]);b2=HH(b2,c2,d2,a2,M_offset_2,23,T2[47]);a2=II(a2,b2,c2,d2,M_offset_0,6,T2[48]);d2=II(d2,a2,b2,c2,M_offset_7,10,T2[49]);c2=II(c2,d2,a2,b2,M_offset_14,15,T2[50]);b2=II(b2,c2,d2,a2,M_offset_5,21,T2[51]);a2=II(a2,b2,c2,d2,M_offset_12,6,T2[52]);d2=II(d2,a2,b2,c2,M_offset_3,10,T2[53]);c2=II(c2,d2,a2,b2,M_offset_10,15,T2[54]);b2=II(b2,c2,d2,a2,M_offset_1,21,T2[55]);a2=II(a2,b2,c2,d2,M_offset_8,6,T2[56]);d2=II(d2,a2,b2,c2,M_offset_15,10,T2[57]);c2=II(c2,d2,a2,b2,M_offset_6,15,T2[58]);b2=II(b2,c2,d2,a2,M_offset_13,21,T2[59]);a2=II(a2,b2,c2,d2,M_offset_4,6,T2[60]);d2=II(d2,a2,b2,c2,M_offset_11,10,T2[61]);c2=II(c2,d2,a2,b2,M_offset_2,15,T2[62]);b2=II(b2,c2,d2,a2,M_offset_9,21,T2[63]);H2[0]=H2[0]+a2|0;H2[1]=H2[1]+b2|0;H2[2]=H2[2]+c2|0;H2[3]=H2[3]+d2|0},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<24-nBitsLeft%32;var nBitsTotalH=Math2.floor(nBitsTotal/4294967296);var nBitsTotalL=nBitsTotal;dataWords[(nBitsLeft+64>>>9<<4)+15]=(nBitsTotalH<<8|nBitsTotalH>>>24)&16711935|(nBitsTotalH<<24|nBitsTotalH>>>8)&4278255360;dataWords[(nBitsLeft+64>>>9<<4)+14]=(nBitsTotalL<<8|nBitsTotalL>>>24)&16711935|(nBitsTotalL<<24|nBitsTotalL>>>8)&4278255360;data.sigBytes=(dataWords.length+1)*4;this._process();var hash=this._hash;var H2=hash.words;for(var i2=0;i2<4;i2++){var H_i=H2[i2];H2[i2]=(H_i<<8|H_i>>>24)&16711935|(H_i<<24|H_i>>>8)&4278255360}return hash},clone:function(){var clone2=Hasher.clone.call(this);clone2._hash=this._hash.clone();return clone2}});function FF(a2,b2,c2,d2,x2,s2,t2){var n2=a2+(b2&c2|~b2&d2)+x2+t2;return(n2<<s2|n2>>>32-s2)+b2}function GG(a2,b2,c2,d2,x2,s2,t2){var n2=a2+(b2&d2|c2&~d2)+x2+t2;return(n2<<s2|n2>>>32-s2)+b2}function HH(a2,b2,c2,d2,x2,s2,t2){var n2=a2+(b2^c2^d2)+x2+t2;return(n2<<s2|n2>>>32-s2)+b2}function II(a2,b2,c2,d2,x2,s2,t2){var n2=a2+(c2^(b2|~d2))+x2+t2;return(n2<<s2|n2>>>32-s2)+b2}C2.MD5=Hasher._createHelper(MD5);C2.HmacMD5=Hasher._createHmacHelper(MD5)})(Math);return CryptoJS2.MD5}))})(md5);return md5.exports}var sha1={exports:{}};var hasRequiredSha1;function requireSha1(){if(hasRequiredSha1)return sha1.exports;hasRequiredSha1=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_algo=C2.algo;var W2=[];var SHA1=C_algo.SHA1=Hasher.extend({_doReset:function(){this._hash=new WordArray.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(M2,offset){var H2=this._hash.words;var a2=H2[0];var b2=H2[1];var c2=H2[2];var d2=H2[3];var e2=H2[4];for(var i2=0;i2<80;i2++){if(i2<16){W2[i2]=M2[offset+i2]|0}else{var n2=W2[i2-3]^W2[i2-8]^W2[i2-14]^W2[i2-16];W2[i2]=n2<<1|n2>>>31}var t2=(a2<<5|a2>>>27)+e2+W2[i2];if(i2<20){t2+=(b2&c2|~b2&d2)+1518500249}else if(i2<40){t2+=(b2^c2^d2)+1859775393}else if(i2<60){t2+=(b2&c2|b2&d2|c2&d2)-1894007588}else{t2+=(b2^c2^d2)-899497514}e2=d2;d2=c2;c2=b2<<30|b2>>>2;b2=a2;a2=t2}H2[0]=H2[0]+a2|0;H2[1]=H2[1]+b2|0;H2[2]=H2[2]+c2|0;H2[3]=H2[3]+d2|0;H2[4]=H2[4]+e2|0},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<24-nBitsLeft%32;dataWords[(nBitsLeft+64>>>9<<4)+14]=Math.floor(nBitsTotal/4294967296);dataWords[(nBitsLeft+64>>>9<<4)+15]=nBitsTotal;data.sigBytes=dataWords.length*4;this._process();return this._hash},clone:function(){var clone2=Hasher.clone.call(this);clone2._hash=this._hash.clone();return clone2}});C2.SHA1=Hasher._createHelper(SHA1);C2.HmacSHA1=Hasher._createHmacHelper(SHA1)})();return CryptoJS2.SHA1}))})(sha1);return sha1.exports}var sha256={exports:{}};var hasRequiredSha256;function requireSha256(){if(hasRequiredSha256)return sha256.exports;hasRequiredSha256=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(Math2){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_algo=C2.algo;var H2=[];var K2=[];(function(){function isPrime(n3){var sqrtN=Math2.sqrt(n3);for(var factor=2;factor<=sqrtN;factor++){if(!(n3%factor)){return false}}return true}function getFractionalBits(n3){return(n3-(n3|0))*4294967296|0}var n2=2;var nPrime=0;while(nPrime<64){if(isPrime(n2)){if(nPrime<8){H2[nPrime]=getFractionalBits(Math2.pow(n2,1/2))}K2[nPrime]=getFractionalBits(Math2.pow(n2,1/3));nPrime++}n2++}})();var W2=[];var SHA256=C_algo.SHA256=Hasher.extend({_doReset:function(){this._hash=new WordArray.init(H2.slice(0))},_doProcessBlock:function(M2,offset){var H3=this._hash.words;var a2=H3[0];var b2=H3[1];var c2=H3[2];var d2=H3[3];var e2=H3[4];var f2=H3[5];var g2=H3[6];var h2=H3[7];for(var i2=0;i2<64;i2++){if(i2<16){W2[i2]=M2[offset+i2]|0}else{var gamma0x=W2[i2-15];var gamma0=(gamma0x<<25|gamma0x>>>7)^(gamma0x<<14|gamma0x>>>18)^gamma0x>>>3;var gamma1x=W2[i2-2];var gamma1=(gamma1x<<15|gamma1x>>>17)^(gamma1x<<13|gamma1x>>>19)^gamma1x>>>10;W2[i2]=gamma0+W2[i2-7]+gamma1+W2[i2-16]}var ch=e2&f2^~e2&g2;var maj=a2&b2^a2&c2^b2&c2;var sigma0=(a2<<30|a2>>>2)^(a2<<19|a2>>>13)^(a2<<10|a2>>>22);var sigma1=(e2<<26|e2>>>6)^(e2<<21|e2>>>11)^(e2<<7|e2>>>25);var t1=h2+sigma1+ch+K2[i2]+W2[i2];var t2=sigma0+maj;h2=g2;g2=f2;f2=e2;e2=d2+t1|0;d2=c2;c2=b2;b2=a2;a2=t1+t2|0}H3[0]=H3[0]+a2|0;H3[1]=H3[1]+b2|0;H3[2]=H3[2]+c2|0;H3[3]=H3[3]+d2|0;H3[4]=H3[4]+e2|0;H3[5]=H3[5]+f2|0;H3[6]=H3[6]+g2|0;H3[7]=H3[7]+h2|0},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<24-nBitsLeft%32;dataWords[(nBitsLeft+64>>>9<<4)+14]=Math2.floor(nBitsTotal/4294967296);dataWords[(nBitsLeft+64>>>9<<4)+15]=nBitsTotal;data.sigBytes=dataWords.length*4;this._process();return this._hash},clone:function(){var clone2=Hasher.clone.call(this);clone2._hash=this._hash.clone();return clone2}});C2.SHA256=Hasher._createHelper(SHA256);C2.HmacSHA256=Hasher._createHmacHelper(SHA256)})(Math);return CryptoJS2.SHA256}))})(sha256);return sha256.exports}var sha224={exports:{}};var hasRequiredSha224;function requireSha224(){if(hasRequiredSha224)return sha224.exports;hasRequiredSha224=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireSha256())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var C_algo=C2.algo;var SHA256=C_algo.SHA256;var SHA224=C_algo.SHA224=SHA256.extend({_doReset:function(){this._hash=new WordArray.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var hash=SHA256._doFinalize.call(this);hash.sigBytes-=4;return hash}});C2.SHA224=SHA256._createHelper(SHA224);C2.HmacSHA224=SHA256._createHmacHelper(SHA224)})();return CryptoJS2.SHA224}))})(sha224);return sha224.exports}var sha512={exports:{}};var hasRequiredSha512;function requireSha512(){if(hasRequiredSha512)return sha512.exports;hasRequiredSha512=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireX64Core())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var Hasher=C_lib.Hasher;var C_x64=C2.x64;var X64Word=C_x64.Word;var X64WordArray=C_x64.WordArray;var C_algo=C2.algo;function X64Word_create(){return X64Word.create.apply(X64Word,arguments)}var K2=[X64Word_create(1116352408,3609767458),X64Word_create(1899447441,602891725),X64Word_create(3049323471,3964484399),X64Word_create(3921009573,2173295548),X64Word_create(961987163,4081628472),X64Word_create(1508970993,3053834265),X64Word_create(2453635748,2937671579),X64Word_create(2870763221,3664609560),X64Word_create(3624381080,2734883394),X64Word_create(310598401,1164996542),X64Word_create(607225278,1323610764),X64Word_create(1426881987,3590304994),X64Word_create(1925078388,4068182383),X64Word_create(2162078206,991336113),X64Word_create(2614888103,633803317),X64Word_create(3248222580,3479774868),X64Word_create(3835390401,2666613458),X64Word_create(4022224774,944711139),X64Word_create(264347078,2341262773),X64Word_create(604807628,2007800933),X64Word_create(770255983,1495990901),X64Word_create(1249150122,1856431235),X64Word_create(1555081692,3175218132),X64Word_create(1996064986,2198950837),X64Word_create(2554220882,3999719339),X64Word_create(2821834349,766784016),X64Word_create(2952996808,2566594879),X64Word_create(3210313671,3203337956),X64Word_create(3336571891,1034457026),X64Word_create(3584528711,2466948901),X64Word_create(113926993,3758326383),X64Word_create(338241895,168717936),X64Word_create(666307205,1188179964),X64Word_create(773529912,1546045734),X64Word_create(1294757372,1522805485),X64Word_create(1396182291,2643833823),X64Word_create(1695183700,2343527390),X64Word_create(1986661051,1014477480),X64Word_create(2177026350,1206759142),X64Word_create(2456956037,344077627),X64Word_create(2730485921,1290863460),X64Word_create(2820302411,3158454273),X64Word_create(3259730800,3505952657),X64Word_create(3345764771,106217008),X64Word_create(3516065817,3606008344),X64Word_create(3600352804,1432725776),X64Word_create(4094571909,1467031594),X64Word_create(275423344,851169720),X64Word_create(430227734,3100823752),X64Word_create(506948616,1363258195),X64Word_create(659060556,3750685593),X64Word_create(883997877,3785050280),X64Word_create(958139571,3318307427),X64Word_create(1322822218,3812723403),X64Word_create(1537002063,2003034995),X64Word_create(1747873779,3602036899),X64Word_create(1955562222,1575990012),X64Word_create(2024104815,1125592928),X64Word_create(2227730452,2716904306),X64Word_create(2361852424,442776044),X64Word_create(2428436474,593698344),X64Word_create(2756734187,3733110249),X64Word_create(3204031479,2999351573),X64Word_create(3329325298,3815920427),X64Word_create(3391569614,3928383900),X64Word_create(3515267271,566280711),X64Word_create(3940187606,3454069534),X64Word_create(4118630271,4000239992),X64Word_create(116418474,1914138554),X64Word_create(174292421,2731055270),X64Word_create(289380356,3203993006),X64Word_create(460393269,320620315),X64Word_create(685471733,587496836),X64Word_create(852142971,1086792851),X64Word_create(1017036298,365543100),X64Word_create(1126000580,2618297676),X64Word_create(1288033470,3409855158),X64Word_create(1501505948,4234509866),X64Word_create(1607167915,987167468),X64Word_create(1816402316,1246189591)];var W2=[];(function(){for(var i2=0;i2<80;i2++){W2[i2]=X64Word_create()}})();var SHA512=C_algo.SHA512=Hasher.extend({_doReset:function(){this._hash=new X64WordArray.init([new X64Word.init(1779033703,4089235720),new X64Word.init(3144134277,2227873595),new X64Word.init(1013904242,4271175723),new X64Word.init(2773480762,1595750129),new X64Word.init(1359893119,2917565137),new X64Word.init(2600822924,725511199),new X64Word.init(528734635,4215389547),new X64Word.init(1541459225,327033209)])},_doProcessBlock:function(M2,offset){var H2=this._hash.words;var H0=H2[0];var H1=H2[1];var H22=H2[2];var H3=H2[3];var H4=H2[4];var H5=H2[5];var H6=H2[6];var H7=H2[7];var H0h=H0.high;var H0l=H0.low;var H1h=H1.high;var H1l=H1.low;var H2h=H22.high;var H2l=H22.low;var H3h=H3.high;var H3l=H3.low;var H4h=H4.high;var H4l=H4.low;var H5h=H5.high;var H5l=H5.low;var H6h=H6.high;var H6l=H6.low;var H7h=H7.high;var H7l=H7.low;var ah=H0h;var al=H0l;var bh=H1h;var bl=H1l;var ch=H2h;var cl=H2l;var dh=H3h;var dl=H3l;var eh=H4h;var el=H4l;var fh=H5h;var fl=H5l;var gh=H6h;var gl=H6l;var hh=H7h;var hl=H7l;for(var i2=0;i2<80;i2++){var Wil;var Wih;var Wi=W2[i2];if(i2<16){Wih=Wi.high=M2[offset+i2*2]|0;Wil=Wi.low=M2[offset+i2*2+1]|0}else{var gamma0x=W2[i2-15];var gamma0xh=gamma0x.high;var gamma0xl=gamma0x.low;var gamma0h=(gamma0xh>>>1|gamma0xl<<31)^(gamma0xh>>>8|gamma0xl<<24)^gamma0xh>>>7;var gamma0l=(gamma0xl>>>1|gamma0xh<<31)^(gamma0xl>>>8|gamma0xh<<24)^(gamma0xl>>>7|gamma0xh<<25);var gamma1x=W2[i2-2];var gamma1xh=gamma1x.high;var gamma1xl=gamma1x.low;var gamma1h=(gamma1xh>>>19|gamma1xl<<13)^(gamma1xh<<3|gamma1xl>>>29)^gamma1xh>>>6;var gamma1l=(gamma1xl>>>19|gamma1xh<<13)^(gamma1xl<<3|gamma1xh>>>29)^(gamma1xl>>>6|gamma1xh<<26);var Wi7=W2[i2-7];var Wi7h=Wi7.high;var Wi7l=Wi7.low;var Wi16=W2[i2-16];var Wi16h=Wi16.high;var Wi16l=Wi16.low;Wil=gamma0l+Wi7l;Wih=gamma0h+Wi7h+(Wil>>>0<gamma0l>>>0?1:0);Wil=Wil+gamma1l;Wih=Wih+gamma1h+(Wil>>>0<gamma1l>>>0?1:0);Wil=Wil+Wi16l;Wih=Wih+Wi16h+(Wil>>>0<Wi16l>>>0?1:0);Wi.high=Wih;Wi.low=Wil}var chh=eh&fh^~eh&gh;var chl=el&fl^~el&gl;var majh=ah&bh^ah&ch^bh&ch;var majl=al&bl^al&cl^bl&cl;var sigma0h=(ah>>>28|al<<4)^(ah<<30|al>>>2)^(ah<<25|al>>>7);var sigma0l=(al>>>28|ah<<4)^(al<<30|ah>>>2)^(al<<25|ah>>>7);var sigma1h=(eh>>>14|el<<18)^(eh>>>18|el<<14)^(eh<<23|el>>>9);var sigma1l=(el>>>14|eh<<18)^(el>>>18|eh<<14)^(el<<23|eh>>>9);var Ki=K2[i2];var Kih=Ki.high;var Kil=Ki.low;var t1l=hl+sigma1l;var t1h=hh+sigma1h+(t1l>>>0<hl>>>0?1:0);var t1l=t1l+chl;var t1h=t1h+chh+(t1l>>>0<chl>>>0?1:0);var t1l=t1l+Kil;var t1h=t1h+Kih+(t1l>>>0<Kil>>>0?1:0);var t1l=t1l+Wil;var t1h=t1h+Wih+(t1l>>>0<Wil>>>0?1:0);var t2l=sigma0l+majl;var t2h=sigma0h+majh+(t2l>>>0<sigma0l>>>0?1:0);hh=gh;hl=gl;gh=fh;gl=fl;fh=eh;fl=el;el=dl+t1l|0;eh=dh+t1h+(el>>>0<dl>>>0?1:0)|0;dh=ch;dl=cl;ch=bh;cl=bl;bh=ah;bl=al;al=t1l+t2l|0;ah=t1h+t2h+(al>>>0<t1l>>>0?1:0)|0}H0l=H0.low=H0l+al;H0.high=H0h+ah+(H0l>>>0<al>>>0?1:0);H1l=H1.low=H1l+bl;H1.high=H1h+bh+(H1l>>>0<bl>>>0?1:0);H2l=H22.low=H2l+cl;H22.high=H2h+ch+(H2l>>>0<cl>>>0?1:0);H3l=H3.low=H3l+dl;H3.high=H3h+dh+(H3l>>>0<dl>>>0?1:0);H4l=H4.low=H4l+el;H4.high=H4h+eh+(H4l>>>0<el>>>0?1:0);H5l=H5.low=H5l+fl;H5.high=H5h+fh+(H5l>>>0<fl>>>0?1:0);H6l=H6.low=H6l+gl;H6.high=H6h+gh+(H6l>>>0<gl>>>0?1:0);H7l=H7.low=H7l+hl;H7.high=H7h+hh+(H7l>>>0<hl>>>0?1:0)},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<24-nBitsLeft%32;dataWords[(nBitsLeft+128>>>10<<5)+30]=Math.floor(nBitsTotal/4294967296);dataWords[(nBitsLeft+128>>>10<<5)+31]=nBitsTotal;data.sigBytes=dataWords.length*4;this._process();var hash=this._hash.toX32();return hash},clone:function(){var clone2=Hasher.clone.call(this);clone2._hash=this._hash.clone();return clone2},blockSize:1024/32});C2.SHA512=Hasher._createHelper(SHA512);C2.HmacSHA512=Hasher._createHmacHelper(SHA512)})();return CryptoJS2.SHA512}))})(sha512);return sha512.exports}var sha384={exports:{}};var hasRequiredSha384;function requireSha384(){if(hasRequiredSha384)return sha384.exports;hasRequiredSha384=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireX64Core(),requireSha512())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_x64=C2.x64;var X64Word=C_x64.Word;var X64WordArray=C_x64.WordArray;var C_algo=C2.algo;var SHA512=C_algo.SHA512;var SHA384=C_algo.SHA384=SHA512.extend({_doReset:function(){this._hash=new X64WordArray.init([new X64Word.init(3418070365,3238371032),new X64Word.init(1654270250,914150663),new X64Word.init(2438529370,812702999),new X64Word.init(355462360,4144912697),new X64Word.init(1731405415,4290775857),new X64Word.init(2394180231,1750603025),new X64Word.init(3675008525,1694076839),new X64Word.init(1203062813,3204075428)])},_doFinalize:function(){var hash=SHA512._doFinalize.call(this);hash.sigBytes-=16;return hash}});C2.SHA384=SHA512._createHelper(SHA384);C2.HmacSHA384=SHA512._createHmacHelper(SHA384)})();return CryptoJS2.SHA384}))})(sha384);return sha384.exports}var sha3={exports:{}};var hasRequiredSha3;function requireSha3(){if(hasRequiredSha3)return sha3.exports;hasRequiredSha3=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireX64Core())}})(commonjsGlobal,(function(CryptoJS2){(function(Math2){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_x64=C2.x64;var X64Word=C_x64.Word;var C_algo=C2.algo;var RHO_OFFSETS=[];var PI_INDEXES=[];var ROUND_CONSTANTS=[];(function(){var x2=1,y2=0;for(var t2=0;t2<24;t2++){RHO_OFFSETS[x2+5*y2]=(t2+1)*(t2+2)/2%64;var newX=y2%5;var newY=(2*x2+3*y2)%5;x2=newX;y2=newY}for(var x2=0;x2<5;x2++){for(var y2=0;y2<5;y2++){PI_INDEXES[x2+5*y2]=y2+(2*x2+3*y2)%5*5}}var LFSR=1;for(var i2=0;i2<24;i2++){var roundConstantMsw=0;var roundConstantLsw=0;for(var j2=0;j2<7;j2++){if(LFSR&1){var bitPosition=(1<<j2)-1;if(bitPosition<32){roundConstantLsw^=1<<bitPosition}else{roundConstantMsw^=1<<bitPosition-32}}if(LFSR&128){LFSR=LFSR<<1^113}else{LFSR<<=1}}ROUND_CONSTANTS[i2]=X64Word.create(roundConstantMsw,roundConstantLsw)}})();var T2=[];(function(){for(var i2=0;i2<25;i2++){T2[i2]=X64Word.create()}})();var SHA3=C_algo.SHA3=Hasher.extend({cfg:Hasher.cfg.extend({outputLength:512}),_doReset:function(){var state=this._state=[];for(var i2=0;i2<25;i2++){state[i2]=new X64Word.init}this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(M2,offset){var state=this._state;var nBlockSizeLanes=this.blockSize/2;for(var i2=0;i2<nBlockSizeLanes;i2++){var M2i=M2[offset+2*i2];var M2i1=M2[offset+2*i2+1];M2i=(M2i<<8|M2i>>>24)&16711935|(M2i<<24|M2i>>>8)&4278255360;M2i1=(M2i1<<8|M2i1>>>24)&16711935|(M2i1<<24|M2i1>>>8)&4278255360;var lane=state[i2];lane.high^=M2i1;lane.low^=M2i}for(var round=0;round<24;round++){for(var x2=0;x2<5;x2++){var tMsw=0,tLsw=0;for(var y2=0;y2<5;y2++){var lane=state[x2+5*y2];tMsw^=lane.high;tLsw^=lane.low}var Tx=T2[x2];Tx.high=tMsw;Tx.low=tLsw}for(var x2=0;x2<5;x2++){var Tx4=T2[(x2+4)%5];var Tx1=T2[(x2+1)%5];var Tx1Msw=Tx1.high;var Tx1Lsw=Tx1.low;var tMsw=Tx4.high^(Tx1Msw<<1|Tx1Lsw>>>31);var tLsw=Tx4.low^(Tx1Lsw<<1|Tx1Msw>>>31);for(var y2=0;y2<5;y2++){var lane=state[x2+5*y2];lane.high^=tMsw;lane.low^=tLsw}}for(var laneIndex=1;laneIndex<25;laneIndex++){var tMsw;var tLsw;var lane=state[laneIndex];var laneMsw=lane.high;var laneLsw=lane.low;var rhoOffset=RHO_OFFSETS[laneIndex];if(rhoOffset<32){tMsw=laneMsw<<rhoOffset|laneLsw>>>32-rhoOffset;tLsw=laneLsw<<rhoOffset|laneMsw>>>32-rhoOffset}else{tMsw=laneLsw<<rhoOffset-32|laneMsw>>>64-rhoOffset;tLsw=laneMsw<<rhoOffset-32|laneLsw>>>64-rhoOffset}var TPiLane=T2[PI_INDEXES[laneIndex]];TPiLane.high=tMsw;TPiLane.low=tLsw}var T0=T2[0];var state0=state[0];T0.high=state0.high;T0.low=state0.low;for(var x2=0;x2<5;x2++){for(var y2=0;y2<5;y2++){var laneIndex=x2+5*y2;var lane=state[laneIndex];var TLane=T2[laneIndex];var Tx1Lane=T2[(x2+1)%5+5*y2];var Tx2Lane=T2[(x2+2)%5+5*y2];lane.high=TLane.high^~Tx1Lane.high&Tx2Lane.high;lane.low=TLane.low^~Tx1Lane.low&Tx2Lane.low}}var lane=state[0];var roundConstant=ROUND_CONSTANTS[round];lane.high^=roundConstant.high;lane.low^=roundConstant.low}},_doFinalize:function(){var data=this._data;var dataWords=data.words;this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;var blockSizeBits=this.blockSize*32;dataWords[nBitsLeft>>>5]|=1<<24-nBitsLeft%32;dataWords[(Math2.ceil((nBitsLeft+1)/blockSizeBits)*blockSizeBits>>>5)-1]|=128;data.sigBytes=dataWords.length*4;this._process();var state=this._state;var outputLengthBytes=this.cfg.outputLength/8;var outputLengthLanes=outputLengthBytes/8;var hashWords=[];for(var i2=0;i2<outputLengthLanes;i2++){var lane=state[i2];var laneMsw=lane.high;var laneLsw=lane.low;laneMsw=(laneMsw<<8|laneMsw>>>24)&16711935|(laneMsw<<24|laneMsw>>>8)&4278255360;laneLsw=(laneLsw<<8|laneLsw>>>24)&16711935|(laneLsw<<24|laneLsw>>>8)&4278255360;hashWords.push(laneLsw);hashWords.push(laneMsw)}return new WordArray.init(hashWords,outputLengthBytes)},clone:function(){var clone2=Hasher.clone.call(this);var state=clone2._state=this._state.slice(0);for(var i2=0;i2<25;i2++){state[i2]=state[i2].clone()}return clone2}});C2.SHA3=Hasher._createHelper(SHA3);C2.HmacSHA3=Hasher._createHmacHelper(SHA3)})(Math);return CryptoJS2.SHA3}))})(sha3);return sha3.exports}var ripemd160={exports:{}};var hasRequiredRipemd160;function requireRipemd160(){if(hasRequiredRipemd160)return ripemd160.exports;hasRequiredRipemd160=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){
/** @preserve
      			(c) 2012 by Cédric Mesnil. All rights reserved.
      
      			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
      
      			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
      			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
      
      			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
      			*/
(function(Math2){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var Hasher=C_lib.Hasher;var C_algo=C2.algo;var _zl=WordArray.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]);var _zr=WordArray.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]);var _sl=WordArray.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]);var _sr=WordArray.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]);var _hl=WordArray.create([0,1518500249,1859775393,2400959708,2840853838]);var _hr=WordArray.create([1352829926,1548603684,1836072691,2053994217,0]);var RIPEMD160=C_algo.RIPEMD160=Hasher.extend({_doReset:function(){this._hash=WordArray.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(M2,offset){for(var i2=0;i2<16;i2++){var offset_i=offset+i2;var M_offset_i=M2[offset_i];M2[offset_i]=(M_offset_i<<8|M_offset_i>>>24)&16711935|(M_offset_i<<24|M_offset_i>>>8)&4278255360}var H2=this._hash.words;var hl=_hl.words;var hr=_hr.words;var zl=_zl.words;var zr=_zr.words;var sl=_sl.words;var sr=_sr.words;var al,bl,cl,dl,el;var ar,br,cr,dr,er;ar=al=H2[0];br=bl=H2[1];cr=cl=H2[2];dr=dl=H2[3];er=el=H2[4];var t2;for(var i2=0;i2<80;i2+=1){t2=al+M2[offset+zl[i2]]|0;if(i2<16){t2+=f1(bl,cl,dl)+hl[0]}else if(i2<32){t2+=f2(bl,cl,dl)+hl[1]}else if(i2<48){t2+=f3(bl,cl,dl)+hl[2]}else if(i2<64){t2+=f4(bl,cl,dl)+hl[3]}else{t2+=f5(bl,cl,dl)+hl[4]}t2=t2|0;t2=rotl(t2,sl[i2]);t2=t2+el|0;al=el;el=dl;dl=rotl(cl,10);cl=bl;bl=t2;t2=ar+M2[offset+zr[i2]]|0;if(i2<16){t2+=f5(br,cr,dr)+hr[0]}else if(i2<32){t2+=f4(br,cr,dr)+hr[1]}else if(i2<48){t2+=f3(br,cr,dr)+hr[2]}else if(i2<64){t2+=f2(br,cr,dr)+hr[3]}else{t2+=f1(br,cr,dr)+hr[4]}t2=t2|0;t2=rotl(t2,sr[i2]);t2=t2+er|0;ar=er;er=dr;dr=rotl(cr,10);cr=br;br=t2}t2=H2[1]+cl+dr|0;H2[1]=H2[2]+dl+er|0;H2[2]=H2[3]+el+ar|0;H2[3]=H2[4]+al+br|0;H2[4]=H2[0]+bl+cr|0;H2[0]=t2},_doFinalize:function(){var data=this._data;var dataWords=data.words;var nBitsTotal=this._nDataBytes*8;var nBitsLeft=data.sigBytes*8;dataWords[nBitsLeft>>>5]|=128<<24-nBitsLeft%32;dataWords[(nBitsLeft+64>>>9<<4)+14]=(nBitsTotal<<8|nBitsTotal>>>24)&16711935|(nBitsTotal<<24|nBitsTotal>>>8)&4278255360;data.sigBytes=(dataWords.length+1)*4;this._process();var hash=this._hash;var H2=hash.words;for(var i2=0;i2<5;i2++){var H_i=H2[i2];H2[i2]=(H_i<<8|H_i>>>24)&16711935|(H_i<<24|H_i>>>8)&4278255360}return hash},clone:function(){var clone2=Hasher.clone.call(this);clone2._hash=this._hash.clone();return clone2}});function f1(x2,y2,z2){return x2^y2^z2}function f2(x2,y2,z2){return x2&y2|~x2&z2}function f3(x2,y2,z2){return(x2|~y2)^z2}function f4(x2,y2,z2){return x2&z2|y2&~z2}function f5(x2,y2,z2){return x2^(y2|~z2)}function rotl(x2,n2){return x2<<n2|x2>>>32-n2}C2.RIPEMD160=Hasher._createHelper(RIPEMD160);C2.HmacRIPEMD160=Hasher._createHmacHelper(RIPEMD160)})();return CryptoJS2.RIPEMD160}))})(ripemd160);return ripemd160.exports}var hmac={exports:{}};var hasRequiredHmac;function requireHmac(){if(hasRequiredHmac)return hmac.exports;hasRequiredHmac=1;(function(module2,exports2){(function(root,factory){{module2.exports=factory(requireCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var Base=C_lib.Base;var C_enc=C2.enc;var Utf8=C_enc.Utf8;var C_algo=C2.algo;C_algo.HMAC=Base.extend({init:function(hasher,key){hasher=this._hasher=new hasher.init;if(typeof key=="string"){key=Utf8.parse(key)}var hasherBlockSize=hasher.blockSize;var hasherBlockSizeBytes=hasherBlockSize*4;if(key.sigBytes>hasherBlockSizeBytes){key=hasher.finalize(key)}key.clamp();var oKey=this._oKey=key.clone();var iKey=this._iKey=key.clone();var oKeyWords=oKey.words;var iKeyWords=iKey.words;for(var i2=0;i2<hasherBlockSize;i2++){oKeyWords[i2]^=1549556828;iKeyWords[i2]^=909522486}oKey.sigBytes=iKey.sigBytes=hasherBlockSizeBytes;this.reset()},reset:function(){var hasher=this._hasher;hasher.reset();hasher.update(this._iKey)},update:function(messageUpdate){this._hasher.update(messageUpdate);return this},finalize:function(messageUpdate){var hasher=this._hasher;var innerHash=hasher.finalize(messageUpdate);hasher.reset();var hmac2=hasher.finalize(this._oKey.clone().concat(innerHash));return hmac2}})})()}))})(hmac);return hmac.exports}var pbkdf2={exports:{}};var hasRequiredPbkdf2;function requirePbkdf2(){if(hasRequiredPbkdf2)return pbkdf2.exports;hasRequiredPbkdf2=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireSha256(),requireHmac())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var Base=C_lib.Base;var WordArray=C_lib.WordArray;var C_algo=C2.algo;var SHA256=C_algo.SHA256;var HMAC=C_algo.HMAC;var PBKDF2=C_algo.PBKDF2=Base.extend({cfg:Base.extend({keySize:128/32,hasher:SHA256,iterations:25e4}),init:function(cfg){this.cfg=this.cfg.extend(cfg)},compute:function(password,salt){var cfg=this.cfg;var hmac2=HMAC.create(cfg.hasher,password);var derivedKey=WordArray.create();var blockIndex=WordArray.create([1]);var derivedKeyWords=derivedKey.words;var blockIndexWords=blockIndex.words;var keySize=cfg.keySize;var iterations=cfg.iterations;while(derivedKeyWords.length<keySize){var block=hmac2.update(salt).finalize(blockIndex);hmac2.reset();var blockWords=block.words;var blockWordsLength=blockWords.length;var intermediate=block;for(var i2=1;i2<iterations;i2++){intermediate=hmac2.finalize(intermediate);hmac2.reset();var intermediateWords=intermediate.words;for(var j2=0;j2<blockWordsLength;j2++){blockWords[j2]^=intermediateWords[j2]}}derivedKey.concat(block);blockIndexWords[0]++}derivedKey.sigBytes=keySize*4;return derivedKey}});C2.PBKDF2=function(password,salt,cfg){return PBKDF2.create(cfg).compute(password,salt)}})();return CryptoJS2.PBKDF2}))})(pbkdf2);return pbkdf2.exports}var evpkdf={exports:{}};var hasRequiredEvpkdf;function requireEvpkdf(){if(hasRequiredEvpkdf)return evpkdf.exports;hasRequiredEvpkdf=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireSha1(),requireHmac())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var Base=C_lib.Base;var WordArray=C_lib.WordArray;var C_algo=C2.algo;var MD5=C_algo.MD5;var EvpKDF=C_algo.EvpKDF=Base.extend({cfg:Base.extend({keySize:128/32,hasher:MD5,iterations:1}),init:function(cfg){this.cfg=this.cfg.extend(cfg)},compute:function(password,salt){var block;var cfg=this.cfg;var hasher=cfg.hasher.create();var derivedKey=WordArray.create();var derivedKeyWords=derivedKey.words;var keySize=cfg.keySize;var iterations=cfg.iterations;while(derivedKeyWords.length<keySize){if(block){hasher.update(block)}block=hasher.update(password).finalize(salt);hasher.reset();for(var i2=1;i2<iterations;i2++){block=hasher.finalize(block);hasher.reset()}derivedKey.concat(block)}derivedKey.sigBytes=keySize*4;return derivedKey}});C2.EvpKDF=function(password,salt,cfg){return EvpKDF.create(cfg).compute(password,salt)}})();return CryptoJS2.EvpKDF}))})(evpkdf);return evpkdf.exports}var cipherCore={exports:{}};var hasRequiredCipherCore;function requireCipherCore(){if(hasRequiredCipherCore)return cipherCore.exports;hasRequiredCipherCore=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireEvpkdf())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.lib.Cipher||function(undefined$1){var C2=CryptoJS2;var C_lib=C2.lib;var Base=C_lib.Base;var WordArray=C_lib.WordArray;var BufferedBlockAlgorithm=C_lib.BufferedBlockAlgorithm;var C_enc=C2.enc;C_enc.Utf8;var Base64=C_enc.Base64;var C_algo=C2.algo;var EvpKDF=C_algo.EvpKDF;var Cipher=C_lib.Cipher=BufferedBlockAlgorithm.extend({cfg:Base.extend(),createEncryptor:function(key,cfg){return this.create(this._ENC_XFORM_MODE,key,cfg)},createDecryptor:function(key,cfg){return this.create(this._DEC_XFORM_MODE,key,cfg)},init:function(xformMode,key,cfg){this.cfg=this.cfg.extend(cfg);this._xformMode=xformMode;this._key=key;this.reset()},reset:function(){BufferedBlockAlgorithm.reset.call(this);this._doReset()},process:function(dataUpdate){this._append(dataUpdate);return this._process()},finalize:function(dataUpdate){if(dataUpdate){this._append(dataUpdate)}var finalProcessedData=this._doFinalize();return finalProcessedData},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function selectCipherStrategy(key){if(typeof key=="string"){return PasswordBasedCipher}else{return SerializableCipher}}return function(cipher){return{encrypt:function(message,key,cfg){return selectCipherStrategy(key).encrypt(cipher,message,key,cfg)},decrypt:function(ciphertext,key,cfg){return selectCipherStrategy(key).decrypt(cipher,ciphertext,key,cfg)}}}}()});C_lib.StreamCipher=Cipher.extend({_doFinalize:function(){var finalProcessedBlocks=this._process(true);return finalProcessedBlocks},blockSize:1});var C_mode=C2.mode={};var BlockCipherMode=C_lib.BlockCipherMode=Base.extend({createEncryptor:function(cipher,iv){return this.Encryptor.create(cipher,iv)},createDecryptor:function(cipher,iv){return this.Decryptor.create(cipher,iv)},init:function(cipher,iv){this._cipher=cipher;this._iv=iv}});var CBC=C_mode.CBC=function(){var CBC2=BlockCipherMode.extend();CBC2.Encryptor=CBC2.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;xorBlock.call(this,words,offset,blockSize);cipher.encryptBlock(words,offset);this._prevBlock=words.slice(offset,offset+blockSize)}});CBC2.Decryptor=CBC2.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var thisBlock=words.slice(offset,offset+blockSize);cipher.decryptBlock(words,offset);xorBlock.call(this,words,offset,blockSize);this._prevBlock=thisBlock}});function xorBlock(words,offset,blockSize){var block;var iv=this._iv;if(iv){block=iv;this._iv=undefined$1}else{block=this._prevBlock}for(var i2=0;i2<blockSize;i2++){words[offset+i2]^=block[i2]}}return CBC2}();var C_pad=C2.pad={};var Pkcs7=C_pad.Pkcs7={pad:function(data,blockSize){var blockSizeBytes=blockSize*4;var nPaddingBytes=blockSizeBytes-data.sigBytes%blockSizeBytes;var paddingWord=nPaddingBytes<<24|nPaddingBytes<<16|nPaddingBytes<<8|nPaddingBytes;var paddingWords=[];for(var i2=0;i2<nPaddingBytes;i2+=4){paddingWords.push(paddingWord)}var padding=WordArray.create(paddingWords,nPaddingBytes);data.concat(padding)},unpad:function(data){var nPaddingBytes=data.words[data.sigBytes-1>>>2]&255;data.sigBytes-=nPaddingBytes}};C_lib.BlockCipher=Cipher.extend({cfg:Cipher.cfg.extend({mode:CBC,padding:Pkcs7}),reset:function(){var modeCreator;Cipher.reset.call(this);var cfg=this.cfg;var iv=cfg.iv;var mode=cfg.mode;if(this._xformMode==this._ENC_XFORM_MODE){modeCreator=mode.createEncryptor}else{modeCreator=mode.createDecryptor;this._minBufferSize=1}if(this._mode&&this._mode.__creator==modeCreator){this._mode.init(this,iv&&iv.words)}else{this._mode=modeCreator.call(mode,this,iv&&iv.words);this._mode.__creator=modeCreator}},_doProcessBlock:function(words,offset){this._mode.processBlock(words,offset)},_doFinalize:function(){var finalProcessedBlocks;var padding=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){padding.pad(this._data,this.blockSize);finalProcessedBlocks=this._process(true)}else{finalProcessedBlocks=this._process(true);padding.unpad(finalProcessedBlocks)}return finalProcessedBlocks},blockSize:128/32});var CipherParams=C_lib.CipherParams=Base.extend({init:function(cipherParams){this.mixIn(cipherParams)},toString:function(formatter){return(formatter||this.formatter).stringify(this)}});var C_format=C2.format={};var OpenSSLFormatter=C_format.OpenSSL={stringify:function(cipherParams){var wordArray;var ciphertext=cipherParams.ciphertext;var salt=cipherParams.salt;if(salt){wordArray=WordArray.create([1398893684,1701076831]).concat(salt).concat(ciphertext)}else{wordArray=ciphertext}return wordArray.toString(Base64)},parse:function(openSSLStr){var salt;var ciphertext=Base64.parse(openSSLStr);var ciphertextWords=ciphertext.words;if(ciphertextWords[0]==1398893684&&ciphertextWords[1]==1701076831){salt=WordArray.create(ciphertextWords.slice(2,4));ciphertextWords.splice(0,4);ciphertext.sigBytes-=16}return CipherParams.create({ciphertext:ciphertext,salt:salt})}};var SerializableCipher=C_lib.SerializableCipher=Base.extend({cfg:Base.extend({format:OpenSSLFormatter}),encrypt:function(cipher,message,key,cfg){cfg=this.cfg.extend(cfg);var encryptor=cipher.createEncryptor(key,cfg);var ciphertext=encryptor.finalize(message);var cipherCfg=encryptor.cfg;return CipherParams.create({ciphertext:ciphertext,key:key,iv:cipherCfg.iv,algorithm:cipher,mode:cipherCfg.mode,padding:cipherCfg.padding,blockSize:cipher.blockSize,formatter:cfg.format})},decrypt:function(cipher,ciphertext,key,cfg){cfg=this.cfg.extend(cfg);ciphertext=this._parse(ciphertext,cfg.format);var plaintext=cipher.createDecryptor(key,cfg).finalize(ciphertext.ciphertext);return plaintext},_parse:function(ciphertext,format){if(typeof ciphertext=="string"){return format.parse(ciphertext,this)}else{return ciphertext}}});var C_kdf=C2.kdf={};var OpenSSLKdf=C_kdf.OpenSSL={execute:function(password,keySize,ivSize,salt,hasher){if(!salt){salt=WordArray.random(64/8)}if(!hasher){var key=EvpKDF.create({keySize:keySize+ivSize}).compute(password,salt)}else{var key=EvpKDF.create({keySize:keySize+ivSize,hasher:hasher}).compute(password,salt)}var iv=WordArray.create(key.words.slice(keySize),ivSize*4);key.sigBytes=keySize*4;return CipherParams.create({key:key,iv:iv,salt:salt})}};var PasswordBasedCipher=C_lib.PasswordBasedCipher=SerializableCipher.extend({cfg:SerializableCipher.cfg.extend({kdf:OpenSSLKdf}),encrypt:function(cipher,message,password,cfg){cfg=this.cfg.extend(cfg);var derivedParams=cfg.kdf.execute(password,cipher.keySize,cipher.ivSize,cfg.salt,cfg.hasher);cfg.iv=derivedParams.iv;var ciphertext=SerializableCipher.encrypt.call(this,cipher,message,derivedParams.key,cfg);ciphertext.mixIn(derivedParams);return ciphertext},decrypt:function(cipher,ciphertext,password,cfg){cfg=this.cfg.extend(cfg);ciphertext=this._parse(ciphertext,cfg.format);var derivedParams=cfg.kdf.execute(password,cipher.keySize,cipher.ivSize,ciphertext.salt,cfg.hasher);cfg.iv=derivedParams.iv;var plaintext=SerializableCipher.decrypt.call(this,cipher,ciphertext,derivedParams.key,cfg);return plaintext}})}()}))})(cipherCore);return cipherCore.exports}var modeCfb={exports:{}};var hasRequiredModeCfb;function requireModeCfb(){if(hasRequiredModeCfb)return modeCfb.exports;hasRequiredModeCfb=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.mode.CFB=function(){var CFB=CryptoJS2.lib.BlockCipherMode.extend();CFB.Encryptor=CFB.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;generateKeystreamAndEncrypt.call(this,words,offset,blockSize,cipher);this._prevBlock=words.slice(offset,offset+blockSize)}});CFB.Decryptor=CFB.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var thisBlock=words.slice(offset,offset+blockSize);generateKeystreamAndEncrypt.call(this,words,offset,blockSize,cipher);this._prevBlock=thisBlock}});function generateKeystreamAndEncrypt(words,offset,blockSize,cipher){var keystream;var iv=this._iv;if(iv){keystream=iv.slice(0);this._iv=void 0}else{keystream=this._prevBlock}cipher.encryptBlock(keystream,0);for(var i2=0;i2<blockSize;i2++){words[offset+i2]^=keystream[i2]}}return CFB}();return CryptoJS2.mode.CFB}))})(modeCfb);return modeCfb.exports}var modeCtr={exports:{}};var hasRequiredModeCtr;function requireModeCtr(){if(hasRequiredModeCtr)return modeCtr.exports;hasRequiredModeCtr=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.mode.CTR=function(){var CTR=CryptoJS2.lib.BlockCipherMode.extend();var Encryptor=CTR.Encryptor=CTR.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var iv=this._iv;var counter=this._counter;if(iv){counter=this._counter=iv.slice(0);this._iv=void 0}var keystream=counter.slice(0);cipher.encryptBlock(keystream,0);counter[blockSize-1]=counter[blockSize-1]+1|0;for(var i2=0;i2<blockSize;i2++){words[offset+i2]^=keystream[i2]}}});CTR.Decryptor=Encryptor;return CTR}();return CryptoJS2.mode.CTR}))})(modeCtr);return modeCtr.exports}var modeCtrGladman={exports:{}};var hasRequiredModeCtrGladman;function requireModeCtrGladman(){if(hasRequiredModeCtrGladman)return modeCtrGladman.exports;hasRequiredModeCtrGladman=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){
/** @preserve
       * Counter block mode compatible with  Dr Brian Gladman fileenc.c
       * derived from CryptoJS.mode.CTR
       * <NAME_EMAIL>
       */
CryptoJS2.mode.CTRGladman=function(){var CTRGladman=CryptoJS2.lib.BlockCipherMode.extend();function incWord(word){if((word>>24&255)===255){var b1=word>>16&255;var b2=word>>8&255;var b3=word&255;if(b1===255){b1=0;if(b2===255){b2=0;if(b3===255){b3=0}else{++b3}}else{++b2}}else{++b1}word=0;word+=b1<<16;word+=b2<<8;word+=b3}else{word+=1<<24}return word}function incCounter(counter){if((counter[0]=incWord(counter[0]))===0){counter[1]=incWord(counter[1])}return counter}var Encryptor=CTRGladman.Encryptor=CTRGladman.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var iv=this._iv;var counter=this._counter;if(iv){counter=this._counter=iv.slice(0);this._iv=void 0}incCounter(counter);var keystream=counter.slice(0);cipher.encryptBlock(keystream,0);for(var i2=0;i2<blockSize;i2++){words[offset+i2]^=keystream[i2]}}});CTRGladman.Decryptor=Encryptor;return CTRGladman}();return CryptoJS2.mode.CTRGladman}))})(modeCtrGladman);return modeCtrGladman.exports}var modeOfb={exports:{}};var hasRequiredModeOfb;function requireModeOfb(){if(hasRequiredModeOfb)return modeOfb.exports;hasRequiredModeOfb=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.mode.OFB=function(){var OFB=CryptoJS2.lib.BlockCipherMode.extend();var Encryptor=OFB.Encryptor=OFB.extend({processBlock:function(words,offset){var cipher=this._cipher;var blockSize=cipher.blockSize;var iv=this._iv;var keystream=this._keystream;if(iv){keystream=this._keystream=iv.slice(0);this._iv=void 0}cipher.encryptBlock(keystream,0);for(var i2=0;i2<blockSize;i2++){words[offset+i2]^=keystream[i2]}}});OFB.Decryptor=Encryptor;return OFB}();return CryptoJS2.mode.OFB}))})(modeOfb);return modeOfb.exports}var modeEcb={exports:{}};var hasRequiredModeEcb;function requireModeEcb(){if(hasRequiredModeEcb)return modeEcb.exports;hasRequiredModeEcb=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.mode.ECB=function(){var ECB=CryptoJS2.lib.BlockCipherMode.extend();ECB.Encryptor=ECB.extend({processBlock:function(words,offset){this._cipher.encryptBlock(words,offset)}});ECB.Decryptor=ECB.extend({processBlock:function(words,offset){this._cipher.decryptBlock(words,offset)}});return ECB}();return CryptoJS2.mode.ECB}))})(modeEcb);return modeEcb.exports}var padAnsix923={exports:{}};var hasRequiredPadAnsix923;function requirePadAnsix923(){if(hasRequiredPadAnsix923)return padAnsix923.exports;hasRequiredPadAnsix923=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.pad.AnsiX923={pad:function(data,blockSize){var dataSigBytes=data.sigBytes;var blockSizeBytes=blockSize*4;var nPaddingBytes=blockSizeBytes-dataSigBytes%blockSizeBytes;var lastBytePos=dataSigBytes+nPaddingBytes-1;data.clamp();data.words[lastBytePos>>>2]|=nPaddingBytes<<24-lastBytePos%4*8;data.sigBytes+=nPaddingBytes},unpad:function(data){var nPaddingBytes=data.words[data.sigBytes-1>>>2]&255;data.sigBytes-=nPaddingBytes}};return CryptoJS2.pad.Ansix923}))})(padAnsix923);return padAnsix923.exports}var padIso10126={exports:{}};var hasRequiredPadIso10126;function requirePadIso10126(){if(hasRequiredPadIso10126)return padIso10126.exports;hasRequiredPadIso10126=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.pad.Iso10126={pad:function(data,blockSize){var blockSizeBytes=blockSize*4;var nPaddingBytes=blockSizeBytes-data.sigBytes%blockSizeBytes;data.concat(CryptoJS2.lib.WordArray.random(nPaddingBytes-1)).concat(CryptoJS2.lib.WordArray.create([nPaddingBytes<<24],1))},unpad:function(data){var nPaddingBytes=data.words[data.sigBytes-1>>>2]&255;data.sigBytes-=nPaddingBytes}};return CryptoJS2.pad.Iso10126}))})(padIso10126);return padIso10126.exports}var padIso97971={exports:{}};var hasRequiredPadIso97971;function requirePadIso97971(){if(hasRequiredPadIso97971)return padIso97971.exports;hasRequiredPadIso97971=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.pad.Iso97971={pad:function(data,blockSize){data.concat(CryptoJS2.lib.WordArray.create([2147483648],1));CryptoJS2.pad.ZeroPadding.pad(data,blockSize)},unpad:function(data){CryptoJS2.pad.ZeroPadding.unpad(data);data.sigBytes--}};return CryptoJS2.pad.Iso97971}))})(padIso97971);return padIso97971.exports}var padZeropadding={exports:{}};var hasRequiredPadZeropadding;function requirePadZeropadding(){if(hasRequiredPadZeropadding)return padZeropadding.exports;hasRequiredPadZeropadding=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.pad.ZeroPadding={pad:function(data,blockSize){var blockSizeBytes=blockSize*4;data.clamp();data.sigBytes+=blockSizeBytes-(data.sigBytes%blockSizeBytes||blockSizeBytes)},unpad:function(data){var dataWords=data.words;var i2=data.sigBytes-1;for(var i2=data.sigBytes-1;i2>=0;i2--){if(dataWords[i2>>>2]>>>24-i2%4*8&255){data.sigBytes=i2+1;break}}}};return CryptoJS2.pad.ZeroPadding}))})(padZeropadding);return padZeropadding.exports}var padNopadding={exports:{}};var hasRequiredPadNopadding;function requirePadNopadding(){if(hasRequiredPadNopadding)return padNopadding.exports;hasRequiredPadNopadding=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){CryptoJS2.pad.NoPadding={pad:function(){},unpad:function(){}};return CryptoJS2.pad.NoPadding}))})(padNopadding);return padNopadding.exports}var formatHex={exports:{}};var hasRequiredFormatHex;function requireFormatHex(){if(hasRequiredFormatHex)return formatHex.exports;hasRequiredFormatHex=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){(function(undefined$1){var C2=CryptoJS2;var C_lib=C2.lib;var CipherParams=C_lib.CipherParams;var C_enc=C2.enc;var Hex=C_enc.Hex;var C_format=C2.format;C_format.Hex={stringify:function(cipherParams){return cipherParams.ciphertext.toString(Hex)},parse:function(input){var ciphertext=Hex.parse(input);return CipherParams.create({ciphertext:ciphertext})}}})();return CryptoJS2.format.Hex}))})(formatHex);return formatHex.exports}var aes={exports:{}};var hasRequiredAes;function requireAes(){if(hasRequiredAes)return aes.exports;hasRequiredAes=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var BlockCipher=C_lib.BlockCipher;var C_algo=C2.algo;var SBOX=[];var INV_SBOX=[];var SUB_MIX_0=[];var SUB_MIX_1=[];var SUB_MIX_2=[];var SUB_MIX_3=[];var INV_SUB_MIX_0=[];var INV_SUB_MIX_1=[];var INV_SUB_MIX_2=[];var INV_SUB_MIX_3=[];(function(){var d2=[];for(var i2=0;i2<256;i2++){if(i2<128){d2[i2]=i2<<1}else{d2[i2]=i2<<1^283}}var x2=0;var xi=0;for(var i2=0;i2<256;i2++){var sx=xi^xi<<1^xi<<2^xi<<3^xi<<4;sx=sx>>>8^sx&255^99;SBOX[x2]=sx;INV_SBOX[sx]=x2;var x22=d2[x2];var x4=d2[x22];var x8=d2[x4];var t2=d2[sx]*257^sx*16843008;SUB_MIX_0[x2]=t2<<24|t2>>>8;SUB_MIX_1[x2]=t2<<16|t2>>>16;SUB_MIX_2[x2]=t2<<8|t2>>>24;SUB_MIX_3[x2]=t2;var t2=x8*16843009^x4*65537^x22*257^x2*16843008;INV_SUB_MIX_0[sx]=t2<<24|t2>>>8;INV_SUB_MIX_1[sx]=t2<<16|t2>>>16;INV_SUB_MIX_2[sx]=t2<<8|t2>>>24;INV_SUB_MIX_3[sx]=t2;if(!x2){x2=xi=1}else{x2=x22^d2[d2[d2[x8^x22]]];xi^=d2[d2[xi]]}}})();var RCON=[0,1,2,4,8,16,32,64,128,27,54];var AES=C_algo.AES=BlockCipher.extend({_doReset:function(){var t2;if(this._nRounds&&this._keyPriorReset===this._key){return}var key=this._keyPriorReset=this._key;var keyWords=key.words;var keySize=key.sigBytes/4;var nRounds=this._nRounds=keySize+6;var ksRows=(nRounds+1)*4;var keySchedule=this._keySchedule=[];for(var ksRow=0;ksRow<ksRows;ksRow++){if(ksRow<keySize){keySchedule[ksRow]=keyWords[ksRow]}else{t2=keySchedule[ksRow-1];if(!(ksRow%keySize)){t2=t2<<8|t2>>>24;t2=SBOX[t2>>>24]<<24|SBOX[t2>>>16&255]<<16|SBOX[t2>>>8&255]<<8|SBOX[t2&255];t2^=RCON[ksRow/keySize|0]<<24}else if(keySize>6&&ksRow%keySize==4){t2=SBOX[t2>>>24]<<24|SBOX[t2>>>16&255]<<16|SBOX[t2>>>8&255]<<8|SBOX[t2&255]}keySchedule[ksRow]=keySchedule[ksRow-keySize]^t2}}var invKeySchedule=this._invKeySchedule=[];for(var invKsRow=0;invKsRow<ksRows;invKsRow++){var ksRow=ksRows-invKsRow;if(invKsRow%4){var t2=keySchedule[ksRow]}else{var t2=keySchedule[ksRow-4]}if(invKsRow<4||ksRow<=4){invKeySchedule[invKsRow]=t2}else{invKeySchedule[invKsRow]=INV_SUB_MIX_0[SBOX[t2>>>24]]^INV_SUB_MIX_1[SBOX[t2>>>16&255]]^INV_SUB_MIX_2[SBOX[t2>>>8&255]]^INV_SUB_MIX_3[SBOX[t2&255]]}}},encryptBlock:function(M2,offset){this._doCryptBlock(M2,offset,this._keySchedule,SUB_MIX_0,SUB_MIX_1,SUB_MIX_2,SUB_MIX_3,SBOX)},decryptBlock:function(M2,offset){var t2=M2[offset+1];M2[offset+1]=M2[offset+3];M2[offset+3]=t2;this._doCryptBlock(M2,offset,this._invKeySchedule,INV_SUB_MIX_0,INV_SUB_MIX_1,INV_SUB_MIX_2,INV_SUB_MIX_3,INV_SBOX);var t2=M2[offset+1];M2[offset+1]=M2[offset+3];M2[offset+3]=t2},_doCryptBlock:function(M2,offset,keySchedule,SUB_MIX_02,SUB_MIX_12,SUB_MIX_22,SUB_MIX_32,SBOX2){var nRounds=this._nRounds;var s0=M2[offset]^keySchedule[0];var s1=M2[offset+1]^keySchedule[1];var s2=M2[offset+2]^keySchedule[2];var s3=M2[offset+3]^keySchedule[3];var ksRow=4;for(var round=1;round<nRounds;round++){var t0=SUB_MIX_02[s0>>>24]^SUB_MIX_12[s1>>>16&255]^SUB_MIX_22[s2>>>8&255]^SUB_MIX_32[s3&255]^keySchedule[ksRow++];var t1=SUB_MIX_02[s1>>>24]^SUB_MIX_12[s2>>>16&255]^SUB_MIX_22[s3>>>8&255]^SUB_MIX_32[s0&255]^keySchedule[ksRow++];var t2=SUB_MIX_02[s2>>>24]^SUB_MIX_12[s3>>>16&255]^SUB_MIX_22[s0>>>8&255]^SUB_MIX_32[s1&255]^keySchedule[ksRow++];var t3=SUB_MIX_02[s3>>>24]^SUB_MIX_12[s0>>>16&255]^SUB_MIX_22[s1>>>8&255]^SUB_MIX_32[s2&255]^keySchedule[ksRow++];s0=t0;s1=t1;s2=t2;s3=t3}var t0=(SBOX2[s0>>>24]<<24|SBOX2[s1>>>16&255]<<16|SBOX2[s2>>>8&255]<<8|SBOX2[s3&255])^keySchedule[ksRow++];var t1=(SBOX2[s1>>>24]<<24|SBOX2[s2>>>16&255]<<16|SBOX2[s3>>>8&255]<<8|SBOX2[s0&255])^keySchedule[ksRow++];var t2=(SBOX2[s2>>>24]<<24|SBOX2[s3>>>16&255]<<16|SBOX2[s0>>>8&255]<<8|SBOX2[s1&255])^keySchedule[ksRow++];var t3=(SBOX2[s3>>>24]<<24|SBOX2[s0>>>16&255]<<16|SBOX2[s1>>>8&255]<<8|SBOX2[s2&255])^keySchedule[ksRow++];M2[offset]=t0;M2[offset+1]=t1;M2[offset+2]=t2;M2[offset+3]=t3},keySize:256/32});C2.AES=BlockCipher._createHelper(AES)})();return CryptoJS2.AES}))})(aes);return aes.exports}var tripledes={exports:{}};var hasRequiredTripledes;function requireTripledes(){if(hasRequiredTripledes)return tripledes.exports;hasRequiredTripledes=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var WordArray=C_lib.WordArray;var BlockCipher=C_lib.BlockCipher;var C_algo=C2.algo;var PC1=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4];var PC2=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32];var BIT_SHIFTS=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28];var SBOX_P=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}];var SBOX_MASK=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679];var DES=C_algo.DES=BlockCipher.extend({_doReset:function(){var key=this._key;var keyWords=key.words;var keyBits=[];for(var i2=0;i2<56;i2++){var keyBitPos=PC1[i2]-1;keyBits[i2]=keyWords[keyBitPos>>>5]>>>31-keyBitPos%32&1}var subKeys=this._subKeys=[];for(var nSubKey=0;nSubKey<16;nSubKey++){var subKey=subKeys[nSubKey]=[];var bitShift=BIT_SHIFTS[nSubKey];for(var i2=0;i2<24;i2++){subKey[i2/6|0]|=keyBits[(PC2[i2]-1+bitShift)%28]<<31-i2%6;subKey[4+(i2/6|0)]|=keyBits[28+(PC2[i2+24]-1+bitShift)%28]<<31-i2%6}subKey[0]=subKey[0]<<1|subKey[0]>>>31;for(var i2=1;i2<7;i2++){subKey[i2]=subKey[i2]>>>(i2-1)*4+3}subKey[7]=subKey[7]<<5|subKey[7]>>>27}var invSubKeys=this._invSubKeys=[];for(var i2=0;i2<16;i2++){invSubKeys[i2]=subKeys[15-i2]}},encryptBlock:function(M2,offset){this._doCryptBlock(M2,offset,this._subKeys)},decryptBlock:function(M2,offset){this._doCryptBlock(M2,offset,this._invSubKeys)},_doCryptBlock:function(M2,offset,subKeys){this._lBlock=M2[offset];this._rBlock=M2[offset+1];exchangeLR.call(this,4,252645135);exchangeLR.call(this,16,65535);exchangeRL.call(this,2,858993459);exchangeRL.call(this,8,16711935);exchangeLR.call(this,1,1431655765);for(var round=0;round<16;round++){var subKey=subKeys[round];var lBlock=this._lBlock;var rBlock=this._rBlock;var f2=0;for(var i2=0;i2<8;i2++){f2|=SBOX_P[i2][((rBlock^subKey[i2])&SBOX_MASK[i2])>>>0]}this._lBlock=rBlock;this._rBlock=lBlock^f2}var t2=this._lBlock;this._lBlock=this._rBlock;this._rBlock=t2;exchangeLR.call(this,1,1431655765);exchangeRL.call(this,8,16711935);exchangeRL.call(this,2,858993459);exchangeLR.call(this,16,65535);exchangeLR.call(this,4,252645135);M2[offset]=this._lBlock;M2[offset+1]=this._rBlock},keySize:64/32,ivSize:64/32,blockSize:64/32});function exchangeLR(offset,mask){var t2=(this._lBlock>>>offset^this._rBlock)&mask;this._rBlock^=t2;this._lBlock^=t2<<offset}function exchangeRL(offset,mask){var t2=(this._rBlock>>>offset^this._lBlock)&mask;this._lBlock^=t2;this._rBlock^=t2<<offset}C2.DES=BlockCipher._createHelper(DES);var TripleDES=C_algo.TripleDES=BlockCipher.extend({_doReset:function(){var key=this._key;var keyWords=key.words;if(keyWords.length!==2&&keyWords.length!==4&&keyWords.length<6){throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.")}var key1=keyWords.slice(0,2);var key2=keyWords.length<4?keyWords.slice(0,2):keyWords.slice(2,4);var key3=keyWords.length<6?keyWords.slice(0,2):keyWords.slice(4,6);this._des1=DES.createEncryptor(WordArray.create(key1));this._des2=DES.createEncryptor(WordArray.create(key2));this._des3=DES.createEncryptor(WordArray.create(key3))},encryptBlock:function(M2,offset){this._des1.encryptBlock(M2,offset);this._des2.decryptBlock(M2,offset);this._des3.encryptBlock(M2,offset)},decryptBlock:function(M2,offset){this._des3.decryptBlock(M2,offset);this._des2.encryptBlock(M2,offset);this._des1.decryptBlock(M2,offset)},keySize:192/32,ivSize:64/32,blockSize:64/32});C2.TripleDES=BlockCipher._createHelper(TripleDES)})();return CryptoJS2.TripleDES}))})(tripledes);return tripledes.exports}var rc4={exports:{}};var hasRequiredRc4;function requireRc4(){if(hasRequiredRc4)return rc4.exports;hasRequiredRc4=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var StreamCipher=C_lib.StreamCipher;var C_algo=C2.algo;var RC4=C_algo.RC4=StreamCipher.extend({_doReset:function(){var key=this._key;var keyWords=key.words;var keySigBytes=key.sigBytes;var S2=this._S=[];for(var i2=0;i2<256;i2++){S2[i2]=i2}for(var i2=0,j2=0;i2<256;i2++){var keyByteIndex=i2%keySigBytes;var keyByte=keyWords[keyByteIndex>>>2]>>>24-keyByteIndex%4*8&255;j2=(j2+S2[i2]+keyByte)%256;var t2=S2[i2];S2[i2]=S2[j2];S2[j2]=t2}this._i=this._j=0},_doProcessBlock:function(M2,offset){M2[offset]^=generateKeystreamWord.call(this)},keySize:256/32,ivSize:0});function generateKeystreamWord(){var S2=this._S;var i2=this._i;var j2=this._j;var keystreamWord=0;for(var n2=0;n2<4;n2++){i2=(i2+1)%256;j2=(j2+S2[i2])%256;var t2=S2[i2];S2[i2]=S2[j2];S2[j2]=t2;keystreamWord|=S2[(S2[i2]+S2[j2])%256]<<24-n2*8}this._i=i2;this._j=j2;return keystreamWord}C2.RC4=StreamCipher._createHelper(RC4);var RC4Drop=C_algo.RC4Drop=RC4.extend({cfg:RC4.cfg.extend({drop:192}),_doReset:function(){RC4._doReset.call(this);for(var i2=this.cfg.drop;i2>0;i2--){generateKeystreamWord.call(this)}}});C2.RC4Drop=StreamCipher._createHelper(RC4Drop)})();return CryptoJS2.RC4}))})(rc4);return rc4.exports}var rabbit={exports:{}};var hasRequiredRabbit;function requireRabbit(){if(hasRequiredRabbit)return rabbit.exports;hasRequiredRabbit=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var StreamCipher=C_lib.StreamCipher;var C_algo=C2.algo;var S2=[];var C_=[];var G2=[];var Rabbit=C_algo.Rabbit=StreamCipher.extend({_doReset:function(){var K2=this._key.words;var iv=this.cfg.iv;for(var i2=0;i2<4;i2++){K2[i2]=(K2[i2]<<8|K2[i2]>>>24)&16711935|(K2[i2]<<24|K2[i2]>>>8)&4278255360}var X2=this._X=[K2[0],K2[3]<<16|K2[2]>>>16,K2[1],K2[0]<<16|K2[3]>>>16,K2[2],K2[1]<<16|K2[0]>>>16,K2[3],K2[2]<<16|K2[1]>>>16];var C3=this._C=[K2[2]<<16|K2[2]>>>16,K2[0]&4294901760|K2[1]&65535,K2[3]<<16|K2[3]>>>16,K2[1]&4294901760|K2[2]&65535,K2[0]<<16|K2[0]>>>16,K2[2]&4294901760|K2[3]&65535,K2[1]<<16|K2[1]>>>16,K2[3]&4294901760|K2[0]&65535];this._b=0;for(var i2=0;i2<4;i2++){nextState.call(this)}for(var i2=0;i2<8;i2++){C3[i2]^=X2[i2+4&7]}if(iv){var IV=iv.words;var IV_0=IV[0];var IV_1=IV[1];var i0=(IV_0<<8|IV_0>>>24)&16711935|(IV_0<<24|IV_0>>>8)&4278255360;var i22=(IV_1<<8|IV_1>>>24)&16711935|(IV_1<<24|IV_1>>>8)&4278255360;var i1=i0>>>16|i22&4294901760;var i3=i22<<16|i0&65535;C3[0]^=i0;C3[1]^=i1;C3[2]^=i22;C3[3]^=i3;C3[4]^=i0;C3[5]^=i1;C3[6]^=i22;C3[7]^=i3;for(var i2=0;i2<4;i2++){nextState.call(this)}}},_doProcessBlock:function(M2,offset){var X2=this._X;nextState.call(this);S2[0]=X2[0]^X2[5]>>>16^X2[3]<<16;S2[1]=X2[2]^X2[7]>>>16^X2[5]<<16;S2[2]=X2[4]^X2[1]>>>16^X2[7]<<16;S2[3]=X2[6]^X2[3]>>>16^X2[1]<<16;for(var i2=0;i2<4;i2++){S2[i2]=(S2[i2]<<8|S2[i2]>>>24)&16711935|(S2[i2]<<24|S2[i2]>>>8)&4278255360;M2[offset+i2]^=S2[i2]}},blockSize:128/32,ivSize:64/32});function nextState(){var X2=this._X;var C3=this._C;for(var i2=0;i2<8;i2++){C_[i2]=C3[i2]}C3[0]=C3[0]+1295307597+this._b|0;C3[1]=C3[1]+3545052371+(C3[0]>>>0<C_[0]>>>0?1:0)|0;C3[2]=C3[2]+886263092+(C3[1]>>>0<C_[1]>>>0?1:0)|0;C3[3]=C3[3]+1295307597+(C3[2]>>>0<C_[2]>>>0?1:0)|0;C3[4]=C3[4]+3545052371+(C3[3]>>>0<C_[3]>>>0?1:0)|0;C3[5]=C3[5]+886263092+(C3[4]>>>0<C_[4]>>>0?1:0)|0;C3[6]=C3[6]+1295307597+(C3[5]>>>0<C_[5]>>>0?1:0)|0;C3[7]=C3[7]+3545052371+(C3[6]>>>0<C_[6]>>>0?1:0)|0;this._b=C3[7]>>>0<C_[7]>>>0?1:0;for(var i2=0;i2<8;i2++){var gx=X2[i2]+C3[i2];var ga=gx&65535;var gb=gx>>>16;var gh=((ga*ga>>>17)+ga*gb>>>15)+gb*gb;var gl=((gx&4294901760)*gx|0)+((gx&65535)*gx|0);G2[i2]=gh^gl}X2[0]=G2[0]+(G2[7]<<16|G2[7]>>>16)+(G2[6]<<16|G2[6]>>>16)|0;X2[1]=G2[1]+(G2[0]<<8|G2[0]>>>24)+G2[7]|0;X2[2]=G2[2]+(G2[1]<<16|G2[1]>>>16)+(G2[0]<<16|G2[0]>>>16)|0;X2[3]=G2[3]+(G2[2]<<8|G2[2]>>>24)+G2[1]|0;X2[4]=G2[4]+(G2[3]<<16|G2[3]>>>16)+(G2[2]<<16|G2[2]>>>16)|0;X2[5]=G2[5]+(G2[4]<<8|G2[4]>>>24)+G2[3]|0;X2[6]=G2[6]+(G2[5]<<16|G2[5]>>>16)+(G2[4]<<16|G2[4]>>>16)|0;X2[7]=G2[7]+(G2[6]<<8|G2[6]>>>24)+G2[5]|0}C2.Rabbit=StreamCipher._createHelper(Rabbit)})();return CryptoJS2.Rabbit}))})(rabbit);return rabbit.exports}var rabbitLegacy={exports:{}};var hasRequiredRabbitLegacy;function requireRabbitLegacy(){if(hasRequiredRabbitLegacy)return rabbitLegacy.exports;hasRequiredRabbitLegacy=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var StreamCipher=C_lib.StreamCipher;var C_algo=C2.algo;var S2=[];var C_=[];var G2=[];var RabbitLegacy=C_algo.RabbitLegacy=StreamCipher.extend({_doReset:function(){var K2=this._key.words;var iv=this.cfg.iv;var X2=this._X=[K2[0],K2[3]<<16|K2[2]>>>16,K2[1],K2[0]<<16|K2[3]>>>16,K2[2],K2[1]<<16|K2[0]>>>16,K2[3],K2[2]<<16|K2[1]>>>16];var C3=this._C=[K2[2]<<16|K2[2]>>>16,K2[0]&4294901760|K2[1]&65535,K2[3]<<16|K2[3]>>>16,K2[1]&4294901760|K2[2]&65535,K2[0]<<16|K2[0]>>>16,K2[2]&4294901760|K2[3]&65535,K2[1]<<16|K2[1]>>>16,K2[3]&4294901760|K2[0]&65535];this._b=0;for(var i2=0;i2<4;i2++){nextState.call(this)}for(var i2=0;i2<8;i2++){C3[i2]^=X2[i2+4&7]}if(iv){var IV=iv.words;var IV_0=IV[0];var IV_1=IV[1];var i0=(IV_0<<8|IV_0>>>24)&16711935|(IV_0<<24|IV_0>>>8)&4278255360;var i22=(IV_1<<8|IV_1>>>24)&16711935|(IV_1<<24|IV_1>>>8)&4278255360;var i1=i0>>>16|i22&4294901760;var i3=i22<<16|i0&65535;C3[0]^=i0;C3[1]^=i1;C3[2]^=i22;C3[3]^=i3;C3[4]^=i0;C3[5]^=i1;C3[6]^=i22;C3[7]^=i3;for(var i2=0;i2<4;i2++){nextState.call(this)}}},_doProcessBlock:function(M2,offset){var X2=this._X;nextState.call(this);S2[0]=X2[0]^X2[5]>>>16^X2[3]<<16;S2[1]=X2[2]^X2[7]>>>16^X2[5]<<16;S2[2]=X2[4]^X2[1]>>>16^X2[7]<<16;S2[3]=X2[6]^X2[3]>>>16^X2[1]<<16;for(var i2=0;i2<4;i2++){S2[i2]=(S2[i2]<<8|S2[i2]>>>24)&16711935|(S2[i2]<<24|S2[i2]>>>8)&4278255360;M2[offset+i2]^=S2[i2]}},blockSize:128/32,ivSize:64/32});function nextState(){var X2=this._X;var C3=this._C;for(var i2=0;i2<8;i2++){C_[i2]=C3[i2]}C3[0]=C3[0]+1295307597+this._b|0;C3[1]=C3[1]+3545052371+(C3[0]>>>0<C_[0]>>>0?1:0)|0;C3[2]=C3[2]+886263092+(C3[1]>>>0<C_[1]>>>0?1:0)|0;C3[3]=C3[3]+1295307597+(C3[2]>>>0<C_[2]>>>0?1:0)|0;C3[4]=C3[4]+3545052371+(C3[3]>>>0<C_[3]>>>0?1:0)|0;C3[5]=C3[5]+886263092+(C3[4]>>>0<C_[4]>>>0?1:0)|0;C3[6]=C3[6]+1295307597+(C3[5]>>>0<C_[5]>>>0?1:0)|0;C3[7]=C3[7]+3545052371+(C3[6]>>>0<C_[6]>>>0?1:0)|0;this._b=C3[7]>>>0<C_[7]>>>0?1:0;for(var i2=0;i2<8;i2++){var gx=X2[i2]+C3[i2];var ga=gx&65535;var gb=gx>>>16;var gh=((ga*ga>>>17)+ga*gb>>>15)+gb*gb;var gl=((gx&4294901760)*gx|0)+((gx&65535)*gx|0);G2[i2]=gh^gl}X2[0]=G2[0]+(G2[7]<<16|G2[7]>>>16)+(G2[6]<<16|G2[6]>>>16)|0;X2[1]=G2[1]+(G2[0]<<8|G2[0]>>>24)+G2[7]|0;X2[2]=G2[2]+(G2[1]<<16|G2[1]>>>16)+(G2[0]<<16|G2[0]>>>16)|0;X2[3]=G2[3]+(G2[2]<<8|G2[2]>>>24)+G2[1]|0;X2[4]=G2[4]+(G2[3]<<16|G2[3]>>>16)+(G2[2]<<16|G2[2]>>>16)|0;X2[5]=G2[5]+(G2[4]<<8|G2[4]>>>24)+G2[3]|0;X2[6]=G2[6]+(G2[5]<<16|G2[5]>>>16)+(G2[4]<<16|G2[4]>>>16)|0;X2[7]=G2[7]+(G2[6]<<8|G2[6]>>>24)+G2[5]|0}C2.RabbitLegacy=StreamCipher._createHelper(RabbitLegacy)})();return CryptoJS2.RabbitLegacy}))})(rabbitLegacy);return rabbitLegacy.exports}var blowfish={exports:{}};var hasRequiredBlowfish;function requireBlowfish(){if(hasRequiredBlowfish)return blowfish.exports;hasRequiredBlowfish=1;(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireEncBase64(),requireMd5(),requireEvpkdf(),requireCipherCore())}})(commonjsGlobal,(function(CryptoJS2){(function(){var C2=CryptoJS2;var C_lib=C2.lib;var BlockCipher=C_lib.BlockCipher;var C_algo=C2.algo;const N2=16;const ORIG_P=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731];const ORIG_S=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var BLOWFISH_CTX={pbox:[],sbox:[]};function F2(ctx,x2){let a2=x2>>24&255;let b2=x2>>16&255;let c2=x2>>8&255;let d2=x2&255;let y2=ctx.sbox[0][a2]+ctx.sbox[1][b2];y2=y2^ctx.sbox[2][c2];y2=y2+ctx.sbox[3][d2];return y2}function BlowFish_Encrypt(ctx,left,right){let Xl=left;let Xr=right;let temp;for(let i2=0;i2<N2;++i2){Xl=Xl^ctx.pbox[i2];Xr=F2(ctx,Xl)^Xr;temp=Xl;Xl=Xr;Xr=temp}temp=Xl;Xl=Xr;Xr=temp;Xr=Xr^ctx.pbox[N2];Xl=Xl^ctx.pbox[N2+1];return{left:Xl,right:Xr}}function BlowFish_Decrypt(ctx,left,right){let Xl=left;let Xr=right;let temp;for(let i2=N2+1;i2>1;--i2){Xl=Xl^ctx.pbox[i2];Xr=F2(ctx,Xl)^Xr;temp=Xl;Xl=Xr;Xr=temp}temp=Xl;Xl=Xr;Xr=temp;Xr=Xr^ctx.pbox[1];Xl=Xl^ctx.pbox[0];return{left:Xl,right:Xr}}function BlowFishInit(ctx,key,keysize){for(let Row=0;Row<4;Row++){ctx.sbox[Row]=[];for(let Col=0;Col<256;Col++){ctx.sbox[Row][Col]=ORIG_S[Row][Col]}}let keyIndex=0;for(let index2=0;index2<N2+2;index2++){ctx.pbox[index2]=ORIG_P[index2]^key[keyIndex];keyIndex++;if(keyIndex>=keysize){keyIndex=0}}let Data1=0;let Data2=0;let res=0;for(let i2=0;i2<N2+2;i2+=2){res=BlowFish_Encrypt(ctx,Data1,Data2);Data1=res.left;Data2=res.right;ctx.pbox[i2]=Data1;ctx.pbox[i2+1]=Data2}for(let i2=0;i2<4;i2++){for(let j2=0;j2<256;j2+=2){res=BlowFish_Encrypt(ctx,Data1,Data2);Data1=res.left;Data2=res.right;ctx.sbox[i2][j2]=Data1;ctx.sbox[i2][j2+1]=Data2}}return true}var Blowfish=C_algo.Blowfish=BlockCipher.extend({_doReset:function(){if(this._keyPriorReset===this._key){return}var key=this._keyPriorReset=this._key;var keyWords=key.words;var keySize=key.sigBytes/4;BlowFishInit(BLOWFISH_CTX,keyWords,keySize)},encryptBlock:function(M2,offset){var res=BlowFish_Encrypt(BLOWFISH_CTX,M2[offset],M2[offset+1]);M2[offset]=res.left;M2[offset+1]=res.right},decryptBlock:function(M2,offset){var res=BlowFish_Decrypt(BLOWFISH_CTX,M2[offset],M2[offset+1]);M2[offset]=res.left;M2[offset+1]=res.right},blockSize:64/32,keySize:128/32,ivSize:64/32});C2.Blowfish=BlockCipher._createHelper(Blowfish)})();return CryptoJS2.Blowfish}))})(blowfish);return blowfish.exports}(function(module2,exports2){(function(root,factory,undef){{module2.exports=factory(requireCore(),requireX64Core(),requireLibTypedarrays(),requireEncUtf16(),requireEncBase64(),requireEncBase64url(),requireMd5(),requireSha1(),requireSha256(),requireSha224(),requireSha512(),requireSha384(),requireSha3(),requireRipemd160(),requireHmac(),requirePbkdf2(),requireEvpkdf(),requireCipherCore(),requireModeCfb(),requireModeCtr(),requireModeCtrGladman(),requireModeOfb(),requireModeEcb(),requirePadAnsix923(),requirePadIso10126(),requirePadIso97971(),requirePadZeropadding(),requirePadNopadding(),requireFormatHex(),requireAes(),requireTripledes(),requireRc4(),requireRabbit(),requireRabbitLegacy(),requireBlowfish())}})(commonjsGlobal,(function(CryptoJS2){return CryptoJS2}))})(cryptoJs);var cryptoJsExports=cryptoJs.exports;const CryptoJS=getDefaultExportFromCjs(cryptoJsExports);
/**
 * @preserve
 * gcoord 1.0.6, geographic coordinate library
 * Copyright (c) 2024 Jiulong Hu <<EMAIL>>
 */const{sin:sin$1,cos:cos$1,sqrt:sqrt$1,abs:abs$1,PI:PI$1}=Math;const a$1=6378245;const ee$1=.006693421622965823;function isInChinaBbox(lon,lat){return lon>=72.004&&lon<=137.8347&&lat>=.8293&&lat<=55.8271}function transformLat(x2,y2){let ret=-100+2*x2+3*y2+.2*y2*y2+.1*x2*y2+.2*sqrt$1(abs$1(x2));ret+=(20*sin$1(6*x2*PI$1)+20*sin$1(2*x2*PI$1))*2/3;ret+=(20*sin$1(y2*PI$1)+40*sin$1(y2/3*PI$1))*2/3;ret+=(160*sin$1(y2/12*PI$1)+320*sin$1(y2*PI$1/30))*2/3;return ret}function transformLon(x2,y2){let ret=300+x2+2*y2+.1*x2*x2+.1*x2*y2+.1*sqrt$1(abs$1(x2));ret+=(20*sin$1(6*x2*PI$1)+20*sin$1(2*x2*PI$1))*2/3;ret+=(20*sin$1(x2*PI$1)+40*sin$1(x2/3*PI$1))*2/3;ret+=(150*sin$1(x2/12*PI$1)+300*sin$1(x2/30*PI$1))*2/3;return ret}function delta(lon,lat){let dLon=transformLon(lon-105,lat-35);let dLat=transformLat(lon-105,lat-35);const radLat=lat/180*PI$1;let magic=sin$1(radLat);magic=1-ee$1*magic*magic;const sqrtMagic=sqrt$1(magic);dLon=dLon*180/(a$1/sqrtMagic*cos$1(radLat)*PI$1);dLat=dLat*180/(a$1*(1-ee$1)/(magic*sqrtMagic)*PI$1);return[dLon,dLat]}function WGS84ToGCJ02(coord){const[lon,lat]=coord;if(!isInChinaBbox(lon,lat))return[lon,lat];const d2=delta(lon,lat);return[lon+d2[0],lat+d2[1]]}function GCJ02ToWGS84(coord){const[lon,lat]=coord;if(!isInChinaBbox(lon,lat))return[lon,lat];let[wgsLon,wgsLat]=[lon,lat];let tempPoint=WGS84ToGCJ02([wgsLon,wgsLat]);let dx=tempPoint[0]-lon;let dy=tempPoint[1]-lat;while(abs$1(dx)>1e-6||abs$1(dy)>1e-6){wgsLon-=dx;wgsLat-=dy;tempPoint=WGS84ToGCJ02([wgsLon,wgsLat]);dx=tempPoint[0]-lon;dy=tempPoint[1]-lat}return[wgsLon,wgsLat]}const{sin:sin,cos:cos,atan2:atan2,sqrt:sqrt,PI:PI}=Math;const baiduFactor=PI*3e3/180;function BD09ToGCJ02(coord){const[lon,lat]=coord;const x2=lon-.0065;const y2=lat-.006;const z2=sqrt(x2*x2+y2*y2)-2e-5*sin(y2*baiduFactor);const theta=atan2(y2,x2)-3e-6*cos(x2*baiduFactor);const newLon=z2*cos(theta);const newLat=z2*sin(theta);return[newLon,newLat]}function GCJ02ToBD09(coord){const[lon,lat]=coord;const x2=lon;const y2=lat;const z2=sqrt(x2*x2+y2*y2)+2e-5*sin(y2*baiduFactor);const theta=atan2(y2,x2)+3e-6*cos(x2*baiduFactor);const newLon=z2*cos(theta)+.0065;const newLat=z2*sin(theta)+.006;return[newLon,newLat]}const R2D=180/Math.PI;const D2R=Math.PI/180;const A$1=6378137;const MAXEXTENT=20037508.342789244;function ESPG3857ToWGS84(xy){return[xy[0]*R2D/A$1,(Math.PI*.5-2*Math.atan(Math.exp(-xy[1]/A$1)))*R2D]}function WGS84ToEPSG3857(lonLat){const adjusted=Math.abs(lonLat[0])<=180?lonLat[0]:lonLat[0]-(lonLat[0]<0?-1:1)*360;const xy=[A$1*adjusted*D2R,A$1*Math.log(Math.tan(Math.PI*.25+.5*lonLat[1]*D2R))];if(xy[0]>MAXEXTENT)xy[0]=MAXEXTENT;if(xy[0]<-MAXEXTENT)xy[0]=-MAXEXTENT;if(xy[1]>MAXEXTENT)xy[1]=MAXEXTENT;if(xy[1]<-MAXEXTENT)xy[1]=-MAXEXTENT;return xy}const{abs:abs}=Math;const MCBAND=[12890594.86,8362377.87,5591021,3481989.83,1678043.12,0];const LLBAND=[75,60,45,30,15,0];const MC2LL=[[1.410526172116255e-8,898305509648872e-20,-1.9939833816331,200.9824383106796,-187.2403703815547,91.6087516669843,-23.38765649603339,2.57121317296198,-.03801003308653,17337981.2],[-7.435856389565537e-9,8983055097726239e-21,-.78625201886289,96.32687599759846,-1.85204757529826,-59.36935905485877,47.40033549296737,-16.50741931063887,2.28786674699375,10260144.86],[-3.030883460898826e-8,898305509983578e-20,.30071316287616,59.74293618442277,7.357984074871,-25.38371002664745,13.45380521110908,-3.29883767235584,.32710905363475,6856817.37],[-1.981981304930552e-8,8983055099779535e-21,.03278182852591,40.31678527705744,.65659298677277,-4.44255534477492,.85341911805263,.12923347998204,-.04625736007561,4482777.06],[3.09191371068437e-9,8983055096812155e-21,6995724062e-14,23.10934304144901,-.00023663490511,-.6321817810242,-.00663494467273,.03430082397953,-.00466043876332,2555164.4],[2.890871144776878e-9,8983055095805407e-21,-3.068298e-8,7.47137025468032,-353937994e-14,-.02145144861037,-1234426596e-14,.00010322952773,-323890364e-14,826088.5]];const LL2MC=[[-.0015702102444,111320.7020616939,0x60e374c3105a3,-0x24bb4115e2e164,0x5cc55543bb0ae8,-0x7ce070193f3784,0x5e7ca61ddf8150,-0x261a578d8b24d0,0x665d60f3742ca,82.5],[.0008277824516172526,111320.7020463578,647795574.6671607,-4082003173.641316,10774905663.51142,-15171875531.51559,12053065338.62167,-5124939663.577472,913311935.9512032,67.5],[.00337398766765,111320.7020202162,4481351.045890365,-23393751.19931662,79682215.47186455,-115964993.2797253,97236711.15602145,-43661946.33752821,8477230.501135234,52.5],[.00220636496208,111320.7020209128,51751.86112841131,3796837.749470245,992013.7397791013,-1221952.21711287,1340652.697009075,-620943.6990984312,144416.9293806241,37.5],[-.0003441963504368392,111320.7020576856,278.2353980772752,2485758.690035394,6070.750963243378,54821.18345352118,9540.606633304236,-2710.55326746645,1405.483844121726,22.5],[-.0003218135878613132,111320.7020701615,.00369383431289,823725.6402795718,.46104986909093,2351.343141331292,1.58060784298199,8.77738589078284,.37238884252424,7.45]];function transform$1(x2,y2,factors){const cc=abs(y2)/factors[9];let xt=factors[0]+factors[1]*abs(x2);let yt=factors[2]+factors[3]*cc+factors[4]*Math.pow(cc,2)+factors[5]*Math.pow(cc,3)+factors[6]*Math.pow(cc,4)+factors[7]*Math.pow(cc,5)+factors[8]*Math.pow(cc,6);xt*=x2<0?-1:1;yt*=y2<0?-1:1;return[xt,yt]}function BD09toBD09MC(coord){const[lng,lat]=coord;let factors=[];for(let i2=0;i2<LLBAND.length;i2++){if(abs(lat)>LLBAND[i2]){factors=LL2MC[i2];break}}return transform$1(lng,lat,factors)}function BD09MCtoBD09(coord){const[x2,y2]=coord;let factors=[];for(let i2=0;i2<MCBAND.length;i2++){if(y2>=MCBAND[i2]){factors=MC2LL[i2];break}}return transform$1(x2,y2,factors)}function assert(condition,msg){if(!condition)throw new Error(msg)}function isArray$2(input){return!!input&&Object.prototype.toString.call(input)==="[object Array]"}function isNumber$2(input){return!isNaN(Number(input))&&input!==null&&!isArray$2(input)}function compose(...funcs){const start=funcs.length-1;return function(...args){let i2=start;let result=funcs[start].apply(null,args);while(i2--)result=funcs[i2].call(null,result);return result}}function coordEach(geojson,callback,excludeWrapCoord=false){if(geojson===null)return;let j2;let k2;let l2;let geometry;let coords;let stopG;let wrapShrink=0;let coordIndex=0;let geometryMaybeCollection;let isGeometryCollection;const{type:type}=geojson;const isFeatureCollection=type==="FeatureCollection";const isFeature=type==="Feature";const stop=isFeatureCollection?geojson.features.length:1;for(let featureIndex=0;featureIndex<stop;featureIndex++){geometryMaybeCollection=isFeatureCollection?geojson.features[featureIndex].geometry:isFeature?geojson.geometry:geojson;isGeometryCollection=geometryMaybeCollection?geometryMaybeCollection.type==="GeometryCollection":false;stopG=isGeometryCollection?geometryMaybeCollection.geometries.length:1;for(let geomIndex=0;geomIndex<stopG;geomIndex++){let multiFeatureIndex=0;let geometryIndex=0;geometry=isGeometryCollection?geometryMaybeCollection.geometries[geomIndex]:geometryMaybeCollection;if(geometry===null)continue;const geomType=geometry.type;wrapShrink=excludeWrapCoord&&(geomType==="Polygon"||geomType==="MultiPolygon")?1:0;switch(geomType){case null:break;case"Point":coords=geometry.coordinates;if(callback(coords,coordIndex,featureIndex,multiFeatureIndex,geometryIndex)===false)return false;coordIndex++;multiFeatureIndex++;break;case"LineString":case"MultiPoint":coords=geometry.coordinates;for(j2=0;j2<coords.length;j2++){if(callback(coords[j2],coordIndex,featureIndex,multiFeatureIndex,geometryIndex)===false)return false;coordIndex++;if(geomType==="MultiPoint")multiFeatureIndex++}if(geomType==="LineString")multiFeatureIndex++;break;case"Polygon":case"MultiLineString":coords=geometry.coordinates;for(j2=0;j2<coords.length;j2++){for(k2=0;k2<coords[j2].length-wrapShrink;k2++){if(callback(coords[j2][k2],coordIndex,featureIndex,multiFeatureIndex,geometryIndex)===false)return false;coordIndex++}if(geomType==="MultiLineString")multiFeatureIndex++;if(geomType==="Polygon")geometryIndex++}if(geomType==="Polygon")multiFeatureIndex++;break;case"MultiPolygon":coords=geometry.coordinates;for(j2=0;j2<coords.length;j2++){geometryIndex=0;for(k2=0;k2<coords[j2].length;k2++){for(l2=0;l2<coords[j2][k2].length-wrapShrink;l2++){if(callback(coords[j2][k2][l2],coordIndex,featureIndex,multiFeatureIndex,geometryIndex)===false)return false;coordIndex++}geometryIndex++}multiFeatureIndex++}break;case"GeometryCollection":for(j2=0;j2<geometry.geometries.length;j2++){if(coordEach(geometry.geometries[j2],callback,excludeWrapCoord)===false)return false}break;default:throw new Error("Unknown Geometry Type")}}}}var CRSTypes;(function(CRSTypes2){CRSTypes2["WGS84"]="WGS84";CRSTypes2["WGS1984"]="WGS84";CRSTypes2["EPSG4326"]="WGS84";CRSTypes2["GCJ02"]="GCJ02";CRSTypes2["AMap"]="GCJ02";CRSTypes2["BD09"]="BD09";CRSTypes2["BD09LL"]="BD09";CRSTypes2["Baidu"]="BD09";CRSTypes2["BMap"]="BD09";CRSTypes2["BD09MC"]="BD09MC";CRSTypes2["BD09Meter"]="BD09MC";CRSTypes2["EPSG3857"]="EPSG3857";CRSTypes2["EPSG900913"]="EPSG3857";CRSTypes2["EPSG102100"]="EPSG3857";CRSTypes2["WebMercator"]="EPSG3857";CRSTypes2["WM"]="EPSG3857"})(CRSTypes||(CRSTypes={}));const WGS84={to:{[CRSTypes.GCJ02]:WGS84ToGCJ02,[CRSTypes.BD09]:compose(GCJ02ToBD09,WGS84ToGCJ02),[CRSTypes.BD09MC]:compose(BD09toBD09MC,GCJ02ToBD09,WGS84ToGCJ02),[CRSTypes.EPSG3857]:WGS84ToEPSG3857}};const GCJ02={to:{[CRSTypes.WGS84]:GCJ02ToWGS84,[CRSTypes.BD09]:GCJ02ToBD09,[CRSTypes.BD09MC]:compose(BD09toBD09MC,GCJ02ToBD09),[CRSTypes.EPSG3857]:compose(WGS84ToEPSG3857,GCJ02ToWGS84)}};const BD09={to:{[CRSTypes.WGS84]:compose(GCJ02ToWGS84,BD09ToGCJ02),[CRSTypes.GCJ02]:BD09ToGCJ02,[CRSTypes.EPSG3857]:compose(WGS84ToEPSG3857,GCJ02ToWGS84,BD09ToGCJ02),[CRSTypes.BD09MC]:BD09toBD09MC}};const EPSG3857={to:{[CRSTypes.WGS84]:ESPG3857ToWGS84,[CRSTypes.GCJ02]:compose(WGS84ToGCJ02,ESPG3857ToWGS84),[CRSTypes.BD09]:compose(GCJ02ToBD09,WGS84ToGCJ02,ESPG3857ToWGS84),[CRSTypes.BD09MC]:compose(BD09toBD09MC,GCJ02ToBD09,WGS84ToGCJ02,ESPG3857ToWGS84)}};const BD09MC={to:{[CRSTypes.WGS84]:compose(GCJ02ToWGS84,BD09ToGCJ02,BD09MCtoBD09),[CRSTypes.GCJ02]:compose(BD09ToGCJ02,BD09MCtoBD09),[CRSTypes.EPSG3857]:compose(WGS84ToEPSG3857,GCJ02ToWGS84,BD09ToGCJ02,BD09MCtoBD09),[CRSTypes.BD09]:BD09MCtoBD09}};const crsMap={WGS84:WGS84,GCJ02:GCJ02,BD09:BD09,EPSG3857:EPSG3857,BD09MC:BD09MC};var crsMap$1=crsMap;function transform(input,crsFrom,crsTo){assert(!!input,"The args[0] input coordinate is required");assert(!!crsFrom,"The args[1] original coordinate system is required");assert(!!crsTo,"The args[2] target coordinate system is required");if(crsFrom===crsTo)return input;const from=crsMap$1[crsFrom];assert(!!from,`Invalid original coordinate system: ${crsFrom}`);const to=from.to[crsTo];assert(!!to,`Invalid target coordinate system: ${crsTo}`);const type=typeof input;assert(type==="string"||type==="object",`Invalid input coordinate type: ${type}`);if(type==="string"){try{input=JSON.parse(input)}catch(e2){throw new Error(`Invalid input coordinate: ${input}`)}}let isPosition=false;if(isArray$2(input)){assert(input.length>=2,`Invalid input coordinate: ${input}`);assert(isNumber$2(input[0])&&isNumber$2(input[1]),`Invalid input coordinate: ${input}`);input=input.map(Number);isPosition=true}const convert=to;if(isPosition)return convert(input);coordEach(input,(coord=>{[coord[0],coord[1]]=convert(coord)}));return input}const exported=Object.assign(Object.assign({},CRSTypes),{CRSTypes:CRSTypes,transform:transform});var e=function(t2,n2){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e2,t3){e2.__proto__=t3}||function(e2,t3){for(var n3 in t3)({}).hasOwnProperty.call(t3,n3)&&(e2[n3]=t3[n3])},e(t2,n2)};function t(t2,n2){if("function"!=typeof n2&&null!==n2)throw new TypeError("Class extends value "+n2+" is not a constructor or null");function i2(){this.constructor=t2}e(t2,n2),t2.prototype=null===n2?Object.create(n2):(i2.prototype=n2.prototype,new i2)}var n=function(){return n=Object.assign||function(e2){for(var t2,n2=1,i2=arguments.length;i2>n2;n2++)for(var r2 in t2=arguments[n2])({}).hasOwnProperty.call(t2,r2)&&(e2[r2]=t2[r2]);return e2},n.apply(this,arguments)};function i(e2,t2,n2,i2){return new(n2||(n2=Promise))((function(r2,o2){function a2(e3){try{s2(i2.next(e3))}catch(e4){o2(e4)}}function u2(e3){try{s2(i2.throw(e3))}catch(e4){o2(e4)}}function s2(e3){var t3;e3.done?r2(e3.value):(t3=e3.value,t3 instanceof n2?t3:new n2((function(e4){e4(t3)}))).then(a2,u2)}s2((i2=i2.apply(e2,[])).next())}))}function r$1(e2,t2){var n2,i2,r2,o2,a2={label:0,sent:function(){if(1&r2[0])throw r2[1];return r2[1]},trys:[],ops:[]};return o2={next:u2(0),throw:u2(1),return:u2(2)},"function"==typeof Symbol&&(o2[Symbol.iterator]=function(){return this}),o2;function u2(o3){return function(u3){return function(o4){if(n2)throw new TypeError("Generator is already executing.");for(;a2;)try{if(n2=1,i2&&(r2=2&o4[0]?i2.return:o4[0]?i2.throw||((r2=i2.return)&&r2.call(i2),0):i2.next)&&!(r2=r2.call(i2,o4[1])).done)return r2;switch(i2=0,r2&&(o4=[2&o4[0],r2.value]),o4[0]){case 0:case 1:r2=o4;break;case 4:return a2.label++,{value:o4[1],done:false};case 5:a2.label++,i2=o4[1],o4=[0];continue;case 7:o4=a2.ops.pop(),a2.trys.pop();continue;default:if(!((r2=(r2=a2.trys).length>0&&r2[r2.length-1])||6!==o4[0]&&2!==o4[0])){a2=0;continue}if(3===o4[0]&&(!r2||o4[1]>r2[0]&&o4[1]<r2[3])){a2.label=o4[1];break}if(6===o4[0]&&a2.label<r2[1]){a2.label=r2[1],r2=o4;break}if(r2&&a2.label<r2[2]){a2.label=r2[2],a2.ops.push(o4);break}r2[2]&&a2.ops.pop(),a2.trys.pop();continue}o4=t2.call(e2,a2)}catch(e3){o4=[6,e3],i2=0}finally{n2=r2=0}if(5&o4[0])throw o4[1];return{value:o4[0]?o4[1]:void 0,done:true}}([o3,u3])}}}function o(e2,t2,n2){if(2===arguments.length)for(var i2,r2=0,o2=t2.length;o2>r2;r2++)!i2&&r2 in t2||(i2||(i2=[].slice.call(t2,0,r2)),i2[r2]=t2[r2]);return e2.concat(i2||[].slice.call(t2))}var a={autotrack:{type:"boolean",default:true},cml:{type:"object",default:false},dataCollect:{type:"boolean",default:true},debug:{type:"boolean",default:false},followShare:{type:"boolean",default:true},forceLogin:{type:"boolean",default:false},subpackage:{type:"boolean",default:false},keepAlive:{type:"number",default:3e5},remax:{type:["object","module"],default:false},requestTimeout:{type:"number",default:5e3},taro:{type:["object","module"],default:false},taroVue:{type:["object","function"],default:false},tbConfig:{type:"object",default:{cloudFuncSend:false,cloudFuncName:"httpTunnel",cloudFuncHandler:"main",cloudAppId:void 0,path:void 0}},uniVue:{type:["object","function"],default:false},version:{type:"string",default:"1.0.0"},wepy:{type:"function",default:false}},u={compress:{type:"boolean",default:true},enableIdMapping:{type:"boolean",default:false},extraParams:{type:"array",default:[]},gtouchHost:{type:"string",default:"cdp.growingio.com"},host:{type:"string",default:"napi.growingio.com"},ignoreFields:{type:"array",default:[]},scheme:{type:"string",default:"https"},uploadInterval:{type:"number",default:1e3},performance:{type:"object",default:{monitor:true,exception:true}}},s={},l=["clearUserId","enableDebug","getGioInfo","getOption","identify","init","setAutotrack","setDataCollect","setOption","setUserId","track","setLocation","setGeneralProps","clearGeneralProps","setPageAttributes","updateImpression"];o(o([],l),["setEvar","setPage","setUser","setVisitor"]);var c,g,d=o(o([],l),["setTrackerHost","setTrackerScheme","setUserAttributes","registerPlugins","getDeviceId","trackTimerStart","trackTimerPause","trackTimerResume","trackTimerEnd","removeTimer","clearTrackTimer"]),p=["autotrack","dataCollect","debug","host","scheme"],f={autotrack:"无埋点",dataCollect:"数据采集",debug:"调试模式"},v=["setConfig","collectImp","setPlatformProfile","getLocation"],h=["deviceBrand","deviceModel","deviceType","networkState","screenHeight","screenWidth","operatingSystem"],m=o(o([],h),["appChannel","language","platformVersion"]),I=function(e2){return["undefined","null"].includes(G(e2))},y=function(e2){return"string"===G(e2)},S=function(e2){return"number"===G(e2)},w=function(e2){return"boolean"===G(e2)},b=function(e2){return"[object Object]"==={}.toString.call(e2)&&!I(e2)},O=function(e2){return["function","asyncfunction"].includes(G(e2))},P=function(e2){return Array.isArray(e2)&&"array"===G(e2)},_=function(e2){try{return Array.from(e2)[0]}catch(e3){return}},T=function(e2){try{var t2=Array.from(e2);return t2[t2.length-1]}catch(e3){return}},k=function(e2,t2){return void 0===t2&&(t2=1),P(e2)&&S(t2)?e2.slice(t2>0?t2:1,e2.length):e2},E=function(e2){if(P(e2)){for(var t2=0,n2=[],i2=0,r2=e2;i2<r2.length;i2++){var o2=r2[i2];o2&&!K(o2)&&(n2[t2++]=o2)}return n2}return e2},C=function(e2,t2){if(P(e2)){var n2=e2.findIndex(t2);return 0>n2?void 0:e2[n2]}},A=function(e2){return I(e2)?"":""+e2},N=function(e2,t2){return"string"===G(e2)?e2.split(t2):e2},x=function(e2){if(y(e2)){var t2=N(e2,"");return""+_(t2).toLowerCase()+k(t2).join("")}return e2},q=function(e2,t2){return e2.slice(0,t2.length)===t2},j=function(e2,t2){var n2=e2.length,i2=n2;i2>n2&&(i2=n2);var r2=i2;return(i2-=t2.length)>=0&&e2.slice(i2,r2)===t2},H={}.hasOwnProperty,D=function(e2,t2){return!I(e2)&&H.call(e2,t2)},F=function(e2){return b(e2)?Object.keys(e2):[]},L=function(e2,t2){F(e2).forEach((function(n2){return t2(e2[n2],n2)}))},U=function(e2,t2){var n2=F(e2);return!(!b(e2)||!b(t2)||n2.length!==F(t2).length||n2.map((function(n3,i2){return b(e2[n3])?U(e2[n3],t2[n3]):e2[n3]===t2[n3]})).includes(false))},V=function(e2,t2,n2){var i2=e2;return b(e2)?(t2.split(".").forEach((function(e3){i2=i2?i2[e3]:n2})),i2):n2},B=function(e2,t2){if(!b(e2))return false;try{if("string"===G(t2))return delete e2[t2];if("array"===G(t2))return t2.map((function(t3){return delete e2[t3]}));"object"===G(t2)&&t2.constructor===RegExp&&F(e2).forEach((function(n2){t2.test(n2)&&B(e2,n2)}))}catch(e3){return false}},R=function(e2,t2,n2,i2){var r2;void 0===t2&&(t2=250);var o2=0;return"boolean"!==G(n2)&&(i2=e2,e2=n2,n2=void 0),function(){for(var a2=[],u2=0;arguments.length>u2;u2++)a2[u2]=arguments[u2];var s2=this,l2=Date.now()-o2;function c2(){o2=Date.now(),e2.apply(s2,a2)}i2&&!r2&&c2(),r2&&clearTimeout(r2),void 0===i2&&l2>t2?c2():true!==n2&&(r2=setTimeout(i2?function(){r2=void 0}:c2,void 0===i2?t2-l2:t2))}},K=function(e2){return P(e2)?0===e2.length:b(e2)?0===F(e2).length:!e2},G=function(e2){return{}.toString.call(e2).slice(8,-1).toLowerCase()},$=Object.freeze({__proto__:null,isNil:I,isString:y,isNumber:S,isBoolean:w,isObject:b,isRegExp:function(e2){return"[object RegExp]"==={}.toString.call(e2)},isFunction:O,isArray:P,isDate:function(e2){return"date"===G(e2)},fixed:function(e2,t2){return S(e2)?Number(e2.toFixed(S(t2)?t2:2)):y(e2)&&"NaN"!==A(Number(e2))?Number(Number(e2).toFixed(S(t2)?t2:2)):e2},head:_,last:T,drop:k,dropWhile:function(e2,t2){return P(e2)?e2.filter((function(e3){return!t2(e3)})):e2},compact:E,find:C,isEqualArray:function(e2,t2){return!(!P(e2)||!P(t2)||e2.length!==t2.length)&&e2.every((function(e3,n2){return e3===t2[n2]}))},toString:A,split:N,lowerFirst:x,upperFirst:function(e2){if(y(e2)){var t2=N(e2,"");return""+_(t2).toUpperCase()+k(t2).join("")}return e2},startsWith:q,endsWith:j,hasOwnProperty:H,has:D,keys:F,forEach:L,isEqual:U,get:V,unset:B,throttle:R,isEmpty:K,typeOf:G,formatDate:function(e2){function t2(e3){return 10>e3?"0"+e3:e3}return e2.getFullYear()+"-"+t2(e2.getMonth()+1)+"-"+t2(e2.getDate())+" "+t2(e2.getHours())+":"+t2(e2.getMinutes())+":"+t2(e2.getSeconds())+"."+t2(e2.getMilliseconds())}}),M=function(e2,t2){console.log("%c[GrowingIO]："+e2,{info:"color: #3B82F6;",error:"color: #EF4444",warn:"color: #F59E0B",success:"color: #10B981"}[t2]||"")},Q=function(e2){try{return e2()}catch(e3){return}},W=function(e2){return y(e2)?e2.trim().substring(0,100):e2},z=function(e2,t2){return y(e2)&&!K(e2)&&e2.match(/^[a-zA-Z_][0-9a-zA-Z_]{0,100}$/)?t2():(M("事件名格式不正确，只能包含数字、字母和下划线，且不能以数字开头，字符总长度不能超过100!","error"),false)},Z=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e2){var t2=16*Math.random()|0;return("x"===e2?t2:3&t2|8).toString(16)}))},J=function(e2){return e2.pixelRatio?Math.round(e2.screenHeight*e2.pixelRatio):e2.screenHeight},Y=function(e2){return e2.pixelRatio?Math.round(e2.screenWidth*e2.pixelRatio):e2.screenWidth},X=function(e2,t2){if(e2){var n2=e2.toLowerCase();return-1!==n2.indexOf("android")?t2+"-Android":-1!==n2.indexOf("ios")?t2+"-iOS":e2}},ee=function(e2){if(y(e2)){var t2=e2.indexOf("?");return-1!==t2?[e2.substring(0,t2),e2.substring(t2+1)]:[e2,""]}return["",""]},te=function(e2){return y(e2)?e2.startsWith("/")?e2:"/"+e2:""},ne=function(e2){return Q((function(){switch(e2){case"wx":return wx;case"my":return my;case"swan":return swan;case"tt":return tt;case"qq":return qq;case"ks":return ks;case"jd":return jd;default:var t2=void 0;return t2||(t2=Q((function(){return swan}))),t2||(t2=Q((function(){return my}))),t2||(t2=Q((function(){return tt}))),t2||(t2=Q((function(){return qq}))),t2||(t2=Q((function(){return ks}))),t2||(t2=Q((function(){return jd}))),t2||(t2=Q((function(){return wx}))),t2}}))},ie=function(e2){return Q((function(){switch(e2){case"wx":return __wxConfig;case"my":return __myConfig;case"swan":return appConfig;case"tt":return __ttConfig;case"qq":return __qqConfig;case"ks":return __ksConfig;case"jd":return __jdConfig;default:return}}))},re=function(){var e2;return!e2&&Q((function(){return swan}))&&(e2="swan"),!e2&&Q((function(){return tt}))&&(e2="tt"),!e2&&Q((function(){return qq}))&&(e2="qq"),!e2&&Q((function(){return my}))&&(e2="my"),!e2&&Q((function(){return ks}))&&(e2="ks"),!e2&&Q((function(){return jd}))&&(e2="jd"),!e2&&Q((function(){return wx}))&&(e2="wx"),e2||C(F(global),(function(e3){return A(e3).toLowerCase().includes("quickapp")}))&&(e2="quickapp"),e2},oe=function(e2){return F(e2).filter((function(e3){return e3.indexOf("__sub")>-1&&e3.indexOf("gio__")>-1})).sort()},ae=function(){var e2=Q((function(){return getApp({allowDefault:true})}));return e2||(e2=ue()),e2},ue=function(){var e2;return null!==(e2=Q((function(){return $global})))&&void 0!==e2?e2:global},se=function(e2){var t2={};return b(e2)&&L(e2,(function(e3,n2){var i2=A(n2).slice(0,100);b(e3)?t2[i2]=se(e3):P(e3)?(t2[i2]=e3.slice(0,100),"cdp"===ue().gioEnvironment?t2[i2]=t2[i2].join("||").slice(0,1e3):t2[i2]=A(t2[i2]).slice(0,1e3)):t2[i2]=I(e3)?"":A(e3).slice(0,1e3)})),t2},le=function(e2,t2){var n2=function(){return t2.map((function(t3){return e2.getStorageSync(t3)}))};try{return e2.minip.canIUse("batchGetStorageSync")?e2.minip.batchGetStorageSync(t2):n2()}catch(e3){return n2()}},ce=function(e2,t2){var n2=function(){return t2.map((function(t3){var n3=t3.key,i2=t3.value;return e2.setStorageSync(n3,i2)}))};try{return e2.minip.canIUse("batchSetStorageSync")?e2.minip.batchSetStorageSync(t2):n2()}catch(e3){return n2()}},ge=function(e2){return F(e2||{}).map((function(t2){return t2?t2+"="+(I(e2[t2])?"":e2[t2]):""})).join("&")},de=function(e2){(q(e2,"?")||q(e2,"&"))&&(e2=e2.slice(1,e2.length));var t2={};return e2.split("&").forEach((function(e3){var n2=e3.split("=");n2[0]&&(t2[n2[0]]=n2[1])})),t2},pe=Object.freeze({__proto__:null,consoleText:M,niceTry:Q,limitString:W,eventNameValidate:z,guid:Z,getScreenHeight:J,getScreenWidth:Y,getOS:X,compareVersion:function(e2,t2){void 0===e2&&(e2=""),void 0===t2&&(t2="");for(var n2=function(e3){return e3.replace(/\s+/g,"")},i2=function(e3){return e3.replace(/[vV]/g,"")},r2=n2(i2(e2)),o2=n2(i2(t2)),a2=r2.split("."),u2=o2.split("."),s2=0;3>s2;s2++){var l2=Number(a2[s2]),c2=Number(u2[s2]);if(l2>c2)return 1;if(c2>l2)return-1;if(!isNaN(l2)&&isNaN(c2))return 1;if(isNaN(l2)&&!isNaN(c2))return-1}return 0},getVueVersion:function(e2){if(e2){var t2=Number.parseInt(_(N(e2.version,".")),10);return Number.isNaN(t2)?0:t2}return 0},splitPath:ee,normalPath:te,isTaro3:function(e2){var t2=e2.Current,n2=e2.createComponent;return!(!e2||!t2||n2)},getPlainMinip:ne,getPlainConfig:ie,getPlainPlatform:re,getSubKeys:oe,getAppInst:ae,getGlobal:ue,limitObject:se,batchGetStorageSync:le,batchSetStorageSync:ce,qsStringify:ge,qsParse:de,hashCode:function(e2){var t2=0;if(K(e2)||"boolean"==typeof e2)return t2;for(var n2=0;n2<e2.length;)t2=(t2<<5)-t2+e2.charCodeAt(n2),t2&=t2,n2++;return t2}}),fe=function(e2){return y(e2)&&e2.length>0||S(e2)&&e2>0},ve=/^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/i,he=/^(https?:\/\/)|(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i,me={name:"gioUniAppAdapter",method:function(e2){var t2,n2,i2=this;this.growingIO=e2,this.main=function(){var e3,t3,n3,r2;if(i2.uniVue||(i2.uniVue=null===(t3=null===(e3=i2.growingIO)||void 0===e3?void 0:e3.vdsConfig)||void 0===t3?void 0:t3.uniVue),console.log("Vue version: ",i2.uniVue.version),null===(r2=null===(n3=i2.growingIO)||void 0===n3?void 0:n3.vdsConfig)||void 0===r2?void 0:r2.subpackage)return false;switch(Number.parseInt(c.head(c.split(i2.uniVue.version,".")),10)){case 2:i2.uniVue2Proxy(i2.uniVue);break;case 3:i2.uniVue3Proxy(i2.uniVue);break;default:c.consoleText("不支持的Vue版本，请使用 Vue2 或 Vue3!","error")}},this.getHandlerName=function(e3,t3){var n3;if("__e"===e3){var r2="__e",o2=t3.type,a2=t3.currentTarget,u2=t3.target,s2=(null==a2?void 0:a2.dataset)||(null==u2?void 0:u2.dataset);if(s2){var l2=s2.eventOpts||s2["event-opts"];l2&&l2.forEach((function(e4){var t4=c.head(e4),n4="^"===t4.charAt(0),i3="~"===(t4=n4?t4.slice(1):o2).charAt(0);t4=i3?t4.slice(1):o2;var a3,u3,s3=c.last(e4);((a3=o2)===(u3=t4)||"regionchange"===u3&&("begin"===a3||"end"===a3))&&(r2=c.head(c.head(s3)))}))}return r2}return c.isEmpty(i2.exposedNames)?e3:null!==(n3=i2.exposedNames[e3])&&void 0!==n3?n3:e3},this.uniVue2Proxy=function(e3){var t3=i2.growingIO,n3=t3.dataStore.eventHooks,r2=t3.gioPlatform,o2=t3.minipInstance;"swan"===r2?App=function(){for(var e4=[],t4=0;arguments.length>t4;t4++)e4[t4]=arguments[t4];return n3.growingApp(e4[0])}:n3.nativeGrowing(),e3.mixin({beforeMount:function(){if("page"===this.mpType){n3.pageOverriding(this.$scope);var e4=this,t4=o2.getPageTitle;o2.getPageTitle=function(){for(var i3,r3,o3,a2=[],u2=0;arguments.length>u2;u2++)a2[u2]=arguments[u2];return(e4.$scope.route===n3.currentPage.path?null===(o3=null===(r3=null===(i3=e4.$scope)||void 0===i3?void 0:i3.$vm)||void 0===r3?void 0:r3._data)||void 0===o3?void 0:o3.gioPageTitle:void 0)||t4.apply(this,a2)}}}})},this.uniVue3Proxy=function(e3){var t3=i2.growingIO,n3=t3.dataStore.eventHooks,r2=t3.minipInstance;n3.nativeGrowing();var o2=r2.getPageTitle;r2.getPageTitle=function(){for(var e4,t4,n4,i3=[],a3=0;arguments.length>a3;a3++)i3[a3]=arguments[a3];var u2=c.isEmpty(i3[0])?r2.getCurrentPage():i3[0];return"page"===(null===(e4=null==u2?void 0:u2.$vm)||void 0===e4?void 0:e4.mpType)&&(null===(t4=null==u2?void 0:u2.$vm)||void 0===t4?void 0:t4.__route__)&&(null===(n4=null==u2?void 0:u2.$vm)||void 0===n4?void 0:n4.gioPageTitle)||o2.apply(this,i3)};var a2=i2;e3.mixin({beforeMount:function(){var e4,t4,i3=this,r3=function(e5,t5){return c.keys(e5).forEach((function(e6){var i4,r4;c.isFunction(t5[e6])&&Object.defineProperty(t5,e6,{value:(i4=e6,r4=t5[e6],c.isFunction(r4)&&!n3.pageHandlers.includes(i4)?function(){for(var e7=[],t6=0;arguments.length>t6;t6++)e7[t6]=arguments[t6];return n3.customFcEffects(i4,r4).apply(this,e7)}:r4),configurable:true,writable:true})}))},o3=this.$options.methods;o3&&r3(o3,this);var u2=null===(e4=null==this?void 0:this._)||void 0===e4?void 0:e4.setupState;u2&&r3(u2,u2);var s2=null===(t4=null==this?void 0:this._)||void 0===t4?void 0:t4.exposed;if(a2.exposedNames={},s2){var l2=c.keys(s2);l2.forEach((function(e5){c.isFunction(s2[e5])&&(a2.exposedNames[s2[e5].name]=e5)})),c.keys(this.$scope).filter((function(e5){return/^e[0-9]+$/.test(e5)&&c.isFunction(i3.$scope[e5])})).forEach((function(e5,t5){var r4=i3.$scope[e5].value,o4=a2.splicingMethodName(r4.name,e5,l2[t5]);i3.$scope[e5].value=n3.customFcEffects(o4,r4)}))}var g2=this._.emit;this._.emit=function(){for(var e5=[],t5=0;arguments.length>t5;t5++)e5[t5]=arguments[t5];return n3.customFcEffects(e5[0],(function(){})).apply(this,e5),g2.apply(this,e5)}}})},this.splicingMethodName=function(e3,t3,n3){return e3||(n3?t3+"#"+n3:t3)},c=this.growingIO.utils,this.uniVue=null===(n2=null===(t2=this.growingIO)||void 0===t2?void 0:t2.vdsConfig)||void 0===n2?void 0:n2.uniVue,this.exposedNames={}}};const Ie={};function ye(e2,t2,n2){var i2=e2;t2.map((function(e3,r2){i2[e3]=r2==t2.length-1?n2:i2[e3]||{},i2=i2[e3]}))}ye(Ie,["plugins","gioCustomTracking"],{name:"gioCustomTracking",method:function(e2){var t2=this;this.growingIO=e2,this.getValidResourceItem=function(e3){if(e3&&g.isObject(e3)&&e3.id&&e3.key){var t3={id:g.isString(e3.id)?e3.id:g.toString(e3.id),key:g.isString(e3.key)?e3.key:g.toString(e3.key)};return e3.attributes&&(t3.attributes=e3.attributes),t3}},this.getDynamicAttributes=function(e3){return g.isNil(e3)||g.keys(e3).forEach((function(t3){g.isFunction(e3[t3])?e3[t3]=e3[t3]():g.isArray(e3[t3])||(e3[t3]=g.toString(e3[t3]))})),e3},this.buildCustomEvent=function(e3,i2,r2){g.eventNameValidate(e3,(function(){var o2,a2=t2.growingIO.dataStore,u2=a2.eventContextBuilder,s2=a2.eventInterceptor,l2=a2.eventHooks;s2(n({eventType:"CUSTOM",eventName:e3,pageShowTimestamp:null===(o2=null==l2?void 0:l2.currentPage)||void 0===o2?void 0:o2.time,attributes:g.limitObject(t2.getDynamicAttributes(g.isObject(i2)&&!g.isEmpty(i2)?i2:void 0)),resourceItem:g.limitObject(t2.getValidResourceItem(r2))},u2()))}))},this.buildUserAttributesEvent=function(e3){var i2=t2.growingIO.dataStore,r2=i2.eventContextBuilder,o2=i2.eventInterceptor,a2=i2.lastVisitEvent,u2=n({eventType:"LOGIN_USER_ATTRIBUTES",attributes:g.limitObject(e3)},r2());u2.path||(u2.path=a2.path,u2.query=a2.query,u2.title=a2.title),o2(u2)},g=this.growingIO.utils}}),ye(Ie,["plugins","gioUniAppAdapter"],me);var Se=function(e2){var t2=this;this.growingIO=e2,this.innerPluginInit=function(){F(Ie.plugins).forEach((function(e3){var n2=Ie.plugins[e3],i2=n2.name,r2=n2.method;t2.pluginItems.find((function(e4){return e4.name===i2}))||t2.pluginItems.push({name:x(i2||e3),method:r2||function(e4){}})})),K(t2.pluginItems)||t2.installAll()},this.install=function(e3,n2,i2){var r2,o2=n2||t2.pluginItems.find((function(t3){return t3.name===e3}));if(null===(r2=t2.growingIO)||void 0===r2?void 0:r2.plugins[e3])return M("重复加载插件 "+e3+" 或插件重名，已跳过加载!","warn"),false;if(!o2)return M("插件加载失败!不存在名为 "+e3+" 的插件!","error"),false;try{return t2.growingIO.plugins[e3]=new o2.method(t2.growingIO,i2),"cdp"===t2.growingIO.gioEnvironment&&n2&&M("加载插件："+e3,"info"),true}catch(e4){return M("插件加载异常："+e4,"error"),false}},this.installAll=function(e3){(e3||t2.pluginItems).forEach((function(n2){return t2.install(n2.name,e3?n2:void 0,n2.options)}))},this.uninstall=function(e3){var n2;B(t2.pluginItems,e3);var i2=B(null===(n2=t2.growingIO)||void 0===n2?void 0:n2.plugins,e3);return i2||M("卸载插件 "+e3+" 失败!","error"),i2},this.uninstallAll=function(){t2.pluginItems.forEach((function(e3){return t2.uninstall(e3.name)}))},this.lifeError=function(e3,t3){return M("插件执行错误 "+e3.name+" "+t3,"error")},this.onComposeBefore=function(e3){t2.pluginItems.forEach((function(n2){var i2,r2=null===(i2=t2.growingIO.plugins[n2.name])||void 0===i2?void 0:i2.onComposeBefore;if(r2&&O(r2))try{r2(e3)}catch(e4){t2.lifeError(n2,e4)}}))},this.onComposeAfter=function(e3){t2.pluginItems.forEach((function(n2){var i2,r2=null===(i2=t2.growingIO.plugins[n2.name])||void 0===i2?void 0:i2.onComposeAfter;if(r2&&O(r2))try{r2(e3)}catch(e4){t2.lifeError(n2,e4)}}))},this.onSendBefore=function(e3){t2.pluginItems.forEach((function(n2){var i2,r2=null===(i2=t2.growingIO.plugins[n2.name])||void 0===i2?void 0:i2.onSendBefore;if(r2&&O(r2))try{r2(e3)}catch(e4){t2.lifeError(n2,e4)}}))},this.onSendAfter=function(e3){t2.pluginItems.forEach((function(n2){var i2,r2=null===(i2=t2.growingIO.plugins[n2.name])||void 0===i2?void 0:i2.onSendAfter;if(r2&&O(r2))try{r2(e3)}catch(e4){t2.lifeError(n2,e4)}}))},this.pluginItems=[],this.growingIO.emitter.on("onComposeBefore",this.onComposeBefore),this.growingIO.emitter.on("onComposeAfter",this.onComposeAfter),this.growingIO.emitter.on("onSendBefore",this.onSendBefore),this.growingIO.emitter.on("onSendAfter",this.onSendAfter)},we=function(){function e2(e3){var t2,n2=this;this.growingIO=e3,this.initUserInfo=function(){var e4,t3=n2.growingIO.minipInstance,i3=[n2.uidStorageName,n2.userIdStorageName,n2.userKeyStorageName,n2.gioIdStorageName],r2=null!==(e4=le(t3,i3))&&void 0!==e4?e4:[];["_uid","_userId","_userKey","_gioId"].forEach((function(e5,t4){n2[e5]=r2[t4],"_uid"!==e5||n2[e5]||(n2.uid=Z())}))},this.saveUserInfo=function(){var e4=n2.growingIO.minipInstance;ce(e4,[{key:n2.uidStorageName,value:n2.uid},{key:n2.userIdStorageName,value:n2.userId},{key:n2.userKeyStorageName,value:n2.userKey},{key:n2.gioIdStorageName,value:n2.gioId}])};var i2=this.growingIO.inPlugin;this.uidStorageName=i2?"_growing_plugin_uid_":"_growing_uid_",this.userIdStorageName=i2?"_growing_plugin_userId_":"_growing_userId_",this.userKeyStorageName=i2?"_growing_plugin_userKey_":"_growing_userKey_",this.gioIdStorageName=i2?"_growing_plugin_gioId_":"_growing_gioId_",this._sessionId=Z(),this._uid=void 0,this._userId=void 0,this._userKey=void 0,this._gioId=void 0,"quickapp"!==(null===(t2=this.growingIO)||void 0===t2?void 0:t2.gioPlatform)&&this.initUserInfo()}return Object.defineProperty(e2.prototype,"sessionId",{get:function(){return this._sessionId||(this._sessionId=Z()),this._sessionId},set:function(e3){var t2=this._sessionId;this._sessionId=e3||Z(),t2!==this._sessionId&&this.growingIO.emitter.emit("SESSIONID_UPDATE",{newSessionId:this._sessionId,oldSessionId:t2})},enumerable:false,configurable:true}),Object.defineProperty(e2.prototype,"uid",{get:function(){return this._uid},set:function(e3){var t2=this._uid;this._uid=e3,t2!==this._uid&&(this.growingIO.minipInstance.setStorage(this.uidStorageName,this._uid),this.growingIO.emitter.emit("UID_UPDATE",{newUId:e3,oldUId:t2}))},enumerable:false,configurable:true}),Object.defineProperty(e2.prototype,"userId",{get:function(){return this._userId},set:function(e3){var t2=this._userId;this._userId=e3,t2!==this._userId&&(this.growingIO.minipInstance.setStorage(this.userIdStorageName,this._userId),this.growingIO.emitter.emit("USERID_UPDATE",{newUserId:e3,oldUserId:t2})),e3&&(this.gioId=e3)},enumerable:false,configurable:true}),Object.defineProperty(e2.prototype,"userKey",{get:function(){return this._userKey},set:function(e3){var t2=this._userKey;this._userKey=e3,t2!==this._userKey&&(this.growingIO.minipInstance.setStorage(this.userKeyStorageName,this._userKey),this.growingIO.emitter.emit("USERKEY_UPDATE",{newUserKey:e3,oldUserKey:t2}))},enumerable:false,configurable:true}),Object.defineProperty(e2.prototype,"gioId",{get:function(){return this._gioId},set:function(e3){var t2=this._gioId;this._gioId=e3,t2!==this._gioId&&(this.growingIO.minipInstance.setStorage(this.gioIdStorageName,this._gioId),this.growingIO.emitter.emit("GIOID_UPDATE",{newGioId:e3,oldGioId:t2}))},enumerable:false,configurable:true}),e2}(),be={scnPrefix:"",appHandlers:["onLaunch","onShow","onHide","onError","onPageNotFound"],pageHandlers:["onLoad","onShow","onReady","onHide","onUnload","onShareAppMessage","onTabItemTap"],actionEventTypes:["onclick","tap","onTap","longpress","longTap","blur","change","onChange","submit","confirm","getuserinfo","getphonenumber","contact"],originalApp:Q((function(){return App})),originalPage:Q((function(){return Page})),originalComponent:Q((function(){return Component})),originalBehavior:Q((function(){return Behavior})),canHook:true,hooks:{App:true,Page:true,Component:true,Behavior:true},listeners:{app:{appLaunch:"onLaunch",appShow:"onShow",appClose:"onHide"},page:{pageLoad:"onLoad",pageShow:"onShow",pageReady:"onReady",pageHide:"onHide",pageUnload:"onUnload",tabTap:"onTabItemTap",shareApp:"onShareAppMessage"},actions:{click:["onclick","tap","longpress","longTap","getuserinfo","getphonenumber","contact"],change:["blur","change","confirm"],submit:["submit"]}}},Oe=n(n({},be),{listeners:n({},be.listeners),name:"JingDong",platform:"jdp",scnPrefix:"jd_"}),Pe=n(n({},be),{listeners:n({},be.listeners),name:"KuaiShou",platform:"kuaishoup",scnPrefix:"ks_"}),_e=n(n({},be),{listeners:n({},be.listeners),name:"Alipay",platform:"alip",scnPrefix:"alip_",hooks:n(n({},null==be?void 0:be.hooks),{Behavior:false})}),Te=n(n({},be),{listeners:n({},be.listeners),name:"QQ",platform:"qq",scnPrefix:"qq_"});Te.pageHandlers=o(o([],be.pageHandlers),["onAddToFavorites"]),Te.listeners.page.addFavorites="onAddToFavorites";var ke=n(n({},be),{listeners:n({},be.listeners),name:"Baidu",platform:"baidup",scnPrefix:"baidup_"}),Ee=n(n({},be),{listeners:n({},be.listeners),name:"Bytedance",platform:"bytedance",scnPrefix:"bytedance_"}),Ce=n(n({},be),{listeners:n({},be.listeners),name:"Weixin",platform:"MinP"});Ce.pageHandlers=o(o([],be.pageHandlers),["onShareTimeline","onAddToFavorites"]),Ce.listeners.page=n(n({},be.listeners.page),{shareTime:"onShareTimeline",addFavorites:"onAddToFavorites"});var Ae=re(),Ne=Q((function(){var e2={jd:Oe,ks:Pe,my:_e,qq:Te,swan:ke,tt:Ee,wx:Ce}[Ae];return e2.canHook=false,e2})),xe=function(e2){var t2=this;this.growingIO=e2,this.minip=ne("framework"),this.hookSetTitle=function(){var e3,n3=null===(e3=t2.minip)||void 0===e3?void 0:e3.setNavigationBarTitle,i2=t2;n3&&Object.defineProperty(t2.minip,"setNavigationBarTitle",{writable:true,enumerable:true,configurable:true,value:function(){if(i2.growingIO.gioSDKInitialized){var e4=i2.getCurrentPath(),t3=(arguments[0]||{}).title;i2.growingIO.dataStore.eventHooks.currentPage.settedTitle[e4]=t3}return n3.apply(this,arguments)}})},this.navigateTo=function(e3){var n3;return null===(n3=t2.minip)||void 0===n3?void 0:n3.navigateTo(e3)},this.switchTab=function(e3){var n3;null===(n3=t2.minip)||void 0===n3||n3.switchTab(e3)},this.navigateToMiniProgram=function(e3){var n3;return null===(n3=t2.minip)||void 0===n3?void 0:n3.navigateToMiniProgram(e3)},this.getImageInfo=function(e3){var n3,i2=e3.src,r2=e3.success,o3=e3.fail,a3=e3.complete;return null===(n3=t2.minip)||void 0===n3?void 0:n3.getImageInfo({src:i2,success:r2,fail:o3,complete:a3})},this.initImpression=function(e3){var n3,i2;null===(i2=null===(n3=t2.growingIO.plugins)||void 0===n3?void 0:n3.gioImpressionTracking)||void 0===i2||i2.main(e3,"observeAll")},this.getCurrentPage=function(){return Q((function(){return T(getCurrentPages())}))||{}},this.getCurrentPath=function(){var e3=t2.getCurrentPage();return(null==e3?void 0:e3.route)||(null==e3?void 0:e3.uri)||(null==e3?void 0:e3.__route__)||""},this.getPageTitle=function(e3){var t3,n3,i2="",r2=ie(re());try{var o3=(null==e3?void 0:e3.data)||e3.$data||{};if(0===(i2=y(null==o3?void 0:o3.gioPageTitle)?null==o3?void 0:o3.gioPageTitle:"").length){var a3=r2.page[e3.route]||r2.page[e3.route+".html"];i2=a3?a3.window.navigationBarTitleText:r2.global.window.navigationBarTitleText}if(0===i2.length){var u2=null===(n3=null===(t3=null==r2?void 0:r2.tabBar)||void 0===t3?void 0:t3.list)||void 0===n3?void 0:n3.find((function(t4){return t4.pagePath===e3.route||t4.pagePath===e3.route+".html"}));i2=(null==u2?void 0:u2.text)||""}}catch(e4){return""}return W(i2)},this.getStorageSync=function(e3){var n3;return null===(n3=t2.minip)||void 0===n3?void 0:n3.getStorageSync(e3)},this.getStorage=function(e3){return new Promise((function(n3){var i2;null===(i2=t2.minip)||void 0===i2||i2.getStorage({key:e3,success:function(e4){var t3=e4.data;return n3(t3)},fail:function(){return n3("")}})}))},this.setStorageSync=function(e3,n3){var i2;null===(i2=t2.minip)||void 0===i2||i2.setStorageSync(e3,n3)},this.setStorage=function(e3,n3){var i2;null===(i2=t2.minip)||void 0===i2||i2.setStorage({key:e3,data:n3})},this.removeStorageSync=function(e3){var n3;null===(n3=t2.minip)||void 0===n3||n3.removeStorageSync(e3)},this.removeStorage=function(e3){var n3;null===(n3=t2.minip)||void 0===n3||n3.removeStorage(e3)},this.getNetworkType=function(){return i(t2,void 0,void 0,(function(){var e3,t3=this;return r$1(this,(function(n3){switch(n3.label){case 0:return e3=this,[4,new Promise((function(n4){var i2;null===(i2=t3.minip)||void 0===i2||i2.getNetworkType({success:function(t4){e3.network=t4,n4(t4)},fail:function(){return n4(null)}})}))];case 1:return[2,n3.sent()]}}))}))},this.request=function(e3){var n3,i2=e3.url,r2=e3.data,o3=e3.header,a3=e3.timeout,u2=e3.method,s2=e3.success,l2=e3.fail,c2=e3.complete,g2=null===(n3=t2.minip)||void 0===n3?void 0:n3.request({url:i2,data:r2,header:o3,headers:o3,timeout:a3,method:u2,success:s2,fail:l2,complete:c2}),d2=setTimeout((function(){null==g2||g2.abort(),clearTimeout(d2),d2=null}),(a3||1e4)+10);return g2},this.getSystemInfo=function(){return i(t2,void 0,void 0,(function(){var e3,t3=this;return r$1(this,(function(n3){switch(n3.label){case 0:return e3=this,[4,new Promise((function(n4){var i2;null===(i2=t3.minip)||void 0===i2||i2.getSystemInfo({success:function(t4){e3.systemInfo=t4,n4(t4)},fail:function(){return n4(null)}})}))];case 1:return[2,n3.sent()]}}))}))},this.getSetting=function(){return new Promise((function(e3){var n3;null===(n3=t2.minip)||void 0===n3||n3.getSetting({success:e3,fail:e3})}))};var n2=this.growingIO.platformConfig,o2=n2.platform,a2=n2.scnPrefix;this.platform=o2,this.scnPrefix=a2,this.getNetworkType().then((function(){var e3;return null===(e3=t2.growingIO.dataStore)||void 0===e3?void 0:e3.eventReleaseInspector()})),this.getSystemInfo().then((function(){var e3;return null===(e3=t2.growingIO.dataStore)||void 0===e3?void 0:e3.eventReleaseInspector()}))},qe=function(e2){function n2(t2){var n3=e2.call(this,t2)||this;return n3.growingIO=t2,n3.hookSetTitle(),n3}return t(n2,e2),n2}(xe),je=function(e2){function n2(t2){var n3=e2.call(this,t2)||this;return n3.growingIO=t2,n3.hookSetTitle(),n3}return t(n2,e2),n2}(xe),He=function(e2){function n2(t2){var n3=e2.call(this,t2)||this;return n3.growingIO=t2,n3.hookSetTitle=function(){var e3,t3=null===(e3=n3.minip)||void 0===e3?void 0:e3.setNavigationBar,i2=n3;t3&&Object.defineProperty(n3.minip,"setNavigationBar",{writable:true,enumerable:true,configurable:true,value:function(){if(i2.growingIO.gioSDKInitialized){var e4=i2.getCurrentPath(),n4=(arguments[0]||{}).title;i2.growingIO.dataStore.eventHooks.currentPage.settedTitle[e4]=n4}return t3.apply(this,arguments)}})},n3.initImpression=function(e3){var t3,i2;null===(i2=null===(t3=n3.growingIO.plugins)||void 0===t3?void 0:t3.gioImpressionTracking)||void 0===i2||i2.main(e3,"selectAll")},n3.getStorageSync=function(e3){return my.getStorageSync({key:e3}).data},n3.setStorageSync=function(e3,t3){var i2;null===(i2=n3.minip)||void 0===i2||i2.setStorageSync({key:e3,data:t3})},n3.hookSetTitle(),n3}return t(n2,e2),n2}(xe),De=function(e2){function n2(t2){var n3=e2.call(this,t2)||this;return n3.growingIO=t2,n3.hookSetTitle(),n3}return t(n2,e2),n2}(xe),Fe=function(e2){function n2(t2){var n3=e2.call(this,t2)||this;return n3.growingIO=t2,n3.initImpression=function(e3){var t3,i2;null===(i2=null===(t3=n3.growingIO.plugins)||void 0===t3?void 0:t3.gioImpressionTracking)||void 0===i2||i2.main(e3,"selectAll")},n3.hookSetTitle(),n3}return t(n2,e2),n2}(xe),Le=function(e2){function n2(t2){var n3=e2.call(this,t2)||this;return n3.growingIO=t2,n3.hookSetTitle(),n3}return t(n2,e2),n2}(xe),Ue={wx:function(e2){function n2(t2){var n3=e2.call(this,t2)||this;return n3.growingIO=t2,n3.hookSetTitle(),n3}return t(n2,e2),n2}(xe),swan:Fe,my:He,qq:De,tt:Le,ks:je,jd:qe}[re()],Ve={},Be={}.hasOwnProperty;function Re(e2){try{return decodeURIComponent(e2.replace(/\+/g," "))}catch(e3){return null}}function Ke(e2){try{return encodeURIComponent(e2)}catch(e3){return null}}Ve.stringify=function(e2,t2){t2=t2||"";var n2,i2,r2=[];for(i2 in"string"!=typeof t2&&(t2="?"),e2)if(Be.call(e2,i2)){if((n2=e2[i2])||null!=n2&&!isNaN(n2)||(n2=""),i2=Ke(i2),n2=Ke(n2),null===i2||null===n2)continue;r2.push(i2+"="+n2)}return r2.length?t2+r2.join("&"):""},Ve.parse=function(e2){for(var t2,n2=/([^=?#&]+)=?([^&]*)/g,i2={};t2=n2.exec(e2);){var r2=Re(t2[1]),o2=Re(t2[2]);null===r2||null===o2||r2 in i2||(i2[r2]=o2)}return i2};var Ge=function(e2){var t2=this;this.growingIO=e2,this.main=function(e3){var i2,r2,o2,a2,u2,s2=t2.growingIO,l2=s2.sdkVersion,c2=s2.vdsConfig,g2=s2.platformConfig,d2=s2.minipInstance,p2=s2.userStore,f2=s2.dataStore,v2=f2.scene,h2=f2.eventHooks,m2=f2.locationData,I2=f2.lastVisitEvent,y2=d2.systemInfo,S2=void 0===y2?{}:y2,w2=d2.network,b2=void 0===w2?{}:w2,O2=S2.brand,P2=S2.model,_2=S2.platform,T2=S2.language,k2=S2.version,E2={appChannel:"scn:"+(v2||"NA"),appVersion:c2.version,dataSourceId:c2.dataSourceId,deviceBrand:O2,deviceId:p2.uid,deviceModel:P2,deviceType:X(_2,g2.name),domain:c2.appId,gioId:p2.gioId,language:T2,latitude:null==m2?void 0:m2.latitude,longitude:null==m2?void 0:m2.longitude,networkState:(null==b2?void 0:b2.networkType)||(null==b2?void 0:b2.type)||(null==b2?void 0:b2.subtype),operatingSystem:X(_2,g2.name),path:(null===(i2=h2.currentPage)||void 0===i2?void 0:i2.path)?null===(r2=h2.currentPage)||void 0===r2?void 0:r2.path:I2.path,platform:g2.platform,platformVersion:g2.name+(k2?" "+k2:""),query:(null===(o2=h2.currentPage)||void 0===o2?void 0:o2.path)?null===(a2=h2.currentPage)||void 0===a2?void 0:a2.query:I2.query,screenHeight:J(S2),screenWidth:Y(S2),sdkVersion:l2,sessionId:p2.sessionId,title:(null===(u2=h2.currentPage)||void 0===u2?void 0:u2.title)||d2.getPageTitle(d2.getCurrentPage()),timestamp:Date.now(),userId:p2.userId};return c2.enableIdMapping&&(E2.userKey=p2.userKey),K(c2.ignoreFields)||c2.ignoreFields.forEach((function(e4){B(E2,e4)})),K(e3)?E2:n(n({},E2),e3)}},$e=function(e2){var t2=this;this.growingIO=e2,this.main=function(e3,n2){var i2,r2,o2=Date.now(),a2=t2.growingIO,u2=a2.emitter,s2=a2.userStore,l2=a2.vdsConfig,c2=a2.platformConfig,g2=a2.uploader,d2=a2.dataStore,p2=a2.dataStore,f2=p2.keepAlive,v2=p2.lastCloseTime,h2=p2.eventHooks,m2=p2.saveStorageInfo;u2.emit("minipLifecycle",{event:"App "+e3,timestamp:o2,params:null!==(i2=n2[0])&&void 0!==i2?i2:{}}),l2.debug&&console.log("App:",e3,o2);var I2=c2.listeners.app;switch(u2.emit("onComposeBefore",{event:"App "+e3,params:null!==(r2=n2[0])&&void 0!==r2?r2:{}}),e3){case I2.appCreate:case I2.appShow:var y2=t2.enterParamsParse(n2[0]),S2=y2.path,w2=y2.query;v2?(Date.now()-v2>f2||d2.lastScene&&d2.scene!==d2.lastScene)&&(s2.sessionId="",h2.currentPage.time=void 0,t2.buildVisitEvent({path:S2,query:w2})):(t2.growingIO.dataStore.lastVisitEvent={path:S2,query:w2},t2.buildVisitEvent({path:S2,query:w2}));break;case I2.appClose:d2.lastScene=d2.scene,d2.lastCloseTime=Date.now(),m2(),s2.saveUserInfo(),t2.buildCloseEvent(),l2.forceLogin||g2.initiateRequest(true)}},this.enterParamsParse=function(e3){var n2=t2.growingIO,i2=n2.gioPlatform,r2=n2.minipInstance,o2=n2.dataStore,a2="",u2="",s2=function(e4,t3){void 0===t3&&(t3={}),a2=e4,u2=F(t3).map((function(e5){return e5+"="+t3[e5]})).join("&")};if("quickapp"!==i2&&r2.minip.canIUse("getEnterOptionsSync")){var l2=r2.minip.getEnterOptionsSync()||{},c2=l2.path,g2=l2.query,d2=l2.scene,p2=l2.referrerInfo,f2=e3.query||g2;if(s2(e3.path||c2,f2),o2.scene=e3.scene||d2,!K(p2)&&b(null==p2?void 0:p2.extraData)){var v2=[];L(p2.extraData,(function(e4,t3){!D(f2,t3)&&["string","number","boolean"].includes(G(e4))&&v2.push(t3+"="+e4)}));var h2=v2.join("&");u2=u2&&h2?u2+"&"+h2:u2||h2}}else s2(c2=e3.path,g2=e3.query),t2.parseScene(e3);return{path:a2,query:u2}},this.parseScene=function(e3){var n2,i2,r2=t2.growingIO,o2=r2.minipInstance,a2=r2.gioPlatform,u2=r2.dataStore;if("quickapp"===a2){var s2=o2.getAppSource(),l2=s2.extra,c2=s2.type;i2=(null==l2?void 0:l2.scene)||c2}else K(e3)||((null===(n2=null==e3?void 0:e3.query)||void 0===n2?void 0:n2.wxShoppingListScene)?i2=e3.query.wxShoppingListScene:(null==e3?void 0:e3.scene)?i2=e3.scene:u2.scene="NA");u2.scene=i2},this.buildVisitEvent=function(e3){var i2=t2.growingIO.dataStore,r2=i2.eventContextBuilder,o2=i2.eventInterceptor,a2=(null==e3?void 0:e3.query)||(null==e3?void 0:e3.q)||"";o2(n({eventType:"VISIT"},r2({path:(null==e3?void 0:e3.path)||(null==e3?void 0:e3.p)||"",query:"string"===G(a2)?a2:ge(a2)})))},this.buildCloseEvent=function(){var e3=t2.growingIO.dataStore,i2=e3.eventContextBuilder;(0,e3.eventInterceptor)(n({eventType:"APP_CLOSED"},i2()))}},Me=function(){function e2(e3){var t2=this;this.growingIO=e3,this.parsePage=function(e4,n2){var i2,r2=e4.route||e4.uri||e4.__route__||(null===(i2=null==e4?void 0:e4.$page)||void 0===i2?void 0:i2.fullPath)||"";K(n2)?t2.query=void 0:t2.query=t2.getQuery(n2),t2.path!==r2&&(t2.time=Date.now()),t2.path=r2,t2.title=t2.settedTitle[t2.path]||t2.growingIO.minipInstance.getPageTitle(e4||t2.growingIO.minipInstance.getCurrentPage())},this.getQuery=function(e4){var t3=n({},e4);return B(t3,"wxShoppingListScene"),ge(t3)},this.saveShareId=function(e4){(null==e4?void 0:e4.gioShareId)&&(t2.queryShareId=e4.gioShareId)},this.updateShareResult=function(e4,n2){var i2=t2.buildShareQuery(e4,n2),r2=i2[0],o2=i2[1],a2=i2[2];return n2.path=te(r2+(o2?"?"+o2:"")),n2.attributes=a2,n2.query=o2,n2},this.updateAppMessageResult=this.updateShareResult,this.updateTimelineResult=this.updateShareResult,this.updateAddFavoritesResult=function(e4,n2){var i2=t2.buildShareQuery(e4,n2,false),r2=i2[0],o2=i2[1];return n2.path=te(r2+(o2?"?"+o2:"")),n2.query=o2,n2},this.query=void 0,this.settedTitle={},this.pageProps={}}return e2.prototype.buildShareQuery=function(e3,t2,i2){var r2,o2,a2;void 0===i2&&(i2=true);var u2=e3.userStore,s2=ee(null!==(r2=t2.path)&&void 0!==r2?r2:""),l2=this.path,c2={},g2=D(t2,"path");if(g2&&(l2=_(s2),c2=de(T(s2))||{}),K(c2)&&(c2=de(null!==(o2=t2.query)&&void 0!==o2?o2:"")||{}),!g2&&K(c2)&&(c2=de(null!==(a2=this.query)&&void 0!==a2?a2:"")||{},E(F(c2)).forEach((function(e4){(["gioPreShareId","gioShareId","suid"].includes(e4)||e4.toLowerCase().startsWith("utm_"))&&B(c2,e4)}))),!i2)return[l2,ge(c2),{}];var d2={gioShareId:Z(),suid:u2.uid},p2={contentType:t2.contentType||c2.contentType,contentId:t2.contentId||c2.contentId,gioPreShareId:this.queryShareId};return L(d2,(function(e4,t3){e4||B(d2,t3)})),[l2,ge(n(n({},c2),d2)),n(n({},d2),p2)]},e2}(),Qe=function(e2){var t2=this;this.growingIO=e2,this.main=function(e3,n2,i2){var r2,o2,a2=Date.now(),u2=t2.growingIO,s2=u2.emitter,l2=u2.minipInstance,c2=u2.plugins,g2=u2.vdsConfig,d2=u2.platformConfig,p2=u2.dataStore,f2=p2.shareOut,v2=p2.eventHooks,h2=p2.toggleShareOut,m2=p2.buildAppMessageEvent,I2=p2.buildTimelineEvent,y2=p2.buildAddFavorites,S2=u2.inPlugin,w2=v2.currentPage;if(!t2.prevEvent[n2]||Date.now()-Number(t2.prevEvent[n2])>=50){e3||(e3=l2.getCurrentPage()),g2.wepy&&(e3.route=e3.$is),e3.route||(e3.route=l2.getCurrentPath(e3));var b2=e3.route||e3.uri||e3.__route__||(null===(r2=null==e3?void 0:e3.$page)||void 0===r2?void 0:r2.fullPath)||"";s2.emit("minipLifecycle",{event:"Page "+n2,timestamp:a2,params:{page:e3,args:i2[0]}}),g2.debug&&console.log("Page:",b2,"#",n2,a2),t2.prevEvent[n2]=Date.now();var P2=d2.listeners.page;switch(s2.emit("onComposeBefore",{page:e3,event:"Page "+n2,params:{page:e3,args:i2[0]}}),w2.lastLifecycle=w2.currentLifecycle,w2.currentLifecycle=n2,n2){case P2.pageLoad:t2.argQuery[b2]=V(e3,"options")||V(e3,"__displayReporter.query")||V(e3,"$page.query")||V(e3,"$wx.__displayReporter.query")||i2[0]||{},B(t2.argQuery[b2],"$taroTimestamp"),w2.parsePage(e3,t2.argQuery[b2]);break;case P2.pageShow:if(S2)break;f2&&v2.currentPage.time||(w2.parsePage(e3,t2.argQuery[b2]),v2.currentPage.time=Date.now(),t2.buildPageEvent()),h2(false),w2.saveShareId(t2.argQuery[b2]);break;case P2.pageHide:case P2.pageUnload:break;case P2.shareApp:h2(true),g2.followShare&&m2(i2);break;case P2.shareTime:g2.followShare&&I2(i2);break;case P2.addFavorites:y2(i2);break;case P2.tabTap:var _2=null===(o2=null==c2?void 0:c2.gioEventAutoTracking)||void 0===o2?void 0:o2.buildTabClickEvent;g2.autotrack&&_2&&O(_2)&&_2(i2[0])}n2===P2.pageUnload&&(w2.pageProps[b2]=void 0)}},this.buildPageEvent=function(){var e3=t2.growingIO,i2=e3.minipInstance,r2=e3.dataStore,o2=r2.scene,a2=r2.lastPageEvent,u2=r2.eventContextBuilder,s2=r2.eventInterceptor,l2=r2.eventHooks.currentPage,c2=n(n({eventType:"PAGE",referralPage:(null==a2?void 0:a2.path)||(null==a2?void 0:a2.p)||(o2?"scn:"+i2.scnPrefix+o2:null)},u2()),{timestamp:l2.time});K(l2.pageProps[c2.path])||(c2.attributes=l2.pageProps[c2.path]),s2(c2)},this.prevEvent={},this.argQuery={}},We=function(e2){var t2,o2,a2=this;this.growingIO=e2,this.isNormalFc=function(e3,t3){return O(t3)&&"constructor"!==e3},this.objectTraverse=function(e3,t3){Object.getOwnPropertyNames(e3).forEach((function(n2){return t3(n2,e3)})),D(e3,"methods")&&Object.getOwnPropertyNames(e3.methods).forEach((function(n2){e3[n2]||t3(n2,e3.methods)})),D(e3,"lifetimes")&&Object.getOwnPropertyNames(e3.lifetimes).forEach((function(n2){e3[n2]||t3(n2,e3.lifetimes)})),D(e3,"pageLifetimes")&&Object.getOwnPropertyNames(e3.pageLifetimes).forEach((function(n2){e3[n2]||t3(n2,e3.pageLifetimes)}))},this.supLifeFcs=function(e3,t3){P(a2[t3+"Handlers"])&&a2[t3+"Handlers"].forEach((function(t4){O(e3[t4])||["onShareAppMessage","onShareTimeline"].includes(t4)||(e3[t4]=function(){})}))},this.lifeFcEffects=function(e3,t3,o3){var u3=a2;return["onShareTimeline","onAddToFavorites"].includes(e3)?function(){for(var n2=[],i2=0;arguments.length>i2;i2++)n2[i2]=arguments[i2];var r2=t3.apply(this,n2);"onShareTimeline"===e3&&u3.growingIO.vdsConfig.followShare&&(r2=u3.currentPage.updateTimelineResult(u3.growingIO,null!=r2?r2:{})),"onAddToFavorites"===e3&&(r2=u3.currentPage.updateAddFavoritesResult(u3.growingIO,null!=r2?r2:{}));var a3=[].slice.call(n2);return r2&&a3.push(r2),u3["def"+o3+"Cbs"][e3].apply(this,a3),r2}:function(){for(var a3,s2=[],l2=0;arguments.length>l2;l2++)s2[l2]=arguments[l2];return i(this,void 0,void 0,(function(){var l3,c2,g2,d2,p2;return r$1(this,(function(f2){switch(f2.label){case 0:return f2.trys.push([0,5,,6]),"onShareAppMessage"!==e3?[3,4]:[4,t3.apply(this,s2)];case 1:return l3=null!==(a3=f2.sent())&&void 0!==a3?a3:{},"promise"!==G(l3.promise)?[3,3]:(c2=function(){return i(this,void 0,void 0,(function(){var e4;return r$1(this,(function(t4){return e4=l3,[2,new Promise((function(t5){var i2=setTimeout((function(){t5(n({},e4)),clearTimeout(i2),i2=void 0}),3e3);l3.promise.then((function(r2){t5(n({},e4=r2)),clearTimeout(i2),i2=void 0}))}))]}))}))},g2=[n({},l3)],[4,c2()]);case 2:l3=n.apply(void 0,g2.concat([f2.sent()])),B(l3,"promise"),f2.label=3;case 3:u3.growingIO.vdsConfig.followShare&&(l3=u3.currentPage.updateAppMessageResult(u3.growingIO,null!=l3?l3:{})),f2.label=4;case 4:return d2=[].slice.call(s2),l3&&d2.push(l3),u3["def"+o3+"Cbs"][e3].apply(this,d2),[3,6];case 5:return p2=f2.sent(),M(p2,"error"),[3,6];case 6:return"onShareAppMessage"!==e3&&(l3=t3.apply(this,s2)),u3.growingIO.emitter.emit("minipLifecycle",{event:o3+" "+e3+"End",timestamp:Date.now(),params:{instance:this,arguments:Array.from(s2)}}),[2,l3]}}))}))}},this.customFcEffects=function(e3,t3){var n2=a2;return function(){for(var i2,r2,o3=[],a3=0;arguments.length>a3;a3++)o3[a3]=arguments[a3];if(n2.growingIO.vdsConfig.autotrack&&n2.growingIO.plugins.gioEventAutoTracking)try{var u3=o3[0]||{};D(u3,"type")&&((null==u3?void 0:u3.currentTarget)||(null==u3?void 0:u3.target))||(u3=o3[o3.length-1]),((null==u3?void 0:u3.currentTarget)||(null==u3?void 0:u3.target))&&n2.actionEventTypes.includes(u3.type)&&(O(n2.actionEffects)||(n2.actionEffects=(null===(r2=null===(i2=n2.growingIO.plugins)||void 0===i2?void 0:i2.gioEventAutoTracking)||void 0===r2?void 0:r2.main)||function(){}),null==n2||n2.actionEffects(u3,e3))}catch(e4){M(e4,"error")}return t3.apply(this,o3)}},this.appApplyProxy=function(e3,t3){return a2.appHandlers.includes(e3)?a2.lifeFcEffects(e3,t3,"App"):a2.customFcEffects(e3,t3)},this.pageApplyProxy=function(e3,t3){return a2.pageHandlers.includes(e3)?a2.lifeFcEffects(e3,t3,"Page"):a2.customFcEffects(e3,t3)},this.appOverriding=function(e3){return a2.supLifeFcs(e3,"app"),a2.objectTraverse(e3,(function(e4,t3){a2.isNormalFc(e4,t3[e4])&&(t3[e4]=a2.appApplyProxy(e4,t3[e4]))})),e3},this.pageOverriding=function(e3){return a2.supLifeFcs(e3,"page"),a2.objectTraverse(e3,(function(e4,t3){a2.isNormalFc(e4,t3[e4])&&(t3[e4]=a2.pageApplyProxy(e4,t3[e4]))})),e3},this.componentOverriding=function(e3){return e3.methods||(e3.methods={}),a2.supLifeFcs(e3.methods,"page"),a2.objectTraverse(e3.methods,(function(e4,t3){a2.isNormalFc(e4,t3[e4])&&(t3[e4]=a2.pageApplyProxy(e4,t3[e4]))})),e3},this.growingApp=function(e3){return a2.originalApp(a2.appOverriding(e3))},this.growingPage=function(e3){return a2.originalPage(a2.pageOverriding(e3))},this.growingComponent=function(e3){return a2.originalComponent(a2.componentOverriding(e3))},this.growingBehavior=function(e3){return a2.originalBehavior(a2.componentOverriding(e3))},this.nativeGrowing=function(e3){void 0===e3&&(e3=["App","Page","Component","Behavior"]);var t3=a2,n2=a2.growingIO.platformConfig.hooks;if(e3.includes("App"))try{n2.App&&!a2.appHooked&&(App=function(){for(var e4=[],n3=0;arguments.length>n3;n3++)e4[n3]=arguments[n3];return t3.growingApp(e4[0])},a2.appHooked=true)}catch(e4){}if(e3.includes("Page"))try{n2.Page&&!a2.pageHooked&&(Page=function(){for(var e4=[],n3=0;arguments.length>n3;n3++)e4[n3]=arguments[n3];return t3.growingPage(e4[0])},a2.pageHooked=true)}catch(e4){}if(e3.includes("Component"))try{n2.Component&&!a2.componentHooked&&(Component=function(){for(var e4=[],n3=0;arguments.length>n3;n3++)e4[n3]=arguments[n3];return t3.growingComponent(e4[0])},a2.componentHooked=true)}catch(e4){}if(e3.includes("Behavior"))try{n2.Behavior&&!a2.behaviorHooked&&(Behavior=function(){for(var e4=[],n3=0;arguments.length>n3;n3++)e4[n3]=arguments[n3];return t3.growingBehavior(e4[0])},a2.behaviorHooked=true)}catch(e4){}},this.initEventHooks=function(){var e3=a2,t3=a2.growingIO,n2=t3.platformConfig,i2=t3.gioPlatform;a2.appHandlers.forEach((function(t4){a2.defAppCbs[t4]=function(){for(var n3=[],i3=0;arguments.length>i3;i3++)n3[i3]=arguments[i3];e3.appEffects.main(t4,n3)}})),a2.pageHandlers.forEach((function(t4){a2.defPageCbs[t4]=function(){for(var n3=[],i3=0;arguments.length>i3;i3++)n3[i3]=arguments[i3];e3.pageEffects.main(this,t4,n3)}})),n2.canHook?a2.nativeGrowing():"quickapp"===i2&&(window.GioApp=function(){for(var t4=[],n3=0;arguments.length>n3;n3++)t4[n3]=arguments[n3];return e3.appOverriding(t4[0])},window.GioPage=function(){for(var t4=[],n3=0;arguments.length>n3;n3++)t4[n3]=arguments[n3];return e3.pageOverriding(t4[0])},window.GioComponent=function(){for(var t4=[],n3=0;arguments.length>n3;n3++)t4[n3]=arguments[n3];return e3.pageOverriding(t4[0])}),a2.growingIO.App=Q((function(){return App})),a2.growingIO.Page=Q((function(){return Page})),a2.growingIO.Component=Q((function(){return Component})),a2.growingIO.Behavior=Q((function(){return Behavior})),"quickapp"===i2&&(a2.growingIO.GioApp=window.GioApp,a2.growingIO.GioPage=window.GioPage,a2.growingIO.GioComponent=window.GioComponent)};var u2=null===(t2=this.growingIO)||void 0===t2?void 0:t2.platformConfig;this.defAppCbs={},this.defPageCbs={},this.appHandlers=u2.appHandlers,this.pageHandlers=u2.pageHandlers,this.actionEventTypes=u2.actionEventTypes,this.originalApp=u2.originalApp,this.originalPage=u2.originalPage,this.originalComponent=u2.originalComponent,this.originalBehavior=null!==(o2=u2.originalBehavior)&&void 0!==o2?o2:function(){},this.appEffects=new $e(this.growingIO),this.pageEffects=new Qe(this.growingIO),this.currentPage=new Me(this.growingIO)},ze=function(){function e2(e3){var t2=this;this.growingIO=e3,this.ALLOW_SETTING=Object.assign({},a,"saas"===this.growingIO.gioEnvironment?s:u),this.allowOptKeys=F(this.ALLOW_SETTING),this.initStorageInfo=function(){var e4=t2.growingIO.minipInstance,n2=ae(),i2=n2.gio_esid?n2.gio_esid:e4.getStorageSync(t2.esidStorageName);i2=b(i2)&&!I(i2)?i2:{},t2._esid={},F(i2).forEach((function(e5){t2._esid[e5]=Number.isNaN(Number(i2[e5]))||i2[e5]>=1e9||1>i2[e5]?1:i2[e5]})),U(i2,t2._esid)||e4.setStorageSync(t2.esidStorageName,t2._esid);var r2=n2.gio_gsid?n2.gio_gsid:Number.parseInt(e4.getStorageSync(t2.gsidStorageName),10);t2._gsid=Number.isNaN(r2)||r2>=1e9||1>r2?1:r2,t2._stid=t2._gsid,r2!==t2._gsid&&e4.setStorageSync(t2.gsidStorageName,t2._gsid)},this.saveStorageInfo=function(){var e4=t2.growingIO.minipInstance;t2._stid=t2._gsid,ce(e4,[{key:t2.esidStorageName,value:t2._esid},{key:t2.gsidStorageName,value:t2._gsid}])},this.initOptions=function(e4){var i2,r2,o2,a2,u2,s2,l2,c2,g2,d2,p2,f2=e4.projectId,v2=e4.dataSourceId,I2=e4.appId,y2={};t2.allowOptKeys.forEach((function(n2){var i3=t2.ALLOW_SETTING[n2].type;(P(i3)?i3.includes(G(e4[n2])):G(e4[n2])===t2.ALLOW_SETTING[n2].type)?"ignoreFields"===n2?y2.ignoreFields=e4.ignoreFields.filter((function(e5){return h.includes(e5)})):"extraParams"===n2?y2.extraParams=e4.extraParams.filter((function(e5){return m.includes(e5)})):y2[n2]=e4[n2]:y2[n2]=t2.ALLOW_SETTING[n2].default})),y2.uploadInterval=Math.round(y2.uploadInterval),(Number.isNaN(y2.uploadInterval)||0>y2.uploadInterval||y2.uploadInterval>2e3)&&(y2.uploadInterval=1e3),!isNaN(Number(y2.requestTimeout))&&y2.requestTimeout>0||(y2.requestTimeout=5e3),t2.growingIO.vdsConfig=n(n({},y2),{projectId:f2,dataSourceId:v2,appId:I2,performance:{monitor:null===(r2=null===(i2=y2.performance)||void 0===i2?void 0:i2.monitor)||void 0===r2||r2,exception:null===(a2=null===(o2=y2.performance)||void 0===o2?void 0:o2.exception)||void 0===a2||a2,network:null!==(s2=null===(u2=y2.performance)||void 0===u2?void 0:u2.network)&&void 0!==s2&&s2}}),t2.keepAlive=y2.keepAlive,y2.dataCollect||M("已关闭数据采集","info"),y2.autotrack||M("已关闭无埋点","info"),t2.growingIO.inPlugin&&(t2.growingIO.vdsConfig.autotrack=false);var S2=y2.cml,w2=y2.uniVue,b2=y2.wepy,O2=y2.taro,_2=y2.remax;"full"!==t2.growingIO.gioFramework||S2||w2||b2||O2||_2||(t2.growingIO.platformConfig.canHook=true),t2.growingIO.inPlugin||t2.eventHooks.initEventHooks();var T2=t2.growingIO.plugins;S2&&(null===(l2=null==T2?void 0:T2.gioChameleonAdapter)||void 0===l2||l2.main()),w2&&(null===(c2=null==T2?void 0:T2.gioUniAppAdapter)||void 0===c2||c2.main()),b2&&(null===(g2=null==T2?void 0:T2.gioWepyAdapter)||void 0===g2||g2.main()),O2&&(null===(d2=null==T2?void 0:T2.gioTaroAdapter)||void 0===d2||d2.main()),_2&&(null===(p2=null==T2?void 0:T2.gioRemaxAdapter)||void 0===p2||p2.main())},this.setOption=function(e4,n2){var i2,r2=t2.growingIO,o2=r2.vdsConfig,a2=r2.callError,u2=r2.uploader,s2=r2.emitter,l2=y(e4)&&t2.allowOptKeys.includes(e4),c2=l2&&typeof n2===(null===(i2=t2.ALLOW_SETTING[e4])||void 0===i2?void 0:i2.type);if(l2&&c2){if("dataCollect"===e4&&false===o2.dataCollect&&true===n2)var g2=setTimeout((function(){var e5=t2.eventHooks.currentPage,n3=e5.path,i3=e5.settedTitle;t2.eventHooks.currentPage.title=i3[n3]||t2.growingIO.minipInstance.getPageTitle(t2.growingIO.minipInstance.getCurrentPage()),t2.sendVisit(),t2.eventHooks.currentPage.time=Date.now(),t2.sendPage(),clearTimeout(g2),g2=null}));return o2[e4]=n2,["host","scheme"].includes(e4)&&(null==u2||u2.generateURL()),s2.emit("OPTION_CHANGE",{optionName:e4,optionValue:n2}),true}return a2("setOption > "+e4),false},this.getOption=function(e4){var i2=t2.growingIO,r2=i2.vdsConfig,o2=i2.callError;return e4&&D(r2,A(e4))?r2[A(e4)]:I(e4)?n({},r2):void o2("getOption > "+e4)},this.toggleShareOut=function(e4){w(e4)?t2.shareOut=e4:t2.shareOut=!t2.shareOut},this.sendVisit=function(){t2.eventHooks.appEffects.buildVisitEvent(t2.lastVisitEvent)},this.sendPage=function(){t2.eventHooks.pageEffects.buildPageEvent()},this.eventInterceptor=function(e4){var i2=t2.growingIO.minipInstance,r2=i2.systemInfo,a2=i2.network;K(r2)||K(a2)?t2.interceptEvents.push(e4):K(t2.interceptEvents)?t2.eventConverter(e4):(o(o([],t2.interceptEvents),[e4]).forEach((function(e5){var i3=n(n({},e5),t2.eventContextBuilder({path:e5.path,query:e5.query,timestamp:e5.timestamp}));t2.eventConverter(i3)})),t2.interceptEvents=[])},this.eventReleaseInspector=function(){var e4=t2.growingIO.minipInstance,i2=e4.systemInfo,r2=e4.network;i2&&r2&&!K(t2.interceptEvents)&&(o([],t2.interceptEvents).forEach((function(e5){var i3=n(n({},e5),t2.eventContextBuilder({path:e5.path,query:e5.query,timestamp:e5.timestamp}));t2.eventConverter(i3)})),t2.interceptEvents=[])},this.buildAppMessageEvent=function(e4){},this.buildTimelineEvent=function(e4){},this.buildAddFavorites=function(e4){},this.esidStorageName="_growing_esid_",this.gsidStorageName="_growing_gsid_",this.initStorageInfo(),this.eventContextBuilder=new Ge(this.growingIO).main,this.eventHooks=new We(this.growingIO),this.shareOut=false,this.lastVisitEvent={},this.lastPageEvent={},this.locationData={},this.generalProps={},this.interceptEvents=[],this.growingIO.emitter.on("onComposeAfter",(function(e4){var n2=e4.composedEvent;"VISIT"!==n2.eventType&&"vst"!==n2.t||(t2.lastVisitEvent=n2),"PAGE"!==n2.eventType&&"page"!==n2.t||(t2.lastPageEvent=n2)}))}return Object.defineProperty(e2.prototype,"esid",{get:function(){var e3=ae(),t2=oe(e3);return!K(t2)&&e3.gio_esid?e3.gio_esid:this._esid},set:function(e3){var t2={};F(e3).forEach((function(n2){t2[n2]=Number.isNaN(e3[n2])||e3[n2]>=1e9||1>e3[n2]?1:e3[n2]})),U(t2,this._esid)||(this._esid=t2,ae().gio_esid=this._esid)},enumerable:false,configurable:true}),Object.defineProperty(e2.prototype,"gsid",{get:function(){var e3=ae(),t2=oe(e3);return!K(t2)&&e3.gio_gsid?e3.gio_gsid:this._gsid},set:function(e3){var t2=Number.parseInt(e3,10);this._gsid=Number.isNaN(t2)||e3>=1e9||1>e3?1:e3,ae().gio_gsid=this._gsid,10>this._gsid-this._stid||this.saveStorageInfo()},enumerable:false,configurable:true}),Object.defineProperty(e2.prototype,"scene",{get:function(){return this._scene},set:function(e3){var t2=this._scene;this._scene=e3,t2!==this._scene&&this.growingIO.emitter.emit("SCENE_UPDATE",{newScene:e3,oldScene:t2})},enumerable:false,configurable:true}),e2}(),Ze=function(e2){function i2(t2){var i3=e2.call(this,t2)||this;return i3.growingIO=t2,i3.eventConverter=function(e3){var t3,r2=i3.growingIO,o2=r2.vdsConfig,a2=r2.dataStore,u2=r2.uploader;if(o2.dataCollect){e3.globalSequenceId=a2.gsid,e3.eventSequenceId=a2.esid[e3.eventType]||1;var s2={};L(e3,(function(e4,t4){var n2;if("element"===t4){var i4=null!==(n2=_(e4))&&void 0!==n2?n2:{};L(i4,(function(e5,t5){K(e5)&&0!==e5||(s2[t5]=e5)}))}else K(e4)&&0!==e4||(s2[t4]=e4)})),i3.growingIO.dataStore.gsid+=1,i3.growingIO.dataStore.esid=n(n({},i3.growingIO.dataStore.esid),((t3={})[s2.eventType]=(i3.growingIO.dataStore.esid[s2.eventType]||1)+1,t3)),i3.growingIO.emitter.emit("onComposeAfter",{composedEvent:s2}),u2.commitRequest(s2)}},i3.buildAppMessageEvent=function(e3){var t3,r2,o2=e3[0];2>e3.length?1===e3.length&&(r2=o2):r2=e3[1];var a2=i3.growingIO.dataStore,u2=a2.eventContextBuilder,s2=a2.eventInterceptor,l2=a2.eventHooks.currentPage,c2=((null==r2?void 0:r2.path)||"").split("?");s2(n({eventType:"CUSTOM",eventName:"$mp_on_share",pageShowTimestamp:l2.time,attributes:n({$from:o2.from,$target:null===(t3=null==o2?void 0:o2.target)||void 0===t3?void 0:t3.id,$share_title:null==r2?void 0:r2.title,$share_path:_(c2),$share_query:c2[1]},null==r2?void 0:r2.attributes)},u2()))},i3.buildTimelineEvent=function(e3){var t3,r2,o2=e3[0];2>e3.length?1===e3.length&&(r2=o2):r2=e3[1];var a2=i3.growingIO.dataStore,u2=a2.eventContextBuilder,s2=a2.eventInterceptor,l2=a2.eventHooks.currentPage,c2=((null==r2?void 0:r2.path)||"").split("?");s2(n({eventType:"CUSTOM",eventName:"$mp_share_timeline",pageShowTimestamp:l2.time,attributes:n({$target:null===(t3=null==o2?void 0:o2.target)||void 0===t3?void 0:t3.id,$share_title:null==r2?void 0:r2.title,$share_path:_(c2),$share_query:c2[1]},null==r2?void 0:r2.attributes)},u2()))},i3.buildAddFavorites=function(e3){var t3,r2=e3[0];2>e3.length?1===e3.length&&(t3=r2):t3=e3[1];var o2=i3.growingIO.dataStore,a2=o2.eventContextBuilder,u2=o2.eventInterceptor,s2=o2.eventHooks.currentPage,l2=((null==t3?void 0:t3.path)||"").split("?");u2(n({eventType:"CUSTOM",eventName:"$mp_add_favorites",pageShowTimestamp:s2.time,attributes:{$share_title:null==t3?void 0:t3.title,$share_path:_(l2),$share_query:l2[1]}},a2()))},i3.trackTimers={},i3}return t(i2,e2),i2}(ze),Je=function(e2){function n2(t2){var n3=e2.call(this,t2)||this;return n3.growingIO=t2,n3.generateURL=function(){var e3=n3.growingIO.vdsConfig,t3=e3.scheme,i2=e3.host,r2=void 0===i2?"":i2,o2=e3.projectId;t3&&q(t3,"http")?j(A(t3),"://")||(t3+="://"):t3="https://",r2=r2.replace("http://","").replace("https://",""),n3.requestURL=""+t3+r2+"/v3/projects/"+o2+"/collect"},n3.generateURL(),n3}return t(n2,e2),n2}((function(e2){var t2=this;this.growingIO=e2,this.commitRequest=function(e3){var i2=n({},e3);t2.growingIO.vdsConfig.forceLogin?t2.hoardingQueue.push(i2):(t2.requestQueue.push(i2),t2.initiateRequest())},this.initiateRequest=function(e3){var n2,i2,r2=t2.growingIO,a2=r2.plugins,u2=r2.vdsConfig;o(o([],t2.hoardingQueue),t2.requestQueue).length>0&&t2.requestingNum<t2.requestLimit&&((null===(n2=null==u2?void 0:u2.tbConfig)||void 0===n2?void 0:n2.cloudFuncSend)?null===(i2=null==a2?void 0:a2.gioTaobaoSendAdapter)||void 0===i2||i2.singleInvoke():e3?t2.batchInvoke():t2.batchInvokeThrottle())},this.batchInvoke=function(){var e3,n2,i2=t2.growingIO,r2=i2.vdsConfig,a2=i2.plugins,u2=i2.emitter,s2=o(o([],t2.hoardingQueue),t2.requestQueue);s2.length>50?(s2=s2.slice(0,50),t2.hoardingQueue.length>50?t2.hoardingQueue=t2.hoardingQueue.slice(50):(t2.requestQueue=t2.requestQueue.slice(50-t2.hoardingQueue.length),t2.hoardingQueue=[])):(t2.hoardingQueue=[],t2.requestQueue=[]);var l2=s2.filter((function(e4){return(t2.retryIds[e4.globalSequenceId||e4.esid]||0)<=t2.retryLimit}));K(l2)||(r2.debug&&console.log("[GrowingIO Debug]:",JSON.stringify(l2,null,2)),u2.emit("onSendBefore",{requestData:l2}),(null===(e3=null==r2?void 0:r2.tbConfig)||void 0===e3?void 0:e3.cloudAppId)?null===(n2=null==a2?void 0:a2.gioTaobaoSendAdapter)||void 0===n2||n2.tbCloudAppInvoke(l2):t2.normalBatchInvoke(l2))},this.batchInvokeThrottle=R(this.batchInvoke,this.growingIO.vdsConfig.uploadInterval,false,false),this.normalBatchInvoke=function(e3){var n2,i2=t2.growingIO,r2=i2.minipInstance,a2=i2.vdsConfig,u2=i2.emitter,s2=i2.plugins,l2=i2.gioEnvironment;t2.requestingNum+=1;var c2={"content-type":"jdp"===r2.platform?"application/json":"application/json;charset=UTF-8"},g2=o([],e3),d2=a2.compress&&(null===(n2=null==s2?void 0:s2.gioCompress)||void 0===n2?void 0:n2.compressToUTF16);"cdp"===l2&&d2&&(g2=s2.gioCompress.compressToUTF16(JSON.stringify(e3)),c2["X-Compress-Codec"]="1"),r2.request({url:t2.requestURL+"?stm="+Date.now()+("cdp"===l2?"&compress="+(d2?"1":"0"):""),header:c2,method:"POST",data:g2,timeout:a2.requestTimeout,fail:function(n3){[200,204].includes(n3.code)||(P(e3)?e3.forEach((function(e4){t2.requestFailFn(e4)})):t2.requestFailFn(e3),M("请求失败!"+JSON.stringify(n3),"error"))},complete:function(e4){t2.requestingNum-=1,u2.emit("onSendAfter",{result:e4}),t2.initiateRequest()}})},this.requestFailFn=function(e3){if(t2.retryIds[e3.globalSequenceId||e3.esid]||(t2.retryIds[e3.globalSequenceId||e3.esid]=0),t2.retryIds[e3.globalSequenceId||e3.esid]+=1,!t2.requestQueue.some((function(t3){return t3.globalSequenceId===e3.globalSequenceId&&t3.esid===e3.esid})))var n2=setTimeout((function(){K(t2.requestQueue)?(t2.requestQueue.push(e3),t2.initiateRequest()):t2.requestQueue.push(e3),clearTimeout(n2),n2=null}),800)},this.hoardingQueue=[],this.requestQueue=[],this.requestLimit=3,this.retryLimit=2,this.retryIds={},this.requestingNum=0,this.requestURL=""})),Ye=new(function(e2){function i2(){var t2,i3,r2=e2.call(this)||this;return r2.registerPlugins=function(e3){r2.plugins.pluginItems=o(o([],r2.plugins.pluginItems),e3),r2.plugins.installAll(e3)},r2.initCallback=function(){r2.uploader=new Je(r2),r2.vdsConfig.enableIdMapping||(r2.userStore.userKey="")},r2.setTrackerScheme=function(e3){["http","https"].includes(e3)?(r2.dataStore.setOption("scheme",e3),r2.notRecommended()):r2.callError("scheme",false)},r2.setTrackerHost=function(e3){ve.test(e3)||he.test(e3)?(r2.dataStore.setOption("host",e3),r2.notRecommended()):r2.callError("host",false)},r2.identify=function(e3){if(r2.vdsConfig.forceLogin){if(!fe(e3))return void r2.callError("identify");var t3=A(e3).slice(0,1e3);r2.userStore.uid=t3,r2.uploader.hoardingQueue.forEach((function(e4,n2){return r2.uploader.hoardingQueue[n2].deviceId=t3})),r2.dataStore.setOption("forceLogin",false),r2.uploader.initiateRequest(true)}else r2.callError("identify",false,"forceLogin未开启")},r2.setUserAttributes=function(e3){var t3,n2;!K(e3)&&b(e3)?null===(n2=null===(t3=r2.plugins)||void 0===t3?void 0:t3.gioCustomTracking)||void 0===n2||n2.buildUserAttributesEvent(e3):r2.callError("setUserAttributes")},r2.setUserId=function(e3,t3){if(fe(e3)){var n2=r2.userStore.gioId;e3=A(e3).slice(0,1e3),t3=A(t3).slice(0,1e3),r2.userStore.userId=e3,r2.vdsConfig.enableIdMapping&&(r2.userStore.userKey=I(t3)?"":t3),r2.reissueLogic(n2,e3)}else r2.callError("setUserId")},r2.clearUserId=function(){r2.userStore.userId=void 0,r2.userStore.userKey=void 0},r2.track=function(e3,t3,i4){var o2,a2;((null===(a2=null===(o2=r2.plugins)||void 0===o2?void 0:o2.gioCustomTracking)||void 0===a2?void 0:a2.buildCustomEvent)||function(){})(e3,n(n({},r2.dataStore.generalProps),b(t3)&&!K(t3)?t3:{}),i4)},r2.reissueLogic=function(e3,t3){e3&&e3!==t3&&(r2.userStore.sessionId="",r2.dataStore.sendVisit()),e3||e3===t3||r2.dataStore.sendVisit()},r2.trackTimerStart=function(e3){return!!r2.vdsConfig.dataCollect&&z(e3,(function(){var t3=Z();return r2.dataStore.trackTimers[t3]={eventName:e3,leng:0,start:+Date.now()},t3}))},r2.trackTimerPause=function(e3){if(e3&&r2.dataStore.trackTimers[e3]){var t3=r2.dataStore.trackTimers[e3];return t3.start&&(t3.leng=t3.leng+(+Date.now()-t3.start)),t3.start=0,true}return false},r2.trackTimerResume=function(e3){if(e3&&r2.dataStore.trackTimers[e3]){var t3=r2.dataStore.trackTimers[e3];return 0===t3.start&&(t3.start=+Date.now()),true}return false},r2.trackTimerEnd=function(e3,t3){if(r2.vdsConfig.dataCollect){if(e3&&r2.dataStore.trackTimers[e3]){var i4=r2.dataStore.trackTimers[e3];if(0!==i4.start){var o2=+Date.now()-i4.start;i4.leng=o2>0?i4.leng+o2:0}return r2.track(i4.eventName,n(n({},t3),{event_duration:i4.leng>864e5?0:i4.leng/1e3})),r2.removeTimer(e3),true}return M("未查找到对应的计时器，请检查!","error"),false}return false},r2.removeTimer=function(e3){return!(!e3||!r2.dataStore.trackTimers[e3]||(delete r2.dataStore.trackTimers[e3],0))},r2.clearTrackTimer=function(){r2.dataStore.trackTimers={}},r2.dataStore=new Ze(r2),O(null===(t2=r2.minipInstance.minip)||void 0===t2?void 0:t2.onAppHide)&&r2.minipInstance.minip.onAppHide((function(){L(r2.dataStore.trackTimers,(function(e3){e3.start&&(e3.leng=e3.leng+(+Date.now()-e3.start))}))})),O(null===(i3=r2.minipInstance.minip)||void 0===i3?void 0:i3.onAppShow)&&r2.minipInstance.minip.onAppShow((function(){L(r2.dataStore.trackTimers,(function(e3){e3.start&&(e3.start=+Date.now())}))})),r2.emitter.on("OPTION_CHANGE",(function(e3){var t3=e3.optionName,n2=e3.optionValue;"dataCollect"===t3&&false===n2&&r2.clearTrackTimer()})),r2}return t(i2,e2),i2}((function(){var e2,t2=this;this.init=function(e3){return M("Gio小程序SDK 初始化中...","info"),t2.dataStore.initOptions(e3),t2.gioSDKInitialized=true,t2.vdsConfig.subpackage||(ae().__gio__=function(){return t2}),null==t2||t2.initCallback(),t2.emitter.emit("SDK_INITIALIZED",t2),M("Gio小程序SDK 初始化完成！","success"),t2.vdsConfig.forceLogin&&M("forceLogin已开启，请调用 identify 方法设置 openId 以继续上报!","info"),t2.inPlugin&&t2.dataStore.sendVisit(),true},this.setDataCollect=function(e3){t2.setOption("dataCollect",!!e3),t2.notRecommended()},this.setAutotrack=function(e3){t2.setOption("autotrack",!!e3),t2.notRecommended()},this.enableDebug=function(e3){t2.setOption("debug",!!e3),t2.notRecommended()},this.setOption=function(e3,n2){if(p.includes(e3)){var i2=t2.dataStore.setOption(e3,n2);return i2&&f[e3]&&M("已"+(n2?"开启":"关闭")+f[e3],"info"),i2}return M("不存在可修改的配置项："+e3+"，请检查后重试!","warn"),false},this.getDeviceId=function(){return t2.userStore.uid},this.getOption=function(e3){return t2.dataStore.getOption(e3)},this.getGioInfo=function(){var e3=t2.userStore,n2=e3.uid,i2=e3.userId,r2=e3.userKey,o2=e3.sessionId,a2=e3.gioId,u2=t2.vdsConfig,s2=u2.projectId,l2=u2.appId,c2=u2.dataSourceId,g2=u2.dataCollect,d2=u2.enableIdMapping,p2=u2.extraParams,f2=u2.ignoreFields,v2={gioappid:l2,giocs1:i2||"",gioplatform:t2.platformConfig.platform,gioprojectid:s2,gios:o2,giou:n2};return"cdp"===t2.gioEnvironment&&(v2.giodatacollect=g2,v2.giodatasourceid=c2,v2.gioid=a2||"",d2&&(v2.giouserkey=r2),K(p2)||p2.forEach((function(e4){f2.includes(e4)||(v2["gio"+e4]=t2.dataStore.lastVisitEvent[e4])}))),Ve.stringify(v2)},this.setLocation=function(e3,n2){var i2={latitude:e3,longitude:n2},r2=t2.dataStore.locationData,o2=function(e4){return e4>=-180&&180>=e4};o2(e3)&&o2(n2)&&(r2.latitude!==e3||r2.longitude!==n2)&&(t2.dataStore.locationData=i2,t2.dataStore.sendVisit())},this.setGeneralProps=function(e3){b(e3)&&!K(e3)?(t2.dataStore.generalProps=n(n({},t2.dataStore.generalProps),e3),F(t2.dataStore.generalProps).forEach((function(e4){[void 0,null].includes(t2.dataStore.generalProps[e4])&&(t2.dataStore.generalProps[e4]="")}))):t2.callError("setGeneralProps")},this.clearGeneralProps=function(e3){P(e3)&&!K(e3)?e3.forEach((function(e4){B(t2.dataStore.generalProps,e4)})):t2.dataStore.generalProps={}},this.setPageAttributes=function(e3){var n2=t2.dataStore.eventHooks.currentPage,i2=t2.minipInstance.getCurrentPath();["onLoad","attached"].includes(n2.currentLifecycle)&&(n2.pageProps[i2]=se(e3))},this.updateImpression=function(){M("当前未集成半自动浏览插件，请集成插件后再调用 updateImpression!","warn")},this.notRecommended=function(){return M("不推荐的方法使用，建议使用 gio('setOption', [optionName], [value])!","info")},this.callError=function(e3,t3,n2){return void 0===t3&&(t3=true),void 0===n2&&(n2="参数不合法"),M((t3?"调用":"设置")+" "+e3+" 失败，"+n2+"!","warn")},this.utils=n(n(n({},$),pe),{qs:Ve}),this.emitter={all:e2=e2||new Map,on:function(t3,n2){var i2=e2.get(t3);i2?i2.push(n2):e2.set(t3,[n2])},off:function(t3,n2){var i2=e2.get(t3);i2&&(n2?i2.splice(i2.indexOf(n2)>>>0,1):e2.set(t3,[]))},emit:function(t3,n2){var i2=e2.get(t3);i2&&i2.slice().map((function(e3){e3(n2)})),(i2=e2.get("*"))&&i2.slice().map((function(e3){e3(t3,n2)}))}},this.gioPlatform=re(),this.gioFramework="uniapp",this.gioEnvironment="cdp",this.sdkVersion="3.8.18";try{"quickapp"===this.gioPlatform||O(getApp)&&O(App)?this.inPlugin=false:this.inPlugin=true}catch(e3){this.inPlugin=true}this.inPlugin&&M("未检测到小程序实例，自动切换为插件模式!","info"),this.platformConfig=Ne,this.minipInstance=new Ue(this),this.userStore=new we(this),this.plugins=new Se(this),this.plugins.innerPluginInit()}))),Xe=function(){var e2,t2=arguments[0];if(y(t2)&&d.includes(t2)&&Ye[t2]){var i2=k(Array.from(arguments));if("init"===t2){var r2=!(e2=Ye).vdsConfig&&!e2.gioSDKInitialized||(M("SDK重复初始化，请检查是否重复加载SDK或接入其他平台SDK导致冲突!","warn"),false),o2=function(e3){return!!E(e3)||(M('SDK初始化失败，请使用 gdp("init", "您的GrowingIO项目 accountId", "您项目的 dataSourceId", "你的应用 AppId", options); 进行初始化!',"error"),false)}(i2),a2=function(e3){var t3=e3[0],n2=T(e3);return fe(t3)?(b(n2)&&n2||(n2={}),{projectId:t3,userOptions:n2}):(M("SDK初始化失败，accountId 参数不合法!","error"),false)}(i2),u2=function(e3){var t3=e3[1],n2=e3[2],i3=T(e3);return t3&&y(t3)?fe(n2)?{dataSourceId:t3,appId:n2,cdpOptions:i3}:(M("SDK初始化失败，appId 参数不合法!","error"),false):(M("SDK初始化失败，dataSourceId 参数不合法!","error"),false)}(i2);if(r2&&o2&&a2&&u2){var s2=a2.projectId,l2=u2.dataSourceId,c2=u2.appId,g2=u2.cdpOptions;return Ye.init(n(n({},g2),{projectId:s2,dataSourceId:l2,appId:c2}))}}else if("registerPlugins"===t2)Ye.registerPlugins(i2[0]);else{if(Ye.gioSDKInitialized&&Ye.vdsConfig)return Ye[t2].apply(Ye,i2);M("SDK未初始化!","error")}}else v.includes(t2)?M("方法 "+A(t2)+" 已被弃用!","warn"):M("不存在名为 "+A(t2)+" 的方法调用!","error")};ue().gdp=Xe,ue().gioEnvironment="cdp",ue().gioSDKVersion=Ye.sdkVersion;var r={name:"gioCompress",method:function(r2){var o2=this;this.growingIO=r2,this.compressToUTF16=function(r3){var e2=o2;return null===r3?"":o2._compress(r3,15,(function(r4){return e2.f(r4+32)}))+" "},this.f=String.fromCharCode,this._compress=function(r3,o3,e2){if(null===r3)return"";var h2,t2,s2,f2={},a2={},p2="",n2="",u2="",i2=2,l2=3,c2=2,d2=[],w2=0,C2=0;for(s2=0;s2<r3.length;s2+=1)if(p2=r3.charAt(s2),{}.hasOwnProperty.call(f2,p2)||(f2[p2]=l2++,a2[p2]=true),n2=u2+p2,{}.hasOwnProperty.call(f2,n2))u2=n2;else{if({}.hasOwnProperty.call(a2,u2)){if(256>u2.charCodeAt(0)){for(h2=0;c2>h2;h2++)w2<<=1,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++;for(t2=u2.charCodeAt(0),h2=0;8>h2;h2++)w2=w2<<1|1&t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2>>=1}else{for(t2=1,h2=0;c2>h2;h2++)w2=w2<<1|t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2=0;for(t2=u2.charCodeAt(0),h2=0;16>h2;h2++)w2=w2<<1|1&t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2>>=1}0==--i2&&(i2=Math.pow(2,c2),c2++),delete a2[u2]}else for(t2=f2[u2],h2=0;c2>h2;h2++)w2=w2<<1|1&t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2>>=1;0==--i2&&(i2=Math.pow(2,c2),c2++),f2[n2]=l2++,u2=p2+""}if(""!==u2){if({}.hasOwnProperty.call(a2,u2)){if(256>u2.charCodeAt(0)){for(h2=0;c2>h2;h2++)w2<<=1,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++;for(t2=u2.charCodeAt(0),h2=0;8>h2;h2++)w2=w2<<1|1&t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2>>=1}else{for(t2=1,h2=0;c2>h2;h2++)w2=w2<<1|t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2=0;for(t2=u2.charCodeAt(0),h2=0;16>h2;h2++)w2=w2<<1|1&t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2>>=1}0==--i2&&(i2=Math.pow(2,c2),c2++),delete a2[u2]}else for(t2=f2[u2],h2=0;c2>h2;h2++)w2=w2<<1|1&t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2>>=1;0==--i2&&(i2=Math.pow(2,c2),c2++)}for(t2=2,h2=0;c2>h2;h2++)w2=w2<<1|1&t2,C2===o3-1?(C2=0,d2.push(e2(w2)),w2=0):C2++,t2>>=1;for(;;){if(w2<<=1,C2===o3-1){d2.push(e2(w2));break}C2++}return d2.join("")}}};
/*!
  * @intlify/shared v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */const hasSymbol=typeof Symbol==="function"&&typeof Symbol.toStringTag==="symbol";const makeSymbol=name=>hasSymbol?Symbol(name):name;const generateFormatCacheKey=(locale,key,source)=>friendlyJSONstringify({l:locale,k:key,s:source});const friendlyJSONstringify=json=>JSON.stringify(json).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027");const isNumber$1=val=>typeof val==="number"&&isFinite(val);const isDate=val=>toTypeString$1(val)==="[object Date]";const isRegExp=val=>toTypeString$1(val)==="[object RegExp]";const isEmptyObject=val=>isPlainObject$1(val)&&Object.keys(val).length===0;function warn(msg,err){if(typeof console!=="undefined"){console.warn(`[intlify] `+msg);if(err){console.warn(err.stack)}}}const assign=Object.assign;function escapeHtml(rawText){return rawText.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const hasOwnProperty$1=Object.prototype.hasOwnProperty;function hasOwn$1(obj,key){return hasOwnProperty$1.call(obj,key)}const isArray$1=Array.isArray;const isFunction$1=val=>typeof val==="function";const isString$1=val=>typeof val==="string";const isBoolean=val=>typeof val==="boolean";const isObject$2=val=>val!==null&&typeof val==="object";const objectToString$1=Object.prototype.toString;const toTypeString$1=value=>objectToString$1.call(value);const isPlainObject$1=val=>toTypeString$1(val)==="[object Object]"
/*!
  * @intlify/message-resolver v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */;const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(obj,key){return hasOwnProperty.call(obj,key)}const isObject$1=val=>val!==null&&typeof val==="object";const pathStateMachine=[];pathStateMachine[0]={["w"]:[0],["i"]:[3,0],["["]:[4],["o"]:[7]};pathStateMachine[1]={["w"]:[1],["."]:[2],["["]:[4],["o"]:[7]};pathStateMachine[2]={["w"]:[2],["i"]:[3,0],["0"]:[3,0]};pathStateMachine[3]={["i"]:[3,0],["0"]:[3,0],["w"]:[1,1],["."]:[2,1],["["]:[4,1],["o"]:[7,1]};pathStateMachine[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],["o"]:8,["l"]:[4,0]};pathStateMachine[5]={["'"]:[4,0],["o"]:8,["l"]:[5,0]};pathStateMachine[6]={['"']:[4,0],["o"]:8,["l"]:[6,0]};const literalValueRE=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function isLiteral(exp){return literalValueRE.test(exp)}function stripQuotes(str){const a2=str.charCodeAt(0);const b2=str.charCodeAt(str.length-1);return a2===b2&&(a2===34||a2===39)?str.slice(1,-1):str}function getPathCharType(ch){if(ch===void 0||ch===null){return"o"}const code=ch.charCodeAt(0);switch(code){case 91:case 93:case 46:case 34:case 39:return ch;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function formatSubPath(path){const trimmed=path.trim();if(path.charAt(0)==="0"&&isNaN(parseInt(path))){return false}return isLiteral(trimmed)?stripQuotes(trimmed):"*"+trimmed}function parse(path){const keys=[];let index2=-1;let mode=0;let subPathDepth=0;let c2;let key;let newChar;let type;let transition;let action;let typeMap;const actions=[];actions[0]=()=>{if(key===void 0){key=newChar}else{key+=newChar}};actions[1]=()=>{if(key!==void 0){keys.push(key);key=void 0}};actions[2]=()=>{actions[0]();subPathDepth++};actions[3]=()=>{if(subPathDepth>0){subPathDepth--;mode=4;actions[0]()}else{subPathDepth=0;if(key===void 0){return false}key=formatSubPath(key);if(key===false){return false}else{actions[1]()}}};function maybeUnescapeQuote(){const nextChar=path[index2+1];if(mode===5&&nextChar==="'"||mode===6&&nextChar==='"'){index2++;newChar="\\"+nextChar;actions[0]();return true}}while(mode!==null){index2++;c2=path[index2];if(c2==="\\"&&maybeUnescapeQuote()){continue}type=getPathCharType(c2);typeMap=pathStateMachine[mode];transition=typeMap[type]||typeMap["l"]||8;if(transition===8){return}mode=transition[0];if(transition[1]!==void 0){action=actions[transition[1]];if(action){newChar=c2;if(action()===false){return}}}if(mode===7){return keys}}}const cache=new Map;function resolveValue(obj,path){if(!isObject$1(obj)){return null}let hit=cache.get(path);if(!hit){hit=parse(path);if(hit){cache.set(path,hit)}}if(!hit){return null}const len=hit.length;let last=obj;let i2=0;while(i2<len){const val=last[hit[i2]];if(val===void 0){return null}last=val;i2++}return last}function handleFlatJson(obj){if(!isObject$1(obj)){return obj}for(const key in obj){if(!hasOwn(obj,key)){continue}if(!key.includes(".")){if(isObject$1(obj[key])){handleFlatJson(obj[key])}}else{const subKeys=key.split(".");const lastIndex=subKeys.length-1;let currentObj=obj;for(let i2=0;i2<lastIndex;i2++){if(!(subKeys[i2]in currentObj)){currentObj[subKeys[i2]]={}}currentObj=currentObj[subKeys[i2]]}currentObj[subKeys[lastIndex]]=obj[key];delete obj[key];if(isObject$1(currentObj[subKeys[lastIndex]])){handleFlatJson(currentObj[subKeys[lastIndex]])}}}return obj}
/*!
  * @intlify/shared v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */const isNumber=val=>typeof val==="number"&&isFinite(val);const isArray=Array.isArray;const isFunction=val=>typeof val==="function";const isString=val=>typeof val==="string";const isObject=val=>val!==null&&typeof val==="object";const objectToString=Object.prototype.toString;const toTypeString=value=>objectToString.call(value);const isPlainObject=val=>toTypeString(val)==="[object Object]";const toDisplayString=val=>val==null?"":isArray(val)||isPlainObject(val)&&val.toString===objectToString?JSON.stringify(val,null,2):String(val);
/*!
  * @intlify/runtime v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */const DEFAULT_MODIFIER=str=>str;const DEFAULT_MESSAGE=ctx=>"";const DEFAULT_MESSAGE_DATA_TYPE="text";const DEFAULT_NORMALIZE=values=>values.length===0?"":values.join("");const DEFAULT_INTERPOLATE=toDisplayString;function pluralDefault(choice,choicesLength){choice=Math.abs(choice);if(choicesLength===2){return choice?choice>1?1:0:1}return choice?Math.min(choice,2):0}function getPluralIndex(options){const index2=isNumber(options.pluralIndex)?options.pluralIndex:-1;return options.named&&(isNumber(options.named.count)||isNumber(options.named.n))?isNumber(options.named.count)?options.named.count:isNumber(options.named.n)?options.named.n:index2:index2}function normalizeNamed(pluralIndex,props){if(!props.count){props.count=pluralIndex}if(!props.n){props.n=pluralIndex}}function createMessageContext(options={}){const locale=options.locale;const pluralIndex=getPluralIndex(options);const pluralRule=isObject(options.pluralRules)&&isString(locale)&&isFunction(options.pluralRules[locale])?options.pluralRules[locale]:pluralDefault;const orgPluralRule=isObject(options.pluralRules)&&isString(locale)&&isFunction(options.pluralRules[locale])?pluralDefault:void 0;const plural=messages2=>messages2[pluralRule(pluralIndex,messages2.length,orgPluralRule)];const _list=options.list||[];const list=index2=>_list[index2];const _named=options.named||{};isNumber(options.pluralIndex)&&normalizeNamed(pluralIndex,_named);const named=key=>_named[key];function message(key){const msg=isFunction(options.messages)?options.messages(key):isObject(options.messages)?options.messages[key]:false;return!msg?options.parent?options.parent.message(key):DEFAULT_MESSAGE:msg}const _modifier=name=>options.modifiers?options.modifiers[name]:DEFAULT_MODIFIER;const normalize=isPlainObject(options.processor)&&isFunction(options.processor.normalize)?options.processor.normalize:DEFAULT_NORMALIZE;const interpolate=isPlainObject(options.processor)&&isFunction(options.processor.interpolate)?options.processor.interpolate:DEFAULT_INTERPOLATE;const type=isPlainObject(options.processor)&&isString(options.processor.type)?options.processor.type:DEFAULT_MESSAGE_DATA_TYPE;const ctx={["list"]:list,["named"]:named,["plural"]:plural,["linked"]:(key,modifier)=>{const msg=message(key)(ctx);return isString(modifier)?_modifier(modifier)(msg):msg},["message"]:message,["type"]:type,["interpolate"]:interpolate,["normalize"]:normalize};return ctx}
/*!
  * @intlify/message-compiler v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */function createCompileError(code,loc,options={}){const{domain:domain,messages:messages2,args:args}=options;const msg=code;const error=new SyntaxError(String(msg));error.code=code;error.domain=domain;return error}
/*!
  * @intlify/core-base v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */const VERSION$1="9.1.9";const NOT_REOSLVED=-1;const MISSING_RESOLVE_VALUE="";function getDefaultLinkedModifiers(){return{upper:val=>isString$1(val)?val.toUpperCase():val,lower:val=>isString$1(val)?val.toLowerCase():val,capitalize:val=>isString$1(val)?`${val.charAt(0).toLocaleUpperCase()}${val.substr(1)}`:val}}let _compiler;let _cid=0;function createCoreContext(options={}){const version2=isString$1(options.version)?options.version:VERSION$1;const locale=isString$1(options.locale)?options.locale:"en-US";const fallbackLocale=isArray$1(options.fallbackLocale)||isPlainObject$1(options.fallbackLocale)||isString$1(options.fallbackLocale)||options.fallbackLocale===false?options.fallbackLocale:locale;const messages2=isPlainObject$1(options.messages)?options.messages:{[locale]:{}};const datetimeFormats=isPlainObject$1(options.datetimeFormats)?options.datetimeFormats:{[locale]:{}};const numberFormats=isPlainObject$1(options.numberFormats)?options.numberFormats:{[locale]:{}};const modifiers=assign({},options.modifiers||{},getDefaultLinkedModifiers());const pluralRules=options.pluralRules||{};const missing=isFunction$1(options.missing)?options.missing:null;const missingWarn=isBoolean(options.missingWarn)||isRegExp(options.missingWarn)?options.missingWarn:true;const fallbackWarn=isBoolean(options.fallbackWarn)||isRegExp(options.fallbackWarn)?options.fallbackWarn:true;const fallbackFormat=!!options.fallbackFormat;const unresolving=!!options.unresolving;const postTranslation=isFunction$1(options.postTranslation)?options.postTranslation:null;const processor=isPlainObject$1(options.processor)?options.processor:null;const warnHtmlMessage=isBoolean(options.warnHtmlMessage)?options.warnHtmlMessage:true;const escapeParameter=!!options.escapeParameter;const messageCompiler=isFunction$1(options.messageCompiler)?options.messageCompiler:_compiler;const onWarn=isFunction$1(options.onWarn)?options.onWarn:warn;const internalOptions=options;const __datetimeFormatters=isObject$2(internalOptions.__datetimeFormatters)?internalOptions.__datetimeFormatters:new Map;const __numberFormatters=isObject$2(internalOptions.__numberFormatters)?internalOptions.__numberFormatters:new Map;const __meta=isObject$2(internalOptions.__meta)?internalOptions.__meta:{};_cid++;const context={version:version2,cid:_cid,locale:locale,fallbackLocale:fallbackLocale,messages:messages2,datetimeFormats:datetimeFormats,numberFormats:numberFormats,modifiers:modifiers,pluralRules:pluralRules,missing:missing,missingWarn:missingWarn,fallbackWarn:fallbackWarn,fallbackFormat:fallbackFormat,unresolving:unresolving,postTranslation:postTranslation,processor:processor,warnHtmlMessage:warnHtmlMessage,escapeParameter:escapeParameter,messageCompiler:messageCompiler,onWarn:onWarn,__datetimeFormatters:__datetimeFormatters,__numberFormatters:__numberFormatters,__meta:__meta};return context}function handleMissing(context,key,locale,missingWarn,type){const{missing:missing,onWarn:onWarn}=context;if(missing!==null){const ret=missing(context,locale,key,type);return isString$1(ret)?ret:key}else{return key}}function getLocaleChain(ctx,fallback,start){const context=ctx;if(!context.__localeChainCache){context.__localeChainCache=new Map}let chain=context.__localeChainCache.get(start);if(!chain){chain=[];let block=[start];while(isArray$1(block)){block=appendBlockToChain(chain,block,fallback)}const defaults=isArray$1(fallback)?fallback:isPlainObject$1(fallback)?fallback["default"]?fallback["default"]:null:fallback;block=isString$1(defaults)?[defaults]:defaults;if(isArray$1(block)){appendBlockToChain(chain,block,false)}context.__localeChainCache.set(start,chain)}return chain}function appendBlockToChain(chain,block,blocks){let follow=true;for(let i2=0;i2<block.length&&isBoolean(follow);i2++){const locale=block[i2];if(isString$1(locale)){follow=appendLocaleToChain(chain,block[i2],blocks)}}return follow}function appendLocaleToChain(chain,locale,blocks){let follow;const tokens=locale.split("-");do{const target=tokens.join("-");follow=appendItemToChain(chain,target,blocks);tokens.splice(-1,1)}while(tokens.length&&follow===true);return follow}function appendItemToChain(chain,target,blocks){let follow=false;if(!chain.includes(target)){follow=true;if(target){follow=target[target.length-1]!=="!";const locale=target.replace(/!/g,"");chain.push(locale);if((isArray$1(blocks)||isPlainObject$1(blocks))&&blocks[locale]){follow=blocks[locale]}}}return follow}function updateFallbackLocale(ctx,locale,fallback){const context=ctx;context.__localeChainCache=new Map;getLocaleChain(ctx,fallback,locale)}function createCoreError(code){return createCompileError(code,null,void 0)}const NOOP_MESSAGE_FUNCTION=()=>"";const isMessageFunction=val=>isFunction$1(val);function translate(context,...args){const{fallbackFormat:fallbackFormat,postTranslation:postTranslation,unresolving:unresolving,fallbackLocale:fallbackLocale,messages:messages2}=context;const[key,options]=parseTranslateArgs(...args);const missingWarn=isBoolean(options.missingWarn)?options.missingWarn:context.missingWarn;const fallbackWarn=isBoolean(options.fallbackWarn)?options.fallbackWarn:context.fallbackWarn;const escapeParameter=isBoolean(options.escapeParameter)?options.escapeParameter:context.escapeParameter;const resolvedMessage=!!options.resolvedMessage;const defaultMsgOrKey=isString$1(options.default)||isBoolean(options.default)?!isBoolean(options.default)?options.default:key:fallbackFormat?key:"";const enableDefaultMsg=fallbackFormat||defaultMsgOrKey!=="";const locale=isString$1(options.locale)?options.locale:context.locale;escapeParameter&&escapeParams(options);let[format2,targetLocale,message]=!resolvedMessage?resolveMessageFormat(context,key,locale,fallbackLocale,fallbackWarn,missingWarn):[key,locale,messages2[locale]||{}];let cacheBaseKey=key;if(!resolvedMessage&&!(isString$1(format2)||isMessageFunction(format2))){if(enableDefaultMsg){format2=defaultMsgOrKey;cacheBaseKey=format2}}if(!resolvedMessage&&(!(isString$1(format2)||isMessageFunction(format2))||!isString$1(targetLocale))){return unresolving?NOT_REOSLVED:key}let occurred=false;const errorDetector=()=>{occurred=true};const msg=!isMessageFunction(format2)?compileMessageFormat(context,key,targetLocale,format2,cacheBaseKey,errorDetector):format2;if(occurred){return format2}const ctxOptions=getMessageContextOptions(context,targetLocale,message,options);const msgContext=createMessageContext(ctxOptions);const messaged=evaluateMessage(context,msg,msgContext);const ret=postTranslation?postTranslation(messaged):messaged;return ret}function escapeParams(options){if(isArray$1(options.list)){options.list=options.list.map((item=>isString$1(item)?escapeHtml(item):item))}else if(isObject$2(options.named)){Object.keys(options.named).forEach((key=>{if(isString$1(options.named[key])){options.named[key]=escapeHtml(options.named[key])}}))}}function resolveMessageFormat(context,key,locale,fallbackLocale,fallbackWarn,missingWarn){const{messages:messages2,onWarn:onWarn}=context;const locales=getLocaleChain(context,fallbackLocale,locale);let message={};let targetLocale;let format2=null;const type="translate";for(let i2=0;i2<locales.length;i2++){targetLocale=locales[i2];message=messages2[targetLocale]||{};if((format2=resolveValue(message,key))===null){format2=message[key]}if(isString$1(format2)||isFunction$1(format2))break;const missingRet=handleMissing(context,key,targetLocale,missingWarn,type);if(missingRet!==key){format2=missingRet}}return[format2,targetLocale,message]}function compileMessageFormat(context,key,targetLocale,format2,cacheBaseKey,errorDetector){const{messageCompiler:messageCompiler,warnHtmlMessage:warnHtmlMessage}=context;if(isMessageFunction(format2)){const msg2=format2;msg2.locale=msg2.locale||targetLocale;msg2.key=msg2.key||key;return msg2}const msg=messageCompiler(format2,getCompileOptions(context,targetLocale,cacheBaseKey,format2,warnHtmlMessage,errorDetector));msg.locale=targetLocale;msg.key=key;msg.source=format2;return msg}function evaluateMessage(context,msg,msgCtx){const messaged=msg(msgCtx);return messaged}function parseTranslateArgs(...args){const[arg1,arg2,arg3]=args;const options={};if(!isString$1(arg1)&&!isNumber$1(arg1)&&!isMessageFunction(arg1)){throw createCoreError(14)}const key=isNumber$1(arg1)?String(arg1):isMessageFunction(arg1)?arg1:arg1;if(isNumber$1(arg2)){options.plural=arg2}else if(isString$1(arg2)){options.default=arg2}else if(isPlainObject$1(arg2)&&!isEmptyObject(arg2)){options.named=arg2}else if(isArray$1(arg2)){options.list=arg2}if(isNumber$1(arg3)){options.plural=arg3}else if(isString$1(arg3)){options.default=arg3}else if(isPlainObject$1(arg3)){assign(options,arg3)}return[key,options]}function getCompileOptions(context,locale,key,source,warnHtmlMessage,errorDetector){return{warnHtmlMessage:warnHtmlMessage,onError:err=>{errorDetector&&errorDetector(err);{throw err}},onCacheKey:source2=>generateFormatCacheKey(locale,key,source2)}}function getMessageContextOptions(context,locale,message,options){const{modifiers:modifiers,pluralRules:pluralRules}=context;const resolveMessage=key=>{const val=resolveValue(message,key);if(isString$1(val)){let occurred=false;const errorDetector=()=>{occurred=true};const msg=compileMessageFormat(context,key,locale,val,key,errorDetector);return!occurred?msg:NOOP_MESSAGE_FUNCTION}else if(isMessageFunction(val)){return val}else{return NOOP_MESSAGE_FUNCTION}};const ctxOptions={locale:locale,modifiers:modifiers,pluralRules:pluralRules,messages:resolveMessage};if(context.processor){ctxOptions.processor=context.processor}if(options.list){ctxOptions.list=options.list}if(options.named){ctxOptions.named=options.named}if(isNumber$1(options.plural)){ctxOptions.pluralIndex=options.plural}return ctxOptions}function datetime(context,...args){const{datetimeFormats:datetimeFormats,unresolving:unresolving,fallbackLocale:fallbackLocale,onWarn:onWarn}=context;const{__datetimeFormatters:__datetimeFormatters}=context;const[key,value,options,overrides]=parseDateTimeArgs(...args);const missingWarn=isBoolean(options.missingWarn)?options.missingWarn:context.missingWarn;isBoolean(options.fallbackWarn)?options.fallbackWarn:context.fallbackWarn;const part=!!options.part;const locale=isString$1(options.locale)?options.locale:context.locale;const locales=getLocaleChain(context,fallbackLocale,locale);if(!isString$1(key)||key===""){return new Intl.DateTimeFormat(locale).format(value)}let datetimeFormat={};let targetLocale;let format2=null;const type="datetime format";for(let i2=0;i2<locales.length;i2++){targetLocale=locales[i2];datetimeFormat=datetimeFormats[targetLocale]||{};format2=datetimeFormat[key];if(isPlainObject$1(format2))break;handleMissing(context,key,targetLocale,missingWarn,type)}if(!isPlainObject$1(format2)||!isString$1(targetLocale)){return unresolving?NOT_REOSLVED:key}let id2=`${targetLocale}__${key}`;if(!isEmptyObject(overrides)){id2=`${id2}__${JSON.stringify(overrides)}`}let formatter=__datetimeFormatters.get(id2);if(!formatter){formatter=new Intl.DateTimeFormat(targetLocale,assign({},format2,overrides));__datetimeFormatters.set(id2,formatter)}return!part?formatter.format(value):formatter.formatToParts(value)}function parseDateTimeArgs(...args){const[arg1,arg2,arg3,arg4]=args;let options={};let overrides={};let value;if(isString$1(arg1)){if(!/\d{4}-\d{2}-\d{2}(T.*)?/.test(arg1)){throw createCoreError(16)}value=new Date(arg1);try{value.toISOString()}catch(e2){throw createCoreError(16)}}else if(isDate(arg1)){if(isNaN(arg1.getTime())){throw createCoreError(15)}value=arg1}else if(isNumber$1(arg1)){value=arg1}else{throw createCoreError(14)}if(isString$1(arg2)){options.key=arg2}else if(isPlainObject$1(arg2)){options=arg2}if(isString$1(arg3)){options.locale=arg3}else if(isPlainObject$1(arg3)){overrides=arg3}if(isPlainObject$1(arg4)){overrides=arg4}return[options.key||"",value,options,overrides]}function clearDateTimeFormat(ctx,locale,format2){const context=ctx;for(const key in format2){const id2=`${locale}__${key}`;if(!context.__datetimeFormatters.has(id2)){continue}context.__datetimeFormatters.delete(id2)}}function number(context,...args){const{numberFormats:numberFormats,unresolving:unresolving,fallbackLocale:fallbackLocale,onWarn:onWarn}=context;const{__numberFormatters:__numberFormatters}=context;const[key,value,options,overrides]=parseNumberArgs(...args);const missingWarn=isBoolean(options.missingWarn)?options.missingWarn:context.missingWarn;isBoolean(options.fallbackWarn)?options.fallbackWarn:context.fallbackWarn;const part=!!options.part;const locale=isString$1(options.locale)?options.locale:context.locale;const locales=getLocaleChain(context,fallbackLocale,locale);if(!isString$1(key)||key===""){return new Intl.NumberFormat(locale).format(value)}let numberFormat={};let targetLocale;let format2=null;const type="number format";for(let i2=0;i2<locales.length;i2++){targetLocale=locales[i2];numberFormat=numberFormats[targetLocale]||{};format2=numberFormat[key];if(isPlainObject$1(format2))break;handleMissing(context,key,targetLocale,missingWarn,type)}if(!isPlainObject$1(format2)||!isString$1(targetLocale)){return unresolving?NOT_REOSLVED:key}let id2=`${targetLocale}__${key}`;if(!isEmptyObject(overrides)){id2=`${id2}__${JSON.stringify(overrides)}`}let formatter=__numberFormatters.get(id2);if(!formatter){formatter=new Intl.NumberFormat(targetLocale,assign({},format2,overrides));__numberFormatters.set(id2,formatter)}return!part?formatter.format(value):formatter.formatToParts(value)}function parseNumberArgs(...args){const[arg1,arg2,arg3,arg4]=args;let options={};let overrides={};if(!isNumber$1(arg1)){throw createCoreError(14)}const value=arg1;if(isString$1(arg2)){options.key=arg2}else if(isPlainObject$1(arg2)){options=arg2}if(isString$1(arg3)){options.locale=arg3}else if(isPlainObject$1(arg3)){overrides=arg3}if(isPlainObject$1(arg4)){overrides=arg4}return[options.key||"",value,options,overrides]}function clearNumberFormat(ctx,locale,format2){const context=ctx;for(const key in format2){const id2=`${locale}__${key}`;if(!context.__numberFormatters.has(id2)){continue}context.__numberFormatters.delete(id2)}}
/*!
  * vue-i18n v9.1.9
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const VERSION="9.1.9";function createI18nError(code,...args){return createCompileError(code,null,void 0)}const TransrateVNodeSymbol=makeSymbol("__transrateVNode");const DatetimePartsSymbol=makeSymbol("__datetimeParts");const NumberPartsSymbol=makeSymbol("__numberParts");const SetPluralRulesSymbol=makeSymbol("__setPluralRules");const InejctWithOption=makeSymbol("__injectWithOption");let composerID=0;function defineCoreMissingHandler(missing){return(ctx,locale,key,type)=>missing(locale,key,getCurrentInstance()||void 0,type)}function getLocaleMessages(locale,options){const{messages:messages2,__i18n:__i18n}=options;const ret=isPlainObject$1(messages2)?messages2:isArray$1(__i18n)?{}:{[locale]:{}};if(isArray$1(__i18n)){__i18n.forEach((({locale:locale2,resource:resource})=>{if(locale2){ret[locale2]=ret[locale2]||{};deepCopy(resource,ret[locale2])}else{deepCopy(resource,ret)}}))}if(options.flatJson){for(const key in ret){if(hasOwn$1(ret,key)){handleFlatJson(ret[key])}}}return ret}const isNotObjectOrIsArray=val=>!isObject$2(val)||isArray$1(val);function deepCopy(src,des){if(isNotObjectOrIsArray(src)||isNotObjectOrIsArray(des)){throw createI18nError(20)}for(const key in src){if(hasOwn$1(src,key)){if(isNotObjectOrIsArray(src[key])||isNotObjectOrIsArray(des[key])){des[key]=src[key]}else{deepCopy(src[key],des[key])}}}}function createComposer(options={}){const{__root:__root}=options;const _isGlobal=__root===void 0;let _inheritLocale=isBoolean(options.inheritLocale)?options.inheritLocale:true;const _locale=ref(__root&&_inheritLocale?__root.locale.value:isString$1(options.locale)?options.locale:"en-US");const _fallbackLocale=ref(__root&&_inheritLocale?__root.fallbackLocale.value:isString$1(options.fallbackLocale)||isArray$1(options.fallbackLocale)||isPlainObject$1(options.fallbackLocale)||options.fallbackLocale===false?options.fallbackLocale:_locale.value);const _messages=ref(getLocaleMessages(_locale.value,options));const _datetimeFormats=ref(isPlainObject$1(options.datetimeFormats)?options.datetimeFormats:{[_locale.value]:{}});const _numberFormats=ref(isPlainObject$1(options.numberFormats)?options.numberFormats:{[_locale.value]:{}});let _missingWarn=__root?__root.missingWarn:isBoolean(options.missingWarn)||isRegExp(options.missingWarn)?options.missingWarn:true;let _fallbackWarn=__root?__root.fallbackWarn:isBoolean(options.fallbackWarn)||isRegExp(options.fallbackWarn)?options.fallbackWarn:true;let _fallbackRoot=__root?__root.fallbackRoot:isBoolean(options.fallbackRoot)?options.fallbackRoot:true;let _fallbackFormat=!!options.fallbackFormat;let _missing=isFunction$1(options.missing)?options.missing:null;let _runtimeMissing=isFunction$1(options.missing)?defineCoreMissingHandler(options.missing):null;let _postTranslation=isFunction$1(options.postTranslation)?options.postTranslation:null;let _warnHtmlMessage=isBoolean(options.warnHtmlMessage)?options.warnHtmlMessage:true;let _escapeParameter=!!options.escapeParameter;const _modifiers=__root?__root.modifiers:isPlainObject$1(options.modifiers)?options.modifiers:{};let _pluralRules=options.pluralRules||__root&&__root.pluralRules;let _context;function getCoreContext(){return createCoreContext({version:VERSION,locale:_locale.value,fallbackLocale:_fallbackLocale.value,messages:_messages.value,messageCompiler:function compileToFunction(source){return ctx=>ctx.normalize([source])},datetimeFormats:_datetimeFormats.value,numberFormats:_numberFormats.value,modifiers:_modifiers,pluralRules:_pluralRules,missing:_runtimeMissing===null?void 0:_runtimeMissing,missingWarn:_missingWarn,fallbackWarn:_fallbackWarn,fallbackFormat:_fallbackFormat,unresolving:true,postTranslation:_postTranslation===null?void 0:_postTranslation,warnHtmlMessage:_warnHtmlMessage,escapeParameter:_escapeParameter,__datetimeFormatters:isPlainObject$1(_context)?_context.__datetimeFormatters:void 0,__numberFormatters:isPlainObject$1(_context)?_context.__numberFormatters:void 0,__v_emitter:isPlainObject$1(_context)?_context.__v_emitter:void 0,__meta:{framework:"vue"}})}_context=getCoreContext();updateFallbackLocale(_context,_locale.value,_fallbackLocale.value);function trackReactivityValues(){return[_locale.value,_fallbackLocale.value,_messages.value,_datetimeFormats.value,_numberFormats.value]}const locale=computed({get:()=>_locale.value,set:val=>{_locale.value=val;_context.locale=_locale.value}});const fallbackLocale=computed({get:()=>_fallbackLocale.value,set:val=>{_fallbackLocale.value=val;_context.fallbackLocale=_fallbackLocale.value;updateFallbackLocale(_context,_locale.value,val)}});const messages2=computed((()=>_messages.value));const datetimeFormats=computed((()=>_datetimeFormats.value));const numberFormats=computed((()=>_numberFormats.value));function getPostTranslationHandler(){return isFunction$1(_postTranslation)?_postTranslation:null}function setPostTranslationHandler(handler){_postTranslation=handler;_context.postTranslation=handler}function getMissingHandler(){return _missing}function setMissingHandler(handler){if(handler!==null){_runtimeMissing=defineCoreMissingHandler(handler)}_missing=handler;_context.missing=_runtimeMissing}function wrapWithDeps(fn,argumentParser,warnType,fallbackSuccess,fallbackFail,successCondition){trackReactivityValues();let ret;{ret=fn(_context)}if(isNumber$1(ret)&&ret===NOT_REOSLVED){const[key,arg2]=argumentParser();return __root&&_fallbackRoot?fallbackSuccess(__root):fallbackFail(key)}else if(successCondition(ret)){return ret}else{throw createI18nError(14)}}function t2(...args){return wrapWithDeps((context=>translate(context,...args)),(()=>parseTranslateArgs(...args)),"translate",(root=>root.t(...args)),(key=>key),(val=>isString$1(val)))}function rt(...args){const[arg1,arg2,arg3]=args;if(arg3&&!isObject$2(arg3)){throw createI18nError(15)}return t2(...[arg1,arg2,assign({resolvedMessage:true},arg3||{})])}function d2(...args){return wrapWithDeps((context=>datetime(context,...args)),(()=>parseDateTimeArgs(...args)),"datetime format",(root=>root.d(...args)),(()=>MISSING_RESOLVE_VALUE),(val=>isString$1(val)))}function n2(...args){return wrapWithDeps((context=>number(context,...args)),(()=>parseNumberArgs(...args)),"number format",(root=>root.n(...args)),(()=>MISSING_RESOLVE_VALUE),(val=>isString$1(val)))}function normalize(values){return values.map((val=>isString$1(val)?createVNode():val))}const interpolate=val=>val;const processor={normalize:normalize,interpolate:interpolate,type:"vnode"};function transrateVNode(...args){return wrapWithDeps((context=>{let ret;const _context2=context;try{_context2.processor=processor;ret=translate(_context2,...args)}finally{_context2.processor=null}return ret}),(()=>parseTranslateArgs(...args)),"translate",(root=>root[TransrateVNodeSymbol](...args)),(key=>[createVNode()]),(val=>isArray$1(val)))}function numberParts(...args){return wrapWithDeps((context=>number(context,...args)),(()=>parseNumberArgs(...args)),"number format",(root=>root[NumberPartsSymbol](...args)),(()=>[]),(val=>isString$1(val)||isArray$1(val)))}function datetimeParts(...args){return wrapWithDeps((context=>datetime(context,...args)),(()=>parseDateTimeArgs(...args)),"datetime format",(root=>root[DatetimePartsSymbol](...args)),(()=>[]),(val=>isString$1(val)||isArray$1(val)))}function setPluralRules(rules){_pluralRules=rules;_context.pluralRules=_pluralRules}function te2(key,locale2){const targetLocale=isString$1(locale2)?locale2:_locale.value;const message=getLocaleMessage(targetLocale);return resolveValue(message,key)!==null}function resolveMessages(key){let messages22=null;const locales=getLocaleChain(_context,_fallbackLocale.value,_locale.value);for(let i2=0;i2<locales.length;i2++){const targetLocaleMessages=_messages.value[locales[i2]]||{};const messageValue=resolveValue(targetLocaleMessages,key);if(messageValue!=null){messages22=messageValue;break}}return messages22}function tm(key){const messages22=resolveMessages(key);return messages22!=null?messages22:__root?__root.tm(key)||{}:{}}function getLocaleMessage(locale2){return _messages.value[locale2]||{}}function setLocaleMessage(locale2,message){_messages.value[locale2]=message;_context.messages=_messages.value}function mergeLocaleMessage(locale2,message){_messages.value[locale2]=_messages.value[locale2]||{};deepCopy(message,_messages.value[locale2]);_context.messages=_messages.value}function getDateTimeFormat(locale2){return _datetimeFormats.value[locale2]||{}}function setDateTimeFormat(locale2,format2){_datetimeFormats.value[locale2]=format2;_context.datetimeFormats=_datetimeFormats.value;clearDateTimeFormat(_context,locale2,format2)}function mergeDateTimeFormat(locale2,format2){_datetimeFormats.value[locale2]=assign(_datetimeFormats.value[locale2]||{},format2);_context.datetimeFormats=_datetimeFormats.value;clearDateTimeFormat(_context,locale2,format2)}function getNumberFormat(locale2){return _numberFormats.value[locale2]||{}}function setNumberFormat(locale2,format2){_numberFormats.value[locale2]=format2;_context.numberFormats=_numberFormats.value;clearNumberFormat(_context,locale2,format2)}function mergeNumberFormat(locale2,format2){_numberFormats.value[locale2]=assign(_numberFormats.value[locale2]||{},format2);_context.numberFormats=_numberFormats.value;clearNumberFormat(_context,locale2,format2)}composerID++;if(__root){watch(__root.locale,(val=>{if(_inheritLocale){_locale.value=val;_context.locale=val;updateFallbackLocale(_context,_locale.value,_fallbackLocale.value)}}));watch(__root.fallbackLocale,(val=>{if(_inheritLocale){_fallbackLocale.value=val;_context.fallbackLocale=val;updateFallbackLocale(_context,_locale.value,_fallbackLocale.value)}}))}const composer={id:composerID,locale:locale,fallbackLocale:fallbackLocale,get inheritLocale(){return _inheritLocale},set inheritLocale(val){_inheritLocale=val;if(val&&__root){_locale.value=__root.locale.value;_fallbackLocale.value=__root.fallbackLocale.value;updateFallbackLocale(_context,_locale.value,_fallbackLocale.value)}},get availableLocales(){return Object.keys(_messages.value).sort()},messages:messages2,datetimeFormats:datetimeFormats,numberFormats:numberFormats,get modifiers(){return _modifiers},get pluralRules(){return _pluralRules||{}},get isGlobal(){return _isGlobal},get missingWarn(){return _missingWarn},set missingWarn(val){_missingWarn=val;_context.missingWarn=_missingWarn},get fallbackWarn(){return _fallbackWarn},set fallbackWarn(val){_fallbackWarn=val;_context.fallbackWarn=_fallbackWarn},get fallbackRoot(){return _fallbackRoot},set fallbackRoot(val){_fallbackRoot=val},get fallbackFormat(){return _fallbackFormat},set fallbackFormat(val){_fallbackFormat=val;_context.fallbackFormat=_fallbackFormat},get warnHtmlMessage(){return _warnHtmlMessage},set warnHtmlMessage(val){_warnHtmlMessage=val;_context.warnHtmlMessage=val},get escapeParameter(){return _escapeParameter},set escapeParameter(val){_escapeParameter=val;_context.escapeParameter=val},t:t2,rt:rt,d:d2,n:n2,te:te2,tm:tm,getLocaleMessage:getLocaleMessage,setLocaleMessage:setLocaleMessage,mergeLocaleMessage:mergeLocaleMessage,getDateTimeFormat:getDateTimeFormat,setDateTimeFormat:setDateTimeFormat,mergeDateTimeFormat:mergeDateTimeFormat,getNumberFormat:getNumberFormat,setNumberFormat:setNumberFormat,mergeNumberFormat:mergeNumberFormat,getPostTranslationHandler:getPostTranslationHandler,setPostTranslationHandler:setPostTranslationHandler,getMissingHandler:getMissingHandler,setMissingHandler:setMissingHandler,[TransrateVNodeSymbol]:transrateVNode,[NumberPartsSymbol]:numberParts,[DatetimePartsSymbol]:datetimeParts,[SetPluralRulesSymbol]:setPluralRules,[InejctWithOption]:options.__injectWithOption};return composer}function convertComposerOptions(options){const locale=isString$1(options.locale)?options.locale:"en-US";const fallbackLocale=isString$1(options.fallbackLocale)||isArray$1(options.fallbackLocale)||isPlainObject$1(options.fallbackLocale)||options.fallbackLocale===false?options.fallbackLocale:locale;const missing=isFunction$1(options.missing)?options.missing:void 0;const missingWarn=isBoolean(options.silentTranslationWarn)||isRegExp(options.silentTranslationWarn)?!options.silentTranslationWarn:true;const fallbackWarn=isBoolean(options.silentFallbackWarn)||isRegExp(options.silentFallbackWarn)?!options.silentFallbackWarn:true;const fallbackRoot=isBoolean(options.fallbackRoot)?options.fallbackRoot:true;const fallbackFormat=!!options.formatFallbackMessages;const modifiers=isPlainObject$1(options.modifiers)?options.modifiers:{};const pluralizationRules=options.pluralizationRules;const postTranslation=isFunction$1(options.postTranslation)?options.postTranslation:void 0;const warnHtmlMessage=isString$1(options.warnHtmlInMessage)?options.warnHtmlInMessage!=="off":true;const escapeParameter=!!options.escapeParameterHtml;const inheritLocale=isBoolean(options.sync)?options.sync:true;let messages2=options.messages;if(isPlainObject$1(options.sharedMessages)){const sharedMessages=options.sharedMessages;const locales=Object.keys(sharedMessages);messages2=locales.reduce(((messages22,locale2)=>{const message=messages22[locale2]||(messages22[locale2]={});assign(message,sharedMessages[locale2]);return messages22}),messages2||{})}const{__i18n:__i18n,__root:__root,__injectWithOption:__injectWithOption}=options;const datetimeFormats=options.datetimeFormats;const numberFormats=options.numberFormats;const flatJson=options.flatJson;return{locale:locale,fallbackLocale:fallbackLocale,messages:messages2,flatJson:flatJson,datetimeFormats:datetimeFormats,numberFormats:numberFormats,missing:missing,missingWarn:missingWarn,fallbackWarn:fallbackWarn,fallbackRoot:fallbackRoot,fallbackFormat:fallbackFormat,modifiers:modifiers,pluralRules:pluralizationRules,postTranslation:postTranslation,warnHtmlMessage:warnHtmlMessage,escapeParameter:escapeParameter,inheritLocale:inheritLocale,__i18n:__i18n,__root:__root,__injectWithOption:__injectWithOption}}function createVueI18n(options={}){const composer=createComposer(convertComposerOptions(options));const vueI18n={id:composer.id,get locale(){return composer.locale.value},set locale(val){composer.locale.value=val},get fallbackLocale(){return composer.fallbackLocale.value},set fallbackLocale(val){composer.fallbackLocale.value=val},get messages(){return composer.messages.value},get datetimeFormats(){return composer.datetimeFormats.value},get numberFormats(){return composer.numberFormats.value},get availableLocales(){return composer.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(val){},get missing(){return composer.getMissingHandler()},set missing(handler){composer.setMissingHandler(handler)},get silentTranslationWarn(){return isBoolean(composer.missingWarn)?!composer.missingWarn:composer.missingWarn},set silentTranslationWarn(val){composer.missingWarn=isBoolean(val)?!val:val},get silentFallbackWarn(){return isBoolean(composer.fallbackWarn)?!composer.fallbackWarn:composer.fallbackWarn},set silentFallbackWarn(val){composer.fallbackWarn=isBoolean(val)?!val:val},get modifiers(){return composer.modifiers},get formatFallbackMessages(){return composer.fallbackFormat},set formatFallbackMessages(val){composer.fallbackFormat=val},get postTranslation(){return composer.getPostTranslationHandler()},set postTranslation(handler){composer.setPostTranslationHandler(handler)},get sync(){return composer.inheritLocale},set sync(val){composer.inheritLocale=val},get warnHtmlInMessage(){return composer.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(val){composer.warnHtmlMessage=val!=="off"},get escapeParameterHtml(){return composer.escapeParameter},set escapeParameterHtml(val){composer.escapeParameter=val},get preserveDirectiveContent(){return true},set preserveDirectiveContent(val){},get pluralizationRules(){return composer.pluralRules||{}},__composer:composer,t(...args){const[arg1,arg2,arg3]=args;const options2={};let list=null;let named=null;if(!isString$1(arg1)){throw createI18nError(15)}const key=arg1;if(isString$1(arg2)){options2.locale=arg2}else if(isArray$1(arg2)){list=arg2}else if(isPlainObject$1(arg2)){named=arg2}if(isArray$1(arg3)){list=arg3}else if(isPlainObject$1(arg3)){named=arg3}return composer.t(key,list||named||{},options2)},rt(...args){return composer.rt(...args)},tc(...args){const[arg1,arg2,arg3]=args;const options2={plural:1};let list=null;let named=null;if(!isString$1(arg1)){throw createI18nError(15)}const key=arg1;if(isString$1(arg2)){options2.locale=arg2}else if(isNumber$1(arg2)){options2.plural=arg2}else if(isArray$1(arg2)){list=arg2}else if(isPlainObject$1(arg2)){named=arg2}if(isString$1(arg3)){options2.locale=arg3}else if(isArray$1(arg3)){list=arg3}else if(isPlainObject$1(arg3)){named=arg3}return composer.t(key,list||named||{},options2)},te(key,locale){return composer.te(key,locale)},tm(key){return composer.tm(key)},getLocaleMessage(locale){return composer.getLocaleMessage(locale)},setLocaleMessage(locale,message){composer.setLocaleMessage(locale,message)},mergeLocaleMessage(locale,message){composer.mergeLocaleMessage(locale,message)},d(...args){return composer.d(...args)},getDateTimeFormat(locale){return composer.getDateTimeFormat(locale)},setDateTimeFormat(locale,format2){composer.setDateTimeFormat(locale,format2)},mergeDateTimeFormat(locale,format2){composer.mergeDateTimeFormat(locale,format2)},n(...args){return composer.n(...args)},getNumberFormat(locale){return composer.getNumberFormat(locale)},setNumberFormat(locale,format2){composer.setNumberFormat(locale,format2)},mergeNumberFormat(locale,format2){composer.mergeNumberFormat(locale,format2)},getChoiceIndex(choice,choicesLength){return-1},__onComponentInstanceCreated(target){const{componentInstanceCreatedListener:componentInstanceCreatedListener}=options;if(componentInstanceCreatedListener){componentInstanceCreatedListener(target,vueI18n)}}};return vueI18n}const baseFormatProps={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:val=>val==="parent"||val==="global",default:"parent"},i18n:{type:Object}};const Translation={name:"i18n-t",props:assign({keypath:{type:String,required:true},plural:{type:[Number,String],validator:val=>isNumber$1(val)||!isNaN(val)}},baseFormatProps),setup(props,context){const{slots:slots,attrs:attrs}=context;const i18n=props.i18n||useI18n({useScope:props.scope,__useComponent:true});const keys=Object.keys(slots).filter((key=>key!=="_"));return()=>{const options={};if(props.locale){options.locale=props.locale}if(props.plural!==void 0){options.plural=isString$1(props.plural)?+props.plural:props.plural}const arg=getInterpolateArg(context,keys);i18n[TransrateVNodeSymbol](props.keypath,arg,options);assign({},attrs);return isString$1(props.tag)?h$1(props.tag):isObject$2(props.tag)?h$1(props.tag):h$1(Fragment)}}};function getInterpolateArg({slots:slots},keys){if(keys.length===1&&keys[0]==="default"){return slots.default?slots.default():[]}else{return keys.reduce(((arg,key)=>{const slot=slots[key];if(slot){arg[key]=slot()}return arg}),{})}}function renderFormatter(props,context,slotKeys,partFormatter){const{slots:slots,attrs:attrs}=context;return()=>{const options={part:true};let overrides={};if(props.locale){options.locale=props.locale}if(isString$1(props.format)){options.key=props.format}else if(isObject$2(props.format)){if(isString$1(props.format.key)){options.key=props.format.key}overrides=Object.keys(props.format).reduce(((options2,prop)=>slotKeys.includes(prop)?assign({},options2,{[prop]:props.format[prop]}):options2),{})}const parts=partFormatter(...[props.value,options,overrides]);[options.key];if(isArray$1(parts)){parts.map(((part,index2)=>{const slot=slots[part.type];return slot?slot({[part.type]:part.value,index:index2,parts:parts}):[part.value]}))}assign({},attrs);return isString$1(props.tag)?h$1(props.tag):isObject$2(props.tag)?h$1(props.tag):h$1(Fragment)}}const NUMBER_FORMAT_KEYS=["localeMatcher","style","unit","unitDisplay","currency","currencyDisplay","useGrouping","numberingSystem","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","notation","formatMatcher"];const NumberFormat={name:"i18n-n",props:assign({value:{type:Number,required:true},format:{type:[String,Object]}},baseFormatProps),setup(props,context){const i18n=props.i18n||useI18n({useScope:"parent",__useComponent:true});return renderFormatter(props,context,NUMBER_FORMAT_KEYS,((...args)=>i18n[NumberPartsSymbol](...args)))}};const DATETIME_FORMAT_KEYS=["dateStyle","timeStyle","fractionalSecondDigits","calendar","dayPeriod","numberingSystem","localeMatcher","timeZone","hour12","hourCycle","formatMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName"];const DatetimeFormat={name:"i18n-d",props:assign({value:{type:[Number,Date],required:true},format:{type:[String,Object]}},baseFormatProps),setup(props,context){const i18n=props.i18n||useI18n({useScope:"parent",__useComponent:true});return renderFormatter(props,context,DATETIME_FORMAT_KEYS,((...args)=>i18n[DatetimePartsSymbol](...args)))}};function getComposer$2(i18n,instance){const i18nInternal=i18n;if(i18n.mode==="composition"){return i18nInternal.__getInstance(instance)||i18n.global}else{const vueI18n=i18nInternal.__getInstance(instance);return vueI18n!=null?vueI18n.__composer:i18n.global.__composer}}function vTDirective(i18n){const bind=(el,{instance:instance,value:value,modifiers:modifiers})=>{if(!instance||!instance.$){throw createI18nError(22)}const composer=getComposer$2(i18n,instance.$);const parsedValue=parseValue(value);el.textContent=composer.t(...makeParams(parsedValue))};return{beforeMount:bind,beforeUpdate:bind}}function parseValue(value){if(isString$1(value)){return{path:value}}else if(isPlainObject$1(value)){if(!("path"in value)){throw createI18nError(19,"path")}return value}else{throw createI18nError(20)}}function makeParams(value){const{path:path,locale:locale,args:args,choice:choice,plural:plural}=value;const options={};const named=args||{};if(isString$1(locale)){options.locale=locale}if(isNumber$1(choice)){options.plural=choice}if(isNumber$1(plural)){options.plural=plural}return[path,named,options]}function apply(app,i18n,...options){const pluginOptions=isPlainObject$1(options[0])?options[0]:{};const useI18nComponentName=!!pluginOptions.useI18nComponentName;const globalInstall=isBoolean(pluginOptions.globalInstall)?pluginOptions.globalInstall:true;if(globalInstall){app.component(!useI18nComponentName?Translation.name:"i18n",Translation);app.component(NumberFormat.name,NumberFormat);app.component(DatetimeFormat.name,DatetimeFormat)}app.directive("t",vTDirective(i18n))}function defineMixin(vuei18n,composer,i18n){return{beforeCreate(){const instance=getCurrentInstance();if(!instance){throw createI18nError(22)}const options=this.$options;if(options.i18n){const optionsI18n=options.i18n;if(options.__i18n){optionsI18n.__i18n=options.__i18n}optionsI18n.__root=composer;if(this===this.$root){this.$i18n=mergeToRoot(vuei18n,optionsI18n)}else{optionsI18n.__injectWithOption=true;this.$i18n=createVueI18n(optionsI18n)}}else if(options.__i18n){if(this===this.$root){this.$i18n=mergeToRoot(vuei18n,options)}else{this.$i18n=createVueI18n({__i18n:options.__i18n,__injectWithOption:true,__root:composer})}}else{this.$i18n=vuei18n}vuei18n.__onComponentInstanceCreated(this.$i18n);i18n.__setInstance(instance,this.$i18n);this.$t=(...args)=>this.$i18n.t(...args);this.$rt=(...args)=>this.$i18n.rt(...args);this.$tc=(...args)=>this.$i18n.tc(...args);this.$te=(key,locale)=>this.$i18n.te(key,locale);this.$d=(...args)=>this.$i18n.d(...args);this.$n=(...args)=>this.$i18n.n(...args);this.$tm=key=>this.$i18n.tm(key)},mounted(){},beforeUnmount(){const instance=getCurrentInstance();if(!instance){throw createI18nError(22)}delete this.$t;delete this.$rt;delete this.$tc;delete this.$te;delete this.$d;delete this.$n;delete this.$tm;i18n.__deleteInstance(instance);delete this.$i18n}}}function mergeToRoot(root,options){root.locale=options.locale||root.locale;root.fallbackLocale=options.fallbackLocale||root.fallbackLocale;root.missing=options.missing||root.missing;root.silentTranslationWarn=options.silentTranslationWarn||root.silentFallbackWarn;root.silentFallbackWarn=options.silentFallbackWarn||root.silentFallbackWarn;root.formatFallbackMessages=options.formatFallbackMessages||root.formatFallbackMessages;root.postTranslation=options.postTranslation||root.postTranslation;root.warnHtmlInMessage=options.warnHtmlInMessage||root.warnHtmlInMessage;root.escapeParameterHtml=options.escapeParameterHtml||root.escapeParameterHtml;root.sync=options.sync||root.sync;root.__composer[SetPluralRulesSymbol](options.pluralizationRules||root.pluralizationRules);const messages2=getLocaleMessages(root.locale,{messages:options.messages,__i18n:options.__i18n});Object.keys(messages2).forEach((locale=>root.mergeLocaleMessage(locale,messages2[locale])));if(options.datetimeFormats){Object.keys(options.datetimeFormats).forEach((locale=>root.mergeDateTimeFormat(locale,options.datetimeFormats[locale])))}if(options.numberFormats){Object.keys(options.numberFormats).forEach((locale=>root.mergeNumberFormat(locale,options.numberFormats[locale])))}return root}function createI18n(options={}){const __legacyMode=isBoolean(options.legacy)?options.legacy:true;const __globalInjection=!!options.globalInjection;const __instances=new Map;const __global=__legacyMode?createVueI18n(options):createComposer(options);const symbol=makeSymbol("");const i18n={get mode(){return __legacyMode?"legacy":"composition"},async install(app,...options2){app.__VUE_I18N_SYMBOL__=symbol;app.provide(app.__VUE_I18N_SYMBOL__,i18n);if(!__legacyMode&&__globalInjection){injectGlobalFields(app,i18n.global)}{apply(app,i18n,...options2)}if(__legacyMode){app.mixin(defineMixin(__global,__global.__composer,i18n))}},get global(){return __global},__instances:__instances,__getInstance(component){return __instances.get(component)||null},__setInstance(component,instance){__instances.set(component,instance)},__deleteInstance(component){__instances.delete(component)}};return i18n}function useI18n(options={}){const instance=getCurrentInstance();if(instance==null){throw createI18nError(16)}if(!instance.appContext.app.__VUE_I18N_SYMBOL__){throw createI18nError(17)}const i18n=inject(instance.appContext.app.__VUE_I18N_SYMBOL__);if(!i18n){throw createI18nError(22)}const global2=i18n.mode==="composition"?i18n.global:i18n.global.__composer;const scope=isEmptyObject(options)?"__i18n"in instance.type?"local":"global":!options.useScope?"local":options.useScope;if(scope==="global"){let messages2=isObject$2(options.messages)?options.messages:{};if("__i18nGlobal"in instance.type){messages2=getLocaleMessages(global2.locale.value,{messages:messages2,__i18n:instance.type.__i18nGlobal})}const locales=Object.keys(messages2);if(locales.length){locales.forEach((locale=>{global2.mergeLocaleMessage(locale,messages2[locale])}))}if(isObject$2(options.datetimeFormats)){const locales2=Object.keys(options.datetimeFormats);if(locales2.length){locales2.forEach((locale=>{global2.mergeDateTimeFormat(locale,options.datetimeFormats[locale])}))}}if(isObject$2(options.numberFormats)){const locales2=Object.keys(options.numberFormats);if(locales2.length){locales2.forEach((locale=>{global2.mergeNumberFormat(locale,options.numberFormats[locale])}))}}return global2}if(scope==="parent"){let composer2=getComposer(i18n,instance,options.__useComponent);if(composer2==null){composer2=global2}return composer2}if(i18n.mode==="legacy"){throw createI18nError(18)}const i18nInternal=i18n;let composer=i18nInternal.__getInstance(instance);if(composer==null){const type=instance.type;const composerOptions=assign({},options);if(type.__i18n){composerOptions.__i18n=type.__i18n}if(global2){composerOptions.__root=global2}composer=createComposer(composerOptions);setupLifeCycle(i18nInternal,instance);i18nInternal.__setInstance(instance,composer)}return composer}function getComposer(i18n,target,useComponent=false){let composer=null;const root=target.root;let current=target.parent;while(current!=null){const i18nInternal=i18n;if(i18n.mode==="composition"){composer=i18nInternal.__getInstance(current)}else{const vueI18n=i18nInternal.__getInstance(current);if(vueI18n!=null){composer=vueI18n.__composer}if(useComponent&&composer&&!composer[InejctWithOption]){composer=null}}if(composer!=null){break}if(root===current){break}current=current.parent}return composer}function setupLifeCycle(i18n,target,composer){onMounted((()=>{}),target);onUnmounted((()=>{i18n.__deleteInstance(target)}),target)}const globalExportProps=["locale","fallbackLocale","availableLocales"];const globalExportMethods=["t","rt","d","n","tm"];function injectGlobalFields(app,composer){const i18n=Object.create(null);globalExportProps.forEach((prop=>{const desc=Object.getOwnPropertyDescriptor(composer,prop);if(!desc){throw createI18nError(22)}const wrap=isRef(desc.value)?{get(){return desc.value.value},set(val){desc.value.value=val}}:{get(){return desc.get&&desc.get()}};Object.defineProperty(i18n,prop,wrap)}));app.config.globalProperties.$i18n=i18n;globalExportMethods.forEach((method=>{const desc=Object.getOwnPropertyDescriptor(composer,method);if(!desc||!desc.value){throw createI18nError(22)}Object.defineProperty(app.config.globalProperties,`$${method}`,desc)}))}const en={"uni-load-more.contentdown":"Pull up to show more","uni-load-more.contentrefresh":"loading...","uni-load-more.contentnomore":"No more data"};const zhHans={"uni-load-more.contentdown":"上拉显示更多","uni-load-more.contentrefresh":"正在加载...","uni-load-more.contentnomore":"没有更多数据了"};const zhHant={"uni-load-more.contentdown":"上拉顯示更多","uni-load-more.contentrefresh":"正在加載...","uni-load-more.contentnomore":"沒有更多數據了"};const messages={en:en,"zh-Hans":zhHans,"zh-Hant":zhHant};class MPAnimation{constructor(options,_this){this.options=options;this.animation=index.createAnimation({...options});this.currentStepAnimates={};this.next=0;this.$=_this}_nvuePushAnimates(type,args){let aniObj=this.currentStepAnimates[this.next];let styles={};if(!aniObj){styles={styles:{},config:{}}}else{styles=aniObj}if(animateTypes1.includes(type)){if(!styles.styles.transform){styles.styles.transform=""}let unit="";if(type==="rotate"){unit="deg"}styles.styles.transform+=`${type}(${args+unit}) `}else{styles.styles[type]=`${args}`}this.currentStepAnimates[this.next]=styles}_animateRun(styles={},config={}){let ref2=this.$.$refs["ani"].ref;if(!ref2)return;return new Promise(((resolve2,reject)=>{nvueAnimation.transition(ref2,{styles:styles,...config},(res=>{resolve2()}))}))}_nvueNextAnimate(animates,step=0,fn){let obj=animates[step];if(obj){let{styles:styles,config:config}=obj;this._animateRun(styles,config).then((()=>{step+=1;this._nvueNextAnimate(animates,step,fn)}))}else{this.currentStepAnimates={};typeof fn==="function"&&fn();this.isEnd=true}}step(config={}){this.animation.step(config);return this}run(fn){this.$.animationData=this.animation.export();this.$.timer=setTimeout((()=>{typeof fn==="function"&&fn()}),this.$.durationTime)}}const animateTypes1=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];const animateTypes2=["opacity","backgroundColor"];const animateTypes3=["width","height","left","right","top","bottom"];animateTypes1.concat(animateTypes2,animateTypes3).forEach((type=>{MPAnimation.prototype[type]=function(...args){this.animation[type](...args);return this}}));function createAnimation(option,_this){if(!_this)return;clearTimeout(_this.timer);return new MPAnimation(option,_this)}exports.CryptoJS=CryptoJS;exports.Pinia=Pinia;exports.Xe=Xe;exports._export_sfc=_export_sfc;exports.createAnimation=createAnimation;exports.createI18n=createI18n;exports.createPinia=createPinia;exports.createSSRApp=createSSRApp;exports.defineStore=defineStore;exports.e=e$1;exports.exported=exported;exports.f=f$1;exports.index=index;exports.initVueI18n=initVueI18n;exports.mapState=mapState;exports.messages=messages;exports.n=n$1;exports.o=o$1;exports.p=p$1;exports.r=r;exports.resolveComponent=resolveComponent;exports.s=s$1;exports.t=t$1;
