<template>
  <uni-popup ref="popup" border-radius="10px" background-color="#fff">
    <view class="popup-container" :class="[careClass]">
      <view class="popup-title">
        {{ $t('servicHallTitle') }}
      </view>
      <view class="scope-list">
        <view v-for="service in scopeList" :key="service.serviceCode" class="scope-item" :class="{'scope-item-both' :  service.both==1}">
          {{ service.serviceName }}
        </view>
      </view>
      <view class="close" @click="closePopup"></view>
    </view>
  </uni-popup>
</template>
<script>
import { useOptionsStore } from '@/store/options'
import { mapState } from 'pinia'
export default {
  data() {
    return {
    }
  },
  props: {
    serviceInfo: {
      type: Object,
      default() { return null }
    }
  },
  computed: {
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      return this.verType=='care' ? 'care-con' : ''
    },
    scopeList:function(){
      if(!this.serviceInfo){
        return []
      }
      let list = [],list1 = []
      this.serviceInfo.forEach(element => {
        if(element.serviceName && element.serviceName.length>4) {
          element.both = 1
          list.push(element)
        }else{
          list1.push(element)
        }
      });
      return list.concat(list1)
    }
  },
  methods:{
    showServiceModel(){
      this.$refs.popup.open('center')
    },
    closePopup(){
      this.$refs.popup.close()
    }
  }
}
</script>
<style lang="scss" scoped>
.popup-container{
  padding:37px 50px 26px;
  box-sizing: border-box;
  width:624px;
  .popup-title{
    font-size: 32px;
    color: #000000;
    font-weight: bold;
    margin-bottom: 28px;
    text-align: center;
  }
  .scope-list{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .scope-item{
      width: 240px;
      height: 64px;
      border-radius: 12px;
      border: 2px solid rgba(0,0,0,0.1);
      font-size: 28px;
      color: rgba(0,0,0,0.6);
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.scope-item-both{
        width:524px;
      }
    }
  }
  .close{
    width:40px;
    height:40px;
    background: url("@/static/home/<USER>") center no-repeat;
    position: absolute;
    right:30px;
    top:37px;
    background-size: contain;
  }
}
.care-con{
  .popup-title{
    font-size:40px!important;
  }
  .scope-item{
    font-size: 36px!important;
  }
  .close{
    width:54px!important;
    height:54px!important;
    top:30px!important;
    position: absolute;
  }
}
</style>
