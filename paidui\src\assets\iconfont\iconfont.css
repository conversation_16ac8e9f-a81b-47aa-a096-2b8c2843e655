@font-face {
  font-family: "iconfont"; /* Project id 4678866 */
  src: url('iconfont.woff2?t=1729480820449') format('woff2'),
       url('iconfont.woff?t=1729480820449') format('woff'),
       url('iconfont.ttf?t=1729480820449') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-daohang:before {
  content: "\e602";
}

.icon-refresh:before {
  content: "\ec08";
}

.icon-cc-arrow-down-circle:before {
  content: "\e607";
}

.icon-arrow1:before {
  content: "\e601";
}

.icon-arrow:before {
  content: "\e70a";
}

.icon-to_address:before {
  content: "\e6ad";
}

.icon-addresss:before {
  content: "\e619";
}

.icon-lingdang:before {
  content: "\e600";
}

.icon-search:before {
  content: "\e630";
}

.icon-send:before {
  content: "\e639";
}

.icon-tips:before {
  content: "\e622";
}

.icon-to_address1:before {
  content: "\e7e2";
}

