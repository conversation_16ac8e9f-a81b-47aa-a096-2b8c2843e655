/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.popup-container.data-v-324e86e6 {
  padding: 0px 30rpx 30rpx;
  box-sizing: border-box;
  width: 624rpx;
}
.popup-container .popup-title.data-v-324e86e6 {
  font-size: 32rpx;
  color: #000000;
  font-weight: bold;
  margin-bottom: 28rpx;
  text-align: center;
  height: 116rpx;
  line-height: 116rpx;
  border-bottom: 1rpx solid #E5E5E5;
}
.popup-container .popup-content.data-v-324e86e6 {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  text-align: center;
  margin-bottom: 45rpx;
  padding: 0 10rpx;
}
.popup-container .foot-container.data-v-324e86e6 {
  display: flex;
  justify-content: space-between;
}
.popup-container .foot-container .btn.data-v-324e86e6 {
  width: 270rpx;
  height: 90rpx;
  line-height: 90rpx;
  background: #F6F6F6;
  border-radius: 18rpx;
  font-size: 32rpx;
  text-align: center;
  font-weight: bold;
  color: #666;
}
.popup-container .foot-container .btn.btn-confirm.data-v-324e86e6 {
  background: linear-gradient(90deg, #2892FF 0%, #007EFF 100%);
  color: #FFFFFF;
}
.care-con .popup-title.data-v-324e86e6 {
  font-size: 40rpx !important;
}
.care-con .popup-content.data-v-324e86e6 {
  font-size: 36rpx !important;
}
.care-con .close.data-v-324e86e6 {
  width: 54rpx !important;
  height: 54rpx !important;
  top: 30rpx !important;
  position: absolute;
}
.en-con .popup-title.data-v-324e86e6 {
  font-size: 40rpx;
}