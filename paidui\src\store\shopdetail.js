import { defineStore } from 'pinia'

export const useShopInfoStore = defineStore('shopInfo', {
  state: () => {
    // const shopInfo = uni.getStorageSync('shopInfo')
    // const info = shopInfo || []
    return {
      info: {}
    }
  },
  actions: {
    setShopInfo(shopInfo) {
      this.info = shopInfo
      // uni.setStorage({
      //   key: 'shopInfo',
      //   data: shopInfo,
      //   success: () => {}
      // })
    }
  }
})
export default {
  useShopInfoStore
}
