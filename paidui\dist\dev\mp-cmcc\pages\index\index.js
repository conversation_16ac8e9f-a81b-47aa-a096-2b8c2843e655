"use strict";
const common_vendor = require("../../common/vendor.js");
const api_paidui = require("../../api/paidui.js");
const store_user = require("../../store/user.js");
const store_shopdetail = require("../../store/shopdetail.js");
const store_options = require("../../store/options.js");
const const_index = require("../../const/index.js");
const utils_location = require("../../utils/location.js");
const utils_gioTrack = require("../../utils/gio-track.js");
const api_yundian = require("../../api/yundian.js");
const Searchbar = () => "../../components/searchbar.js";
const EMPTY = () => "../../components/empty.js";
const ShopInfo = () => "./components/shopinfo.js";
const Appointment = () => "./components/appointment.js";
const Pickupnumber = () => "./components/pickupnumber.js";
const ResultCom = () => "./components/resultcom.js";
const BaseCard = () => "./components/basecard.js";
const shopInfo = store_shopdetail.useShopInfoStore();
const CONSTANTS = {
  BIZ_CODE: {
    SUCCESS: "0000",
    UNAUTHORIZED: "401"
  },
  CARD_TYPE: {
    BASE: "basecard",
    PICKUP: "pickupnumber",
    APPOINTMENT: "appointment"
  }
};
const _sfc_main = {
  data() {
    return {
      cardType: "",
      // basecard,pickupnumber,appointment
      baseCardType: "baseCard",
      // baseCard,baseAppointCard
      stationCode: "",
      hallDetail: {},
      distance: null,
      appRecord: {},
      queueInfo: {},
      showResult: false,
      baseType: "",
      showTips: false,
      options: {},
      from: "",
      unifiedChannelId: null,
      //新的店铺编码
      isEmpty: false,
      shopEnInfo: {}
    };
  },
  components: {
    Searchbar,
    ShopInfo,
    Appointment,
    Pickupnumber,
    BaseCard,
    ResultCom,
    EMPTY
  },
  computed: {
    ...common_vendor.mapState(store_user.useUserStore, ["info", "isLogined", "geo"]),
    ...common_vendor.mapState(store_options.useOptionsStore, ["verType"]),
    showTag: function() {
      if (!this.stationCode && (this.geo && this.geo.latitude)) {
        return true;
      } else {
        return false;
      }
    },
    careClass: function() {
      if (this.verType == "care") {
        return "care-con";
      }
      if (this.verType == "en") {
        return "en-con";
      }
      return "";
    }
  },
  onLoad(options) {
    this.stationCode = options.stationCode;
    this.baseType = options.baseType;
    this.from = options.from;
    this.unifiedChannelId = options.unifiedChannelId;
    let { provinceCode: province, cityCode: city } = options;
    let userStore = store_user.useUserStore();
    userStore.setGeo({
      ...this.geo,
      province,
      city
    });
    const optionsStore = store_options.useOptionsStore();
    optionsStore.setVerType(options);
    if (this.isLogined) {
      this.logined();
    }
    my.setNavigationBar({
      reset: true,
      title: this.$t("mpappTitle")
    });
  },
  onShow: function() {
    if (this.hallDetail && this.hallDetail.provinceCode) {
      let userStore = store_user.useUserStore();
      userStore.setGeo({
        ...this.geo,
        province: this.hallDetail.provinceCode,
        city: this.hallDetail.cityCode,
        cityName: this.hallDetail.cityName
      });
    }
    let isBack = common_vendor.index.getStorageSync("isBack");
    if (isBack && this.isLogined) {
      this.logined();
    }
  },
  watch: {
    isLogined: {
      handler(newVal) {
        if (newVal) {
          this.logined();
        }
      }
    }
  },
  methods: {
    async logined() {
      common_vendor.index.showLoading();
      if (this.stationCode && this.from === "fjt" || this.unifiedChannelId) {
        let stationCode = this.unifiedChannelId || this.stationCode;
        await this.queryChannelCode(stationCode);
      }
      this.queryHomeData();
    },
    /**页面展示切换 */
    changeCardType(type, queueRes) {
      switch (type) {
        case "pickupnumberSec":
        case "appointmentSec":
          this.openResultDialog(type, queueRes);
          break;
        case "appointment":
          this.baseCardType = "baseAppointCard";
          this.gdpTrack("imp", "确定预约");
          break;
        case "cancleAppointment":
          this.baseCardType = "baseCard";
          this.cardType = "basecard";
          this.showResult = false;
          this.gdpTrack("imp", "立即预约");
          this.gdpTrack("imp", "在线预约");
      }
    },
    /**获取取号&预约结果 */
    async openResultDialog(type, queueRes) {
      if (type == "appointmentSec") {
        let result = await this.queryUserAppRecords();
        let resultEn = {};
        if (this.verType === "en" && result && result.stationCode) {
          resultEn = await this.getShopEnInfo(result.stationCode);
        }
        if (result) {
          this.hallDetail = {
            ...this.hallDetail,
            number: this.info.msisdn,
            ...result,
            distance: result.distance || this.distance,
            ...resultEn
          };
          this.cardType = "appointment";
          this.showResult = true;
          console.log("预约成功模拟", result);
        }
      }
      if (type == "pickupnumberSec") {
        this.queueInfo = queueRes.resultData;
        let resultEn = {};
        if (this.verType === "en") {
          resultEn = await this.getShopEnInfo(this.queueInfo.stationCode);
        }
        this.hallDetail = {
          ...this.hallDetail,
          ...this.queueInfo,
          ...resultEn
        };
        if (this.queueInfo) {
          this.cardType = "pickupnumber";
          this.showResult = true;
          console.log("取号成功模拟", queueRes.resultData);
        }
      }
      setTimeout(() => {
        this.logined();
        this.showResult = false;
      }, 5e3);
    },
    /**
     * 去云店查询stationCode
     */
    queryChannelCode(stationCode) {
      return new Promise((resolve) => {
        api_yundian.yundianApi.queryChannelCode(stationCode).then((res) => {
          if (res && res.data && res.data.length == 19) {
            this.stationCode = res.data;
          } else {
            this.stationCode = stationCode;
          }
          resolve(stationCode);
        }).catch(() => {
          resolve(stationCode);
        });
      });
    },
    queryShopInfo({ unifiedChannelIdList, stationCodeList }) {
      return new Promise((resolve) => {
        api_yundian.yundianApi.queryShopInfo({ unifiedChannelIdList, stationCodeList }).then((res) => {
          console.log("queryShopInfo===", { unifiedChannelIdList, stationCodeList }, res);
          if (res && res.data && res.data.shopEnInfoList && res.data.shopEnInfoList.length > 0 && this.verType === "en") {
            resolve(res.data.shopEnInfoList[0]);
          }
          if (res && res.data && res.data.shopInfoList && res.data.shopInfoList.length > 0) {
            resolve(res.data.shopInfoList[0]);
          }
          resolve({});
        }).catch(() => {
          resolve({});
        });
      });
    },
    /**获取取号&预约&附近店铺信息 */
    async queryHomeData() {
      await utils_location.getLocation(this.geo.province, this.geo.city);
      const params = {
        "latitude": this.geo.latitude || "0.0",
        "longitude": this.geo.longitude || "0.0",
        "mobile": this.info.msisdn,
        "stationCode": this.stationCode
      };
      let res = await api_paidui.paiduiApi.queryHomeData(params);
      this.multiTrack();
      if (res.bizCode === CONSTANTS.BIZ_CODE.SUCCESS) {
        this.isEmpty = false;
        console.log("getHomeData====1========res.resultData.franchisee=" + res.resultData.franchisee);
        if (res.resultData.franchisee === 2) {
          const params2 = {
            mobile: this.info.msisdn,
            stationCode: res.resultData.nearStationDetail.stationCode
          };
          let resQ = await api_paidui.paiduiApi.queryHomeDataWithQueueNumber(params2);
          console.log(resQ);
          if (resQ.bizCode === CONSTANTS.BIZ_CODE.SUCCESS) {
            let setNum = res.resultData.nearStationDetail.setNum;
            let totalWaitCount = res.resultData.nearStationDetail.totalWaitCount;
            resQ.resultData.nearStationDetail = res.resultData.nearStationDetail;
            resQ.resultData.nearStationDetail.setNum = setNum;
            resQ.resultData.nearStationDetail.totalWaitCount = totalWaitCount;
            this.handleNumber(resQ);
          }
        } else {
          this.handleNumber(res);
        }
      } else {
        console.log(res, 88888888888889, "未登录");
        this.getHomeError(res);
      }
    },
    async getHomeError(res) {
      if (res.bizCode === CONSTANTS.BIZ_CODE.UNAUTHORIZED || res.bizCode === "A0100") {
        return false;
      }
      let enInfo = await this.getShopEnInfo();
      if (enInfo.stationCode) {
        this.isEmpty = false;
        this.handleNumber({
          resultData: {
            nearStationDetail: enInfo
          }
        }, false);
      } else {
        common_vendor.index.hideLoading();
        this.isEmpty = true;
      }
    },
    async getShopEnInfo(stationCode) {
      if (this.shopEnInfo[stationCode]) {
        return this.shopEnInfo[stationCode];
      }
      let enData = {};
      if (stationCode) {
        enData = await this.queryShopInfo({
          stationCodeList: [stationCode]
        });
      } else if (this.unifiedChannelId) {
        enData = await this.queryShopInfo({
          unifiedChannelIdList: [this.unifiedChannelId]
        });
      } else if (this.stationCode) {
        enData = await this.queryShopInfo({
          stationCodeList: [this.stationCode]
        });
      }
      this.shopEnInfo[stationCode] = enData;
      return enData;
    },
    /**取号&预约&附近店铺信息处理 */
    async handleNumber(res, hasPaiduiData = true) {
      common_vendor.index.hideLoading();
      common_vendor.index.stopPullDownRefresh();
      this.showTipsFn();
      if (res.resultData.appRecord) {
        let enInfo = {};
        if (this.verType === "en") {
          enInfo = await this.getShopEnInfo(res.resultData.nearStationDetail.stationCode);
        }
        this.appRecord = {
          ...res.resultData.appRecord,
          number: this.info.msisdn,
          stationAddress: res.resultData.appStationDetail.stationAddress,
          provinceCode: res.resultData.appStationDetail.provinceCode,
          ...enInfo
        };
        this.appRecord.appHandleDate = this.appRecord.appDate + " " + this.appRecord.startTime + "~" + this.appRecord.endTime;
        this.cardType = "appointment";
      } else if (res.resultData.takeNumberRecord) {
        let enInfo = {};
        if (this.verType === "en") {
          enInfo = await this.getShopEnInfo(res.resultData.nearStationDetail.stationCode);
        }
        this.queueInfo = { ...res.resultData.takeNumberRecord, ...enInfo };
        this.cardType = "pickupnumber";
      } else {
        this.cardType = "basecard";
        if (this.baseType == "appointment") {
          this.baseCardType = "baseAppointCard";
          return false;
        }
      }
      if (res.resultData.nearStationDetail) {
        console.log("------------------------init home data--------------------");
        let enInfo = {};
        if (this.verType === "en" && hasPaiduiData) {
          enInfo = await this.getShopEnInfo(res.resultData.nearStationDetail.stationCode);
        }
        this.hallDetail = {
          ...res.resultData.nearStationDetail,
          ...enInfo
        };
        this.hallDetail.serviceInfoShort = this.hallDetail.newServiceInfo ? this.hallDetail.newServiceInfo.slice(0, 4) : [];
      } else {
        this.$dialog.alert({
          title: this.$t("notice"),
          message: "该省暂未查询到厅店信息，请切换至其他省份"
        }).then(() => {
          this.$router.push({
            name: "hallList"
          });
        });
      }
    },
    /** 获取页面详情 */
    getHallDetail() {
      api_paidui.paiduiApi.queryHallDetail({
        "latitude": this.geo.latitude,
        "longitude": this.geo.longitude,
        "stationCode": this.stationCode,
        "mobile": this.info.msisdn
      }).then((res) => {
        if (res.bizCode == CONSTANTS.BIZ_CODE.SUCCESS) {
          this.hallDetail = {
            ...this.hallDetail,
            ...res.resultData,
            distance: res.resultData.distance || this.distance
          };
          this.hallDetail.serviceInfoShort = res.resultData.newServiceInfo.slice(0, 4);
          shopInfo.setShopInfo(this.hallDetail);
          console.log("营业厅详情", this.hallDetail);
        }
      });
    },
    /**获取用户预约信息 */
    queryUserAppRecords() {
      return new Promise((resolve) => {
        api_paidui.paiduiApi.queryUserAppRecords({
          "currentPage": 1,
          "pageSize": 1,
          "mobile": this.info.msisdn
        }).then((appInfoRes) => {
          console.log("查询用户应用记录", appInfoRes);
          if (appInfoRes.bizCode == CONSTANTS.BIZ_CODE.SUCCESS && appInfoRes.resultData.appList && appInfoRes.resultData.appList.list[0] && appInfoRes.resultData.appList.list[0]) {
            let appInfo = appInfoRes.resultData.appList.list[0];
            if (appInfo.appointmentStatus == const_index.APPOINTMENTSTATUS.SUCCESS.CODE) {
              appInfo.appHandleDate = appInfo.appDate + " " + appInfo.startTime + "~" + appInfo.endTime;
              resolve(appInfo);
            } else {
              resolve(false);
            }
          } else {
            resolve(false);
          }
        });
      });
    },
    /**跳转列表页 */
    goStoreList() {
      let url = `/pages/shoplist/index?provinceCode=${this.geo.province}&cityCode=${this.geo.city}`;
      common_vendor.index.navigateTo({ url });
    },
    /**温馨提示展示时间 */
    showTipsFn() {
      this.showTips = true;
      setTimeout(() => {
        this.showTips = false;
      }, 1e4);
    },
    /**插码 */
    async multiTrack() {
      await utils_gioTrack.gdpSetGeneralProps({
        WT_page_type: "排队取号_" + const_index.ENUM_VERTYPE[this.verType]
      });
      utils_gioTrack.gdpSetUserId(this.info.gdpUserId);
      utils_gioTrack.gdpDcsMultiTrack("pageView", {
        "WT_et": "pageview",
        "WT_event": "MPXPageShow",
        "WT_ti": "预约取号"
      });
      this.gdpTrack("imp", "切换营业厅");
      this.gdpTrack("imp", "地图导航");
      if (this.baseCardType == "baseAppointCard") {
        this.gdpTrack("imp", "确定预约");
      }
      if (this.baseCardType == "baseCard") {
        this.gdpTrack("imp", "立即取号");
        this.gdpTrack("imp", "在线预约");
        this.gdpTrack("imp", "历史记录");
      }
    },
    gdpTrack(type, name) {
      utils_gioTrack.gdpDcsMultiTrack(type, {
        WT_et: type,
        WT_area_type_1: "楼层",
        WT_area_name: name,
        WT_envName: name,
        XY_env_type: "button",
        WT_page_type: const_index.ENUM_VERTYPE[this.verType] + "_排队取号_" + name
      });
    }
  },
  onPullDownRefresh() {
    this.queryHomeData();
  }
};
if (!Array) {
  const _component_Searchbar = common_vendor.resolveComponent("Searchbar");
  const _component_ShopInfo = common_vendor.resolveComponent("ShopInfo");
  const _component_BaseCard = common_vendor.resolveComponent("BaseCard");
  const _component_Pickupnumber = common_vendor.resolveComponent("Pickupnumber");
  const _component_Appointment = common_vendor.resolveComponent("Appointment");
  const _component_ResultCom = common_vendor.resolveComponent("ResultCom");
  const _component_EMPTY = common_vendor.resolveComponent("EMPTY");
  (_component_Searchbar + _component_ShopInfo + _component_BaseCard + _component_Pickupnumber + _component_Appointment + _component_ResultCom + _component_EMPTY)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.careClass !== "en-con"
  }, $options.careClass !== "en-con" ? {
    b: common_vendor.p({
      ["is-big"]: true,
      bigFont: $options.careClass == "care-con"
    })
  } : {}, {
    c: common_vendor.o($options.goStoreList),
    d: common_vendor.o($options.gdpTrack),
    e: common_vendor.p({
      ["hall-detail"]: $data.hallDetail,
      ["show-tag"]: $options.showTag,
      ["show-tips"]: $data.showTips
    }),
    f: $data.cardType == "basecard"
  }, $data.cardType == "basecard" ? {
    g: common_vendor.o($options.changeCardType),
    h: common_vendor.o($options.gdpTrack),
    i: common_vendor.p({
      ["hall-detail"]: $data.hallDetail,
      ["base-card-tyle"]: $data.baseCardType
    })
  } : {}, {
    j: $data.cardType == "pickupnumber"
  }, $data.cardType == "pickupnumber" ? {
    k: common_vendor.o($options.changeCardType),
    l: common_vendor.p({
      ["hall-detail"]: $data.queueInfo
    })
  } : {}, {
    m: $data.cardType == "appointment"
  }, $data.cardType == "appointment" ? {
    n: common_vendor.o($options.changeCardType),
    o: common_vendor.p({
      ["hall-detail"]: $data.appRecord
    })
  } : {}, {
    p: $data.showResult
  }, $data.showResult ? {
    q: common_vendor.o($options.changeCardType),
    r: common_vendor.p({
      ["hall-detail"]: $data.hallDetail,
      ["card-type"]: $data.cardType
    })
  } : {}, {
    s: $data.hallDetail && $data.hallDetail.stationCode,
    t: common_vendor.n($options.careClass),
    v: $data.isEmpty
  }, $data.isEmpty ? {
    w: common_vendor.o($options.logined)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-83a5a03c"]]);
my.createPage(MiniProgramPage);
