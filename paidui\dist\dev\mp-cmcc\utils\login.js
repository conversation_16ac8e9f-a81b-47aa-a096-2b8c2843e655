"use strict";
const common_vendor = require("../common/vendor.js");
const store_user = require("../store/user.js");
const sdk_cmcc = require("../sdk/cmcc.js");
const api_paidui = require("../api/paidui.js");
async function checkLogin() {
  const user = store_user.useUserStore();
  let msisdn = "";
  msisdn = user.info.msisdn;
  return !!(user.isLogined && user.info.msisdn == msisdn) ? msisdn : false;
}
async function forceLogin() {
  let islogin = await checkLogin();
  if (!islogin) {
    common_vendor.index.showLoading();
    let loginRes;
    const user = store_user.useUserStore();
    const appUser = await sdk_cmcc.cmcc.callGetUserInfo();
    loginRes = await api_paidui.paiduiApi.validateToken({ token: "testYiCe", loginType: "2", userInformation: "", mobile: appUser.phoneNumber || appUser.loginId });
    this.userInfo = loginRes.resultData;
    user.setUser({ ...loginRes.resultData, province: 100 });
    common_vendor.index.hideLoading();
  }
  return true;
}
const login = {
  checkLogin,
  forceLogin
};
exports.login = login;
