<template>
  <view class="shop-item" v-if="item && item.stationCode">
    <view class="shop-info"  @click="goToShop(item)" >
      <view class="img-con">
        <img src="@/static/home/<USER>" alt="">
      </view>
      <view class="shop-info-con">
        <view class="shop-title">
          <span class="span span-name">{{ item.stationName }}</span>
          <view v-if="(!isMap) && geo.latitude" class="span span-distance" @click.stop="goToMap(item)">{{ item.distance }}</view>
          <view v-if="isMap" class="span span-map">
            <view class="iconfont icon-daohang"></view>
          </view>
        </view>
        <view class="shop-address">
          {{ item.stationAddress}}
        </view>
      </view>
    </view>
    <view class="shop-busi-hour">
      <span class="span">营业时间：{{ item.workPeriod }}</span>
      <span class="span">{{ item.isBusiness? '营业中': '暂停营业' }}</span>
    </view>
    <view v-if="!isMap" class="shop-busi-scope" @click="goToShop(item)">
      <view class="label">业务范围</view>
      <view class="scope-list" @click.stop="showServiceModel(item)">
        <view v-for="service in item.serviceInfoShort" :key="service.serviceCode" class="scope-item">
          {{ service.serviceName }}
        </view>
      </view>
      <view v-show="item.newServiceInfo && item.newServiceInfo.length>maxNumLength" class="iconfont icon-arrow1" @click.stop="showServiceModel(item)" ></view>
    </view>
    <view class="shop-btn" @click="goToShop(item)">
      <view class="btn-item-label">
        <view class="btn-item-label-item">当前排队</view>
        <view class="btn-item-label-item">{{ item.totalWaitCount ? item.totalWaitCount : 0 }}</view>
        <view class="btn-item-label-item">人</view>
      </view>
      <view class="btn-item btn-quhao" :class="{'disabled':item.isBusiness==0}" @click.stop="goPickupnumber(item,item.isBusiness==0)">
        预约取号
      </view>
      <view class="btn-item btn-yuyue" :class="{'disabled':item.isAppointment==0}" @click.stop="goPickupnumber(item,item.isAppointment==0)">
        在线预约
      </view>
    </view>
    <Servicepopup ref="serPopup" :service-info="item.newServiceInfo" />
  </view>
</template>
<script>
import Servicepopup from '@/components/servicepopup.vue'
import { useUserStore } from '@/store/user'
import { mapState } from 'pinia'
export default {
  data() {
    return {
    }
  },
  props: {
    item: {
      type: Object,
      default() { return null }
    },
    isMap:{//map页面的店铺列表不展示一些功能
      type:Boolean,
      default:false
    },
    maxNumLength:{
      type:Number,
      default:3
    }
  },
  components:{
    Servicepopup
  },
  computed: {
    ...mapState(useUserStore, ['geo']),
  },
  methods:{
    goPickupnumber(item,isDisabled){
      if(this.isMap){
        uni.navigateTo({ url: `/pages/index/index?stationCode=${item.stationCode}&provinceCode=${item.provinceCode}&cityCode=${item.cityCode}` })
        return false
      }
      if(!isDisabled){
        this.goToShop(item)
      }
    },
    showServiceModel(item){
      if(item.newServiceInfo && item.newServiceInfo.length>4){
        this.$refs.serPopup.showServiceModel()
      }else{
        this.goToShop(item)
      }
    },
    goToMap(item){
      uni.navigateTo({ url: `/pages/map/index?stationCode=${item.stationCode}&latitude=${item.latitude}&longitude=${item.longitude}` })
    },
    goToShop(item){
      if(this.isMap){
        return false
      }
      uni.navigateTo({ url: `/pages/index/index?stationCode=${item.stationCode}&provinceCode=${item.provinceCode}&cityCode=${item.cityCode}` })
    }
  }
}
</script>
<style lang="scss" scoped>
.shop-item{
  width: 690px;
  background: linear-gradient( 180deg, #E3F1FF 0%, #FFFFFF 40%);
  box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.1);
  border-radius: 18px;
  margin:0 auto 24px;
  box-sizing: border-box;
  padding:24px;
  .shop-info{
    display: flex;
    .img-con{
      width:160px;
      height:160px;
      padding:17px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 18px;
      border: 2px solid #F5F5F5;
      image{
        width:126px;
        height:126px;
      }
    }
    .shop-info-con{
      margin-left:24px;
      width:470px;
      margin-top:3px;
      .shop-title{
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        .span-name{
          font-size: 36px;
          font-weight: bold;
          color: #000000;
          line-height: 40px;
        }
        .span-distance{
          font-size: 32px;
          color:rgba(0,0,0,0.6);
      }
      }
    }
  }
  .shop-address,.shop-busi-hour{
    font-size: 36px;
    color: rgba(0,0,0,0.6);
    line-height: 50px;
    margin-top:20px;
    font-weight: bold;
  }
  .shop-busi-hour{
    display: flex;
    align-items: center;
    height:40px;
    margin-top:40px;
    .span:last-child{
      height: 36px;
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #007EFF;
      padding:0 14px;
      font-size: 28px;
      color:#007EFF;
      margin-left: 16px;
      display: flex;
      align-items: center;
    }
  }
  .shop-busi-scope{
    margin-top:30px;
    display: flex;
    align-items: center;
    .label{
      font-size: 36px;
      color: rgba(0,0,0,0.6);
      font-weight: bold;
    }
    .scope-list{
      flex:1;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .scope-item{
        border-radius: 12px;
        border: 2px solid rgba(0,0,0,0.06);
        font-size: 28px;
        line-height: 34px;
        color: rgba(0,0,0,0.6);
        padding:6px 12px;
        margin-left: 12px;
        font-weight: bold;
      }
    }
    .icon-arrow1{
      font-size: 30px;
    }
  }
  .shop-btn{
    display: flex;
    align-items: center;
    margin-top: 24px;
    .btn-item-label{
      font-size: 36px;
      color: #3D3D3D;
      flex:1;
      font-weight:bold;
      display: flex;
      .btn-item-label-item:nth-child(2){
        color: #007EFF;
      }
    }
    .btn-item{
      font-weight: 400;
      font-size: 36px;
      width:198px;
      box-sizing: border-box;
      border-radius: 90px 90px 90px 90px;
      height:62px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.btn-quhao{
        border: 2px solid #007EFF;
        color:#007EFF;
      }
      &.btn-yuyue{
        background: linear-gradient( 262deg, #07B7FF 0%, #0380FF 100%);
        color:#fff;
        margin-left: 19px;
      }
      &.disabled{
        opacity:0.5;
        cursor: not-allowed;
      }

    }
  }
}
</style>
