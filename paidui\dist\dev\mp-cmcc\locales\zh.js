"use strict";
const stationInfo = {
  notice: "提示",
  nearShop: "最近厅店",
  workPeriod: "营业时间",
  serviceInfo: "业务范围",
  tipsDes: "建议您点击业务范围，确认此营业厅能够满足您的办理需求后取号",
  toogle: "切换",
  working: "营业中",
  huisinessClose: "暂停营业",
  currentQueue: "当前排队",
  stationCode: "营业厅",
  stationAddress: "地址"
};
const takeInfoObj = {
  takeInfo: "取号信息",
  takeRecord: "取号记录",
  takeNumber: "排队号码",
  waitCount: "前方等待",
  takeTime: "取号时间",
  waitDetail: "排队详情",
  waitNum: "等待人数"
};
const appointInfo = {
  appoint: "预约信息",
  appointRecord: "预约记录",
  appointNumber: "预约号码",
  appointTime: "预约办理时间",
  reservedTime: "预约时间",
  appointToday: "今天",
  appointTomorrow: "明天",
  pleaseSelectTitle: "请选择预约办理时间",
  serviceName: "办理业务",
  appointNoTime: "无可用时间段"
};
const wxTipsInfo = {
  wxTip: "温馨提示",
  wxTip1: "温馨提示",
  wxTakeSecTip: "请留意现场叫号大屏，过号需重新取号。祝您生活愉快！",
  wxAppointSec1Tip: "1、请携带您的身份证，提前到厅签到排队。请留意短信提醒",
  wxAppointSec2Tip: "2、如需重新预约，请取消当前预约后再操作，谢谢!",
  wxAppointCon1Tip: "距离预约时间30分钟可进行签到取号",
  wxAppointCon2Tip: "如需重新预约，请取消当前预约后再操作，谢谢！",
  wxCardTip: "每个号码每天最多允许5次在线取号或取消，取号成功后请注意排队情况和系统叫号，以免错过。",
  wxAppointTimeTip: "线上预约成功后，如需修改，请先取消再操作。",
  wxCancelTip: "确定要取消票号吗?"
};
const searcPlaceholder = {
  shortPlaceholder: "请输入营业厅名称/地址",
  langPlaceholder: "请输入您要搜索的营业厅名称/地址"
};
const btnInfo = {
  takeNow: "立即取号",
  appointOnline: "在线预约",
  appointConfirm: "确定预约",
  signTake: "签到取号",
  cancelAppoint: "取消预约",
  historyRecord: "历史记录",
  cancelBtn: "取消",
  confirmBtn: "确定",
  confirmBtn1: "确定",
  apptake: "预约/取号",
  cancelQueue: "取消排队"
};
const orderStatus = {
  appointSec: "预约成功",
  takeSec: "取号成功"
};
const textObj = {
  selectText: "请选择",
  mpappTitle: "预约取号",
  mapSelTitle: "导航到目的地",
  mapErrorTip: "APP未安装",
  emptyTip: "营业厅在线预约取号业务 正在维护",
  emptyBtnTip: "刷新试试",
  refreshTip: "刷新过于频繁",
  noRecord: "暂无记录",
  //
  servicHallTitle: "营业厅主营业务类型"
};
const message = {
  /**厅店详情 */
  ...stationInfo,
  /**取号信息 */
  ...takeInfoObj,
  /**预约信息 */
  ...appointInfo,
  /**订单状态信息 */
  ...orderStatus,
  /**温馨提示 */
  ...wxTipsInfo,
  /**按钮信息 */
  ...btnInfo,
  /**搜索提示信息 */
  ...searcPlaceholder,
  /**其他标题信息 */
  ...textObj
};
exports.message = message;
