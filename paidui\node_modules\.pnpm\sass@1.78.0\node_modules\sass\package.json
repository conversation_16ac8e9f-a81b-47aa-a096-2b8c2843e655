{"name": "sass", "description": "A pure JavaScript implementation of Sass.", "license": "MIT", "bugs": "https://github.com/sass/dart-sass/issues", "homepage": "https://github.com/sass/dart-sass", "repository": {"type": "git", "url": "https://github.com/sass/dart-sass"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nex3"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"chokidar": ">=3.0.0 <4.0.0", "immutable": "^4.0.0", "source-map-js": ">=0.6.2 <2.0.0"}, "keywords": ["style", "scss", "sass", "preprocessor", "css"], "types": "types/index.d.ts", "exports": {"types": "./types/index.d.ts", "node": {"require": "./sass.node.js", "default": "./sass.node.mjs"}, "default": {"require": "./sass.default.cjs", "default": "./sass.default.js"}}, "version": "1.78.0", "bin": {"sass": "sass.js"}, "main": "sass.node.js"}