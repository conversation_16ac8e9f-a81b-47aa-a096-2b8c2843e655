"use strict";
const store_user = require("../../../store/user.js");
const common_vendor = require("../../../common/vendor.js");
const Appointment = () => "./appointment.js";
const Pickupnumber = () => "./pickupnumber.js";
const _sfc_main = {
  props: {
    endType: {
      type: String,
      default: "appointment"
    },
    hallDetail: {
      type: Object,
      default: () => ({})
    },
    cardType: {
      type: String,
      default: "appointment"
    }
  },
  data() {
    return {};
  },
  components: {
    Appointment,
    Pickupnumber
  },
  computed: {
    ...common_vendor.mapState(store_user.useUserStore, ["info", "isLogined", "geo"]),
    endTitle: function() {
      return this.cardType == "appointment" ? this.$t("appointSec") : this.$t("takeSec");
    }
  },
  watch: {
    isLogined: {
      handler(newVal) {
      },
      immediate: true
    }
  },
  methods: {
    cancleAppointment() {
      this.$emit("chagneTab", "cancleAppointment");
    },
    queueHandle() {
      this.$emit("chagneTab", "pickupnumberSec");
    }
  }
};
if (!Array) {
  const _component_Pickupnumber = common_vendor.resolveComponent("Pickupnumber");
  const _component_Appointment = common_vendor.resolveComponent("Appointment");
  (_component_Pickupnumber + _component_Appointment)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.endTitle),
    b: $props.cardType == "pickupnumber"
  }, $props.cardType == "pickupnumber" ? {
    c: common_vendor.p({
      ["hall-detail"]: $props.hallDetail,
      ["hide-title"]: true
    })
  } : {}, {
    d: $props.cardType == "appointment"
  }, $props.cardType == "appointment" ? {
    e: common_vendor.o($options.cancleAppointment),
    f: common_vendor.o($options.queueHandle),
    g: common_vendor.p({
      ["hall-detail"]: $props.hallDetail,
      ["hide-title"]: true
    })
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-db66bd5c"]]);
my.createComponent(Component);
