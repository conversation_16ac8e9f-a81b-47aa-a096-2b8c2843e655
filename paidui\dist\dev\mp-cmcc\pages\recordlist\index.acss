/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content-item.data-v-b6853b6d {
  display: flex;
  margin-top: 30rpx;
  line-height: 40rpx;
}
.content-item .label-con.data-v-b6853b6d {
  display: flex;
}
.content-item .label-con .label.data-v-b6853b6d {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.6);
  min-width: 168rpx;
  margin-left: 12rpx;
}
.content-item .value.data-v-b6853b6d {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #000000;
  font-weight: bold;
}
.left-box.data-v-b6853b6d {
  width: 6rpx;
  height: 23rpx;
  background: linear-gradient(360deg, #5DADFF 0%, #007EFF 100%);
  border-radius: 90rpx 90rpx 90rpx 90rpx;
  margin-top: 7rpx;
}
.content-more.data-v-b6853b6d {
  margin: 53rpx auto 54rpx;
  height: 2rpx;
  border-top: 2rpx dashed #CDCDCD;
  position: relative;
}
.content-more .content-more-con.data-v-b6853b6d {
  width: 149rpx;
  height: 47rpx;
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  font-size: 24rpx;
}
.content-more .content-more-con .content-more-text.data-v-b6853b6d {
  margin-right: 6rpx;
}
.content-more .icon-cc-arrow-down-circle.data-v-b6853b6d {
  font-size: 20rpx;
  font-weight: bold;
}
.content-more .icon-cc-arrow-down-circle.arrow-up.data-v-b6853b6d {
  transform: rotate(180deg);
}
.wait-detail.data-v-b6853b6d {
  margin-bottom: 30rpx;
}
.wait-detail-title.data-v-b6853b6d {
  color: #000000;
  font-size: 28rpx;
  line-height: 40rpx;
  display: flex;
  margin-top: -12rpx;
  margin-bottom: 18rpx;
}
.wait-detail-title .wait-detail-title-label.data-v-b6853b6d {
  margin: 0 12rpx 0 18rpx;
  font-weight: bold;
}
.wait-detail-title .icon-refresh.data-v-b6853b6d {
  color: #007EFF;
  font-size: 32rpx;
}
.wait-detail-item.data-v-b6853b6d {
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  margin-bottom: 13rpx;
  line-height: 40rpx;
}
.wait-detail-item .label.data-v-b6853b6d {
  flex: 1;
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
}
.wait-detail-item .value.data-v-b6853b6d {
  color: #F3513B;
  font-size: 28rpx;
}
.record-item.data-v-b6853b6d {
  width: 690rpx;
  border-radius: 18rpx;
  box-shadow: 0px 3rpx 8rpx 0px rgba(0, 0, 0, 0.16);
  margin: 10rpx auto 24rpx;
}
.record-item .blue.data-v-b6853b6d {
  color: #007EFF;
}
.record-item .grey.data-v-b6853b6d {
  color: rgba(0, 0, 0, 0.4);
}
.record-item .record-item-title.data-v-b6853b6d {
  height: 64rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 0 30rpx;
  background: #E0E9FF;
  border-radius: 18rpx 18rpx 0px 0px;
}
.record-item .record-item-title .record-item-title-time.data-v-b6853b6d {
  flex: 1;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
}
.record-item .content.data-v-b6853b6d {
  padding: 0 30rpx 30rpx;
}
.record-item .content-item.data-v-b6853b6d {
  align-items: center;
}
.record-item .content-item .label-con.data-v-b6853b6d {
  min-width: 198rpx;
  color: rgba(0, 0, 0, 0.6);
}
.tabs.data-v-b6853b6d {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tabs .tab-item.data-v-b6853b6d {
  font-size: 32rpx;
  color: #000000;
  line-height: 44rpx;
}
.tabs .tab-item.data-v-b6853b6d:first-child {
  margin-right: 150rpx;
}
.tabs .tab-item.active.data-v-b6853b6d {
  font-weight: bold;
  position: relative;
}
.tabs .tab-item.active.data-v-b6853b6d::after {
  content: "";
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(270deg, #2892FF 0%, #007EFF 100%);
  border-radius: 90rpx;
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translate(-50%, 0);
}
.empty-img.data-v-b6853b6d {
  background: url("../../static/payend/empty.jpg") no-repeat center;
  height: 452rpx;
  background-size: contain;
}
.empty-text.data-v-b6853b6d {
  text-align: center;
}
.care-con .record-item-title-time.data-v-b6853b6d, .care-con .record-status.data-v-b6853b6d {
  font-size: 32rpx !important;
  font-weight: bold;
}
.care-con .record-item-title-time.data-v-b6853b6d:last-child {
  line-height: 42rpx;
}
.care-con .content-item.data-v-b6853b6d {
  font-size: 36rpx !important;
  line-height: 54rpx;
  align-items: baseline;
}
.care-con .content-item .label-con.data-v-b6853b6d {
  font-weight: bold !important;
}
.care-con .content-item .value.data-v-b6853b6d {
  font-size: 36rpx !important;
}
.care-con .tab-item.data-v-b6853b6d {
  font-size: 36rpx !important;
}
.en-con .record-item-title .record-item-title-time.data-v-b6853b6d {
  display: block !important;
}
.en-con .tab-item.data-v-b6853b6d {
  text-align: center;
  width: 330rpx;
  margin-right: 0px !important;
}
.en-con .tab-item.active.data-v-b6853b6d {
  font-weight: bold;
}
.en-con .tab-item.data-v-b6853b6d:last-child {
  width: 280rpx;
}
.en-con .record-item.data-v-b6853b6d {
  margin-top: 20rpx;
}
.en-con .record-item .content-item.data-v-b6853b6d {
  align-items: flex-start;
}
.en-con .record-item .label-con.data-v-b6853b6d {
  font-size: 28rpx;
  width: 280rpx;
}
.en-con .record-item-title.data-v-b6853b6d {
  height: 80rpx;
  line-height: 32rpx;
}