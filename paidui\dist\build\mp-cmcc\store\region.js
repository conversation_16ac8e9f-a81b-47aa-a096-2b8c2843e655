"use strict";const common_vendor=require("../common/vendor.js");const regionTmp=common_vendor.index.getStorageSync("region")||{};const useRegionStore=common_vendor.defineStore("region",{state:()=>({info:regionTmp.list||[],timestamp:regionTmp.timestamp||(new Date).valueOf()}),actions:{setRegion(region){this.info=region;common_vendor.index.setStorage({key:"region",data:{list:region,timestamp:(new Date).valueOf()},success:()=>{}})}}});exports.useRegionStore=useRegionStore;
