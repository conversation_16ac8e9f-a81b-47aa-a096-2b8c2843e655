<uni-popup class="data-v-049fc9d5" u-s="{{['d']}}" ref="__r" u-r="popup" u-i="049fc9d5-0" onVI="__l" u-p="{{e}}"><view class="{{('popup-container') + ' ' + 'data-v-049fc9d5' + ' ' + d}}"><view class="popup-title data-v-049fc9d5">{{a}}</view><view class="scope-list data-v-049fc9d5"><view a:for="{{b}}" a:for-item="service" a:key="b" class="{{('scope-item') + ' ' + 'data-v-049fc9d5' + ' ' + (service.c && 'scope-item-both')}}">{{service.a}}</view></view><view class="close data-v-049fc9d5" onTap="{{c}}"></view></view></uni-popup>