"use strict";const common_vendor=require("../../../common/vendor.js");const api_paidui=require("../../../api/paidui.js");const utils_utils=require("../../../utils/utils.js");const store_user=require("../../../store/user.js");const store_options=require("../../../store/options.js");const const_index=require("../../../const/index.js");const BaseInfo=()=>"./baseinfo.js";const user=store_user.useUserStore();const _sfc_main={components:{BaseInfo:BaseInfo},props:{hallDetail:{type:Object,default:()=>({})},baseCardTyle:{type:String}},data(){return{basicVisible:false,indicatorStyle:`height: 50px`,days:[],value:[0,0],appDate:"",appointmentTimeList:[],appointmentTime:{},waitCountData:null,confirmValue:[],careClass:null,careStyle:`height:50px;line-height:50px`}},computed:{totalWaitCount:function(){if(this.waitCountData&&this.waitCountData.isNew){return this.waitCountData.waitCount}if(this.hallDetail.totalWaitCount){return this.hallDetail.totalWaitCount}return 0},isBusiness:function(){return this.hallDetail.takeNumber==1&&this.hallDetail.isBusiness==1},isAppointment:function(){return this.hallDetail.takeNumber==1&&this.hallDetail.isAppointment==1},...common_vendor.mapState(store_options.useOptionsStore,["verType"])},created(){if(this.verType=="care"){this.careClass="care-con";this.careStyle="font-size:20px;height:50px;line-height:50px"}if(this.verType=="en"){this.careClass="en-con"}},watch:{appDate:function(newVal,oldVal){if(oldVal&&newVal!=oldVal){this.queryAppTime()}}},methods:{chagneTab(btnType,disabled){if(disabled){return false}if(btnType=="pickupnumberSec"){this.userTakeNumber(btnType);return false}this.$emit("gdpTrack","clk","在线预约");this.$emit("chagneTab",btnType)},userTakeNumber(btnType){let data={stationCode:this.hallDetail.stationCode,takeType:2,mobile:user.info.msisdn,serviceCode:"H",carrierOperator:"002",takeChannel:1};if(this.verType=="en"){data.queueVersion="en";data.enStationName=this.hallDetail.stationName;data.enStationAddress=this.hallDetail.stationAddress}this.$emit("gdpTrack","clk","立即取号");let _this=this;api_paidui.paiduiApi.userTakeNumber(data).then((res=>{console.log(res);if(res.bizCode=="0000"){_this.$emit("chagneTab",btnType,res)}else{common_vendor.index.showToast({title:this.getErrorMsg(res.bizDesc),duration:3e3})}}))},getAppTime(){let date=new Date;this.days=[{label:this.$t("appointToday"),value:utils_utils.parseTime(new Date(date.getTime()),"{y}-{m}-{d}")},{label:this.$t("appointTomorrow"),value:utils_utils.parseTime(new Date(date.getTime()+1e3*60*60*24),"{y}-{m}-{d}")}];if(this.confirmValue&&this.confirmValue.length==0){this.appDate=this.days[0].value}else{this.value=[this.confirmValue[0],this.confirmValue[1]];this.appDate=this.days[this.confirmValue[0]].value}this.queryAppTime()},queryAppTime(){let data={appDate:this.appDate,stationCode:this.hallDetail.stationCode};api_paidui.paiduiApi.queryAppTime(data).then((res=>{console.log("可预约时间段",res);if(res.bizCode=="0000"){this.appointmentTimeList=res.resultData.appResources;if(this.appointmentTimeList.length==0){this.appointmentTime={};this.appointmentTimeList=[this.$t("appointNoTime")]}}else{common_vendor.index.showToast({title:this.getErrorMsg(res.bizDesc),duration:3e3})}}))},userAppointment(){if(!(this.appointmentTime&&this.appointmentTime.startTime)){return false}this.$emit("gdpTrack","clk","确定预约");let data={bookingPeriodId:this.appointmentTime.bookingPeriodId,stationCode:this.hallDetail.stationCode,appDate:this.appDate,mobile:user.info.msisdn,appChannel:1,serviceCode:"H",carrierOperator:"002",startTime:this.appointmentTime.startTime,endTime:this.appointmentTime.endTime};if(this.verType=="en"){data.queueVersion="en";data.enStationName=this.hallDetail.stationName;data.enStationAddress=this.hallDetail.stationAddress}let _this=this;api_paidui.paiduiApi.userAppointment(data).then((res=>{console.log("预约结果",res);if(res.bizCode=="0000"){_this.$emit("chagneTab","appointmentSec")}else{common_vendor.index.showToast({title:this.getErrorMsg(res.bizDesc),duration:3e3})}}))},getErrorMsg(msg){if(this.verType=="en"&&msg.includes("当天最大预约取号操作次数")){const numbers=msg.replace(/(.*[^\d])(\d+)(.*)/g,"You've hit the daily cap of $2 appointments!");console.log(numbers);return numbers}if(this.verType=="en"){return const_index.ERRORINFO[msg]||"The system is busy. Please try again later"}return msg},openAppTime(){this.getAppTime();this.$refs.appTimePopup.open("bottom")},onChange(e){this.value=e.value||e.detail.value;this.appDate=this.days[this.value[0]].value},onConfirm(){this.confirmValue=Object.assign({},this.value);this.appDate=this.days[this.value[0]].value;this.appointmentTime=this.appointmentTimeList[this.value[1]];this.closePopup()},closePopup(){this.$refs.appTimePopup.close()},goRecord(){this.$emit("gdpTrack","clk","历史记录");let url="/pages/recordlist/index?basetyle=picknumber";common_vendor.index.navigateTo({url:url})},getWaitCount(res){this.waitCountData=res}}};if(!Array){const _component_BaseInfo=common_vendor.resolveComponent("BaseInfo");const _easycom_uni_popup2=common_vendor.resolveComponent("uni-popup");(_component_BaseInfo+_easycom_uni_popup2)()}const _easycom_uni_popup=()=>"../../../node-modules/npm-scope-dcloudio/uni-ui/lib/uni-popup/uni-popup.js";if(!Math){_easycom_uni_popup()}function _sfc_render(_ctx,_cache,$props,$setup,$data,$options){return common_vendor.e({a:$props.hallDetail},$props.hallDetail?common_vendor.e({b:$props.baseCardTyle=="baseAppointCard"},$props.baseCardTyle=="baseAppointCard"?{c:common_vendor.t(_ctx.$t("appointTime")),d:common_vendor.t($data.days[$data.confirmValue[0]]?$data.days[$data.confirmValue[0]].label:""),e:common_vendor.t(this.appointmentTime&&this.appointmentTime.startTime?this.appointmentTime.startTime+"-"+this.appointmentTime.endTime:_ctx.$t("selectText")),f:common_vendor.o(((...args)=>$options.openAppTime&&$options.openAppTime(...args))),g:common_vendor.t(_ctx.$t("wxTip")),h:common_vendor.t(_ctx.$t("wxAppointTimeTip")),i:common_vendor.p({["hall-detail"]:$props.hallDetail}),j:common_vendor.t(_ctx.$t("appointConfirm")),k:!(this.appointmentTime&&this.appointmentTime.startTime)?1:"",l:common_vendor.o(($event=>$options.userAppointment()))}:{},{m:$props.baseCardTyle=="baseCard"},$props.baseCardTyle=="baseCard"?{n:common_vendor.t(_ctx.$t("currentQueue")),o:common_vendor.t($options.totalWaitCount),p:common_vendor.o($options.getWaitCount),q:common_vendor.p({["hall-detail"]:$props.hallDetail}),r:common_vendor.t(_ctx.$t("takeNow")),s:!$options.isBusiness?1:"",t:common_vendor.o(($event=>$options.chagneTab("pickupnumberSec",!$options.isBusiness))),v:common_vendor.t(_ctx.$t("appointOnline")),w:!$options.isAppointment?1:"",x:common_vendor.o(($event=>$options.chagneTab("appointment",!$options.isAppointment)))}:{},{y:common_vendor.t(_ctx.$t("historyRecord")),z:common_vendor.o(((...args)=>$options.goRecord&&$options.goRecord(...args))),A:common_vendor.o(((...args)=>$options.goRecord&&$options.goRecord(...args))),B:$props.baseCardTyle=="baseCard"},$props.baseCardTyle=="baseCard"?{C:common_vendor.t(_ctx.$t("wxTip")),D:common_vendor.t(_ctx.$t("wxCardTip"))}:{},{E:common_vendor.t(_ctx.$t("pleaseSelectTitle")),F:common_vendor.f($data.days,((item,k0,i0)=>({a:common_vendor.t(item.label)}))),G:common_vendor.s($data.careStyle),H:common_vendor.f($data.appointmentTimeList,((item,k0,i0)=>({a:common_vendor.t(item.startTime?item.startTime+"~"+item.endTime:item)}))),I:common_vendor.s($data.careStyle),J:$data.value,K:common_vendor.o(((...args)=>$options.onChange&&$options.onChange(...args))),L:$data.indicatorStyle,M:common_vendor.t(_ctx.$t("cancelBtn")),N:common_vendor.o(((...args)=>$options.closePopup&&$options.closePopup(...args))),O:common_vendor.t(_ctx.$t("confirmBtn")),P:common_vendor.o(((...args)=>$options.onConfirm&&$options.onConfirm(...args))),Q:common_vendor.o(((...args)=>$options.closePopup&&$options.closePopup(...args))),R:common_vendor.n($data.careClass),S:common_vendor.p({["border-radius"]:"10px",["background-color"]:"#fff"}),T:common_vendor.n($data.careClass)}):{})}const Component=common_vendor._export_sfc(_sfc_main,[["render",_sfc_render],["__scopeId","data-v-8eafca19"]]);my.createComponent(Component);
