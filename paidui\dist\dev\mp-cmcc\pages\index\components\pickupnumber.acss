/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.card.data-v-1f73e369 {
  border-top-left-radius: 18rpx;
  border-top-right-radius: 18rpx;
  background: #fff;
  position: relative;
}
.card .title.data-v-1f73e369 {
  height: 72rpx;
  background: #F5F7FB;
  display: flex;
  align-items: center;
  padding: 0 10rpx 0 30rpx;
  border-radius: 18rpx 18rpx 0px 0px;
}
.card .title .title-name.data-v-1f73e369 {
  font-size: 32rpx;
  color: #3D3D3D;
  flex: 1;
}
.card .title .title-btn.data-v-1f73e369, .card .title .icon-arrow1.data-v-1f73e369 {
  font-size: 24rpx;
  color: #007EFF;
}
.card .content.data-v-1f73e369 {
  padding: 0 30rpx 30rpx;
}
.card .content.data-v-1f73e369::before {
  content: "";
  display: table;
}
.card-footer.data-v-1f73e369 {
  width: 690rpx;
  height: 13rpx;
  background: url("../../../static/home/<USER>") no-repeat 0 0;
  background-size: cover;
  position: absolute;
  bottom: -13rpx;
  left: 0;
}
.tips-title.data-v-1f73e369 {
  font-size: 28rpx;
  color: #000000;
  line-height: 40rpx;
  margin-bottom: 12rpx;
  font-weight: bold;
}
.tips-content.data-v-1f73e369 {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.6);
  line-height: 38rpx;
}
.btn-con.data-v-1f73e369 {
  display: flex;
  justify-content: space-between;
  padding: 0 39rpx;
}
.btn-con .btn.data-v-1f73e369 {
  width: 259rpx;
  height: 80rpx;
  color: #999;
  text-align: center;
  line-height: 80rpx;
  font-size: 32rpx;
  border-radius: 90rpx;
  font-weight: bold;
}
.btn-con .btn-cancle.data-v-1f73e369 {
  color: #007EFF;
  border: 2rpx solid #007EFF;
}
.btn-con .btn-confirm.data-v-1f73e369 {
  color: #fff;
  background: linear-gradient(262deg, #07B7FF 0%, #0380FF 100%);
}
.btn-con .btn.disabled.data-v-1f73e369 {
  opacity: 0.5;
  cursor: not-allowed;
}
.btn-con-center.data-v-1f73e369 {
  justify-content: center;
}
.btn-con-center .btn-confirm.data-v-1f73e369 {
  width: 363rpx;
}
.content-more.data-v-1f73e369 {
  margin: 60rpx auto 54rpx;
  height: 2rpx;
  border-top: 2rpx dashed #CDCDCD;
  position: relative;
}
.content-more .content-more-con.data-v-1f73e369 {
  width: 149rpx;
  height: 47rpx;
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  font-size: 24rpx;
}
.icon-arrow-blue.data-v-1f73e369 {
  width: 40rpx;
  height: 40rpx;
  display: block;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.icon-arrow-blue-little.data-v-1f73e369 {
  width: 30rpx;
  height: 30rpx;
  display: block;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.care-con .tips-title.data-v-1f73e369, .care-con .tips-content.data-v-1f73e369 {
  font-size: 32rpx !important;
  font-weight: bold !important;
  line-height: 48rpx;
}
.care-con .tips-title.font30.data-v-1f73e369, .care-con .tips-content.font30.data-v-1f73e369 {
  font-size: 30rpx !important;
}
.care-con .title-btn.data-v-1f73e369 {
  transform: translate(0, 1rpx) !important;
}
.number-box.data-v-1f73e369 {
  width: 283rpx;
  min-height: 274rpx;
  margin: 34rpx auto 0;
  text-align: center;
  padding-top: 12rpx;
}
.number-box-title.data-v-1f73e369, .number-box-wait.data-v-1f73e369 {
  font-size: 28rpx;
  color: #000;
  line-height: 40rpx;
}
.number-box-number.data-v-1f73e369 {
  font-size: 90rpx;
  color: #007EFF;
  line-height: 72rpx;
  font-weight: bold;
  margin: 24rpx auto 24rpx;
}
.number-box-wait.data-v-1f73e369 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12rpx;
  line-height: 62rpx;
}
.number-box-wait .wait-number.data-v-1f73e369 {
  font-size: 44rpx;
  font-weight: bold;
  margin-left: 10rpx;
}
.btn-item.data-v-1f73e369 {
  font-size: 32rpx;
  width: 258rpx;
  height: 80rpx;
  box-sizing: border-box;
  border-radius: 90rpx 90rpx 90rpx 90rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #007EFF;
  color: #007EFF;
  margin: 20rpx auto 0;
}
.care-con.data-v-1f73e369 {
  margin-bottom: 50rpx;
}
.care-con .title.data-v-1f73e369 {
  height: 80rpx;
}
.care-con .title .title-name.data-v-1f73e369 {
  font-size: 36rpx;
  font-weight: bold;
}
.care-con .title .title-btn.data-v-1f73e369 {
  font-size: 32rpx;
  font-weight: bold;
}
.care-con .title .icon-arrow1.data-v-1f73e369 {
  font-size: 32rpx;
}
.care-con .number-box.data-v-1f73e369 {
  padding-top: 2rpx;
  height: auto !important;
}
.care-con .number-box-number.data-v-1f73e369 {
  margin: 36rpx auto 18rpx !important;
  font-size: 104rpx !important;
  line-height: 104rpx !important;
}
.care-con .number-box-title.data-v-1f73e369 {
  font-size: 32rpx !important;
  font-weight: bold;
}
.care-con .number-box-wait.data-v-1f73e369 {
  font-size: 36rpx !important;
  font-weight: bold;
  line-height: 44rpx !important;
}
.care-con .wait-number.data-v-1f73e369 {
  font-size: 44rpx !important;
}
.care-con.content.data-v-1f73e369 {
  margin-top: 48rpx !important;
}
.care-con .btn-item.data-v-1f73e369 {
  font-size: 40rpx;
}
.en-con .title-name.data-v-1f73e369 {
  width: 300rpx;
  font-size: 24rpx !important;
}
.en-con .title-btn.data-v-1f73e369 {
  width: 230rpx;
}
.en-con .btn-item.data-v-1f73e369 {
  width: 290rpx;
}