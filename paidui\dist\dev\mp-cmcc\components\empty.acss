/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.empty-con.data-v-f29883ec {
  padding-top: 250rpx;
}
.empty-img.data-v-f29883ec {
  width: 612rpx;
  height: 318rpx;
  background: url("../static/home/<USER>") no-repeat center;
  background-size: contain;
  margin: 0 auto;
}
.empty-text.data-v-f29883ec {
  font-size: 36rpx;
  font-weight: bold;
  width: 420rpx;
  text-align: center;
  color: #000000;
  margin: 24rpx auto 30rpx;
  line-height: 54rpx;
}
.empty-btn.data-v-f29883ec {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  width: 438rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: 2rpx solid #007EFF;
  color: #007EFF;
  border-radius: 90rpx;
  margin: 0 auto;
  box-sizing: border-box;
}
.care-con .empty-btn.data-v-f29883ec {
  width: 258rpx;
  font-size: 36rpx;
}
.en-con .empty-text.data-v-f29883ec {
  width: 548rpx;
}
.care-con .empty-text.data-v-f29883ec {
  width: 496rpx;
  line-height: 60rpx;
  padding: 0 32rpx;
  box-sizing: border-box;
}