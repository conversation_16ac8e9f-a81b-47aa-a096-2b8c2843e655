import paiduiApi from "@/api/paidui.js"
// import cmcc from "@/sdk/cmcc.js"
import { useUserStore } from "@/store/user.js"
// import coordtransform from "coordtransform"

export async function getOriginLocation() {
  return new Promise((resolve, reject) => {
    my.getLocation({
      type: 1
    }).then(location => {
      resolve(location)
    }).catch(()=>{
      resolve({})
    })
  })
  // #ifdef MP-CMCC-LOCAL
  // #else
  // return await cmcc.callGetLocation()
  // #endif
}
const adCodeMap = {
  110000: { provinceCode: 100, provinceName: "北京" },
  440000: { provinceCode: 200, provinceName: "广东" },
  310000: { provinceCode: 210, provinceName: "上海" },
  120000: { provinceCode: 220, provinceName: "天津" },
  500000: { provinceCode: 230, provinceName: "重庆" },
  210000: { provinceCode: 240, provinceName: "辽宁" },
  320000: { provinceCode: 250, provinceName: "江苏" },
  420000: { provinceCode: 270, provinceName: "湖北" },
  510000: { provinceCode: 280, provinceName: "四川" },
  610000: { provinceCode: 290, provinceName: "陕西" },
  130000: { provinceCode: 311, provinceName: "河北" },
  140000: { provinceCode: 351, provinceName: "山西" },
  410000: { provinceCode: 371, provinceName: "河南" },
  220000: { provinceCode: 431, provinceName: "吉林" },
  230000: { provinceCode: 451, provinceName: "黑龙江" },
  150000: { provinceCode: 471, provinceName: "内蒙古" },
  370000: { provinceCode: 531, provinceName: "山东" },
  340000: { provinceCode: 551, provinceName: "安徽" },
  330000: { provinceCode: 571, provinceName: "浙江" },
  350000: { provinceCode: 591, provinceName: "福建" },
  430000: { provinceCode: 731, provinceName: "湖南" },
  450000: { provinceCode: 771, provinceName: "广西" },
  360000: { provinceCode: 791, provinceName: "江西" },
  520000: { provinceCode: 851, provinceName: "贵州" },
  530000: { provinceCode: 871, provinceName: "云南" },
  540000: { provinceCode: 891, provinceName: "西藏" },
  460000: { provinceCode: 898, provinceName: "海南" },
  620000: { provinceCode: 931, provinceName: "甘肃" },
  640000: { provinceCode: 951, provinceName: "宁夏" },
  630000: { provinceCode: 971, provinceName: "青海" },
  650000: { provinceCode: 991, provinceName: "新疆" },
}

function getAdProvince(adCode) {
  let adCodeStr = adCode.slice(0,2)
  return adCodeMap[adCodeStr+"0000"]
}
export async function getLocation(optionProvince,optionCity) {
  const user = useUserStore()
  const locationRes = await getOriginLocation()
  console.log(locationRes,'app:locationRes')
  const {
    latitude = '', longitude = '', city = "北京", adcode = "", cityAdcode
  } = locationRes
  console.log(user.info,'user.info')
  let myCode = cityAdcode||adcode
  const provinceCode = myCode ? getAdProvince(myCode).provinceCode : user.info.province
  const cityRes = await paiduiApi.getCityInfoByCityName({
    provinceCode,
    cityName: city.replace('市', '')
  }, provinceCode)
  const cityCode = cityRes && cityRes.resultData && cityRes.resultData.cityCode ? cityRes.resultData.cityCode : ''
  user.setGeo({
    longitude: longitude || '',
    latitude: latitude || '',
    province: optionProvince || provinceCode || user.info.province,
    city: optionCity||cityCode
  })
}
export async function getLocationAndProv({provinceCode,cityCode}){
  if(provinceCode){
    await getIndexLocation(provinceCode)
    const user = useUserStore()
    const cityRes = await paiduiApi.getCityInfoByCode({
      provinceCode,
      cityCode
    })
    let cityName = cityRes.resultData && cityRes.resultData.cityName ? cityRes.resultData.cityName : ''
    user.setGeo({
      ...user.geo,
      province: provinceCode,
      city: cityCode,
      cityName,
    })
  }else{
    getLocation()
  }
}
export async function getIndexLocation(province) {
  const user = useUserStore()

  const locationRes = await getOriginLocation()
  const {
    latitude = '', longitude = ''
  } = locationRes

  user.setGeo({
    longitude: longitude || '',
    latitude: latitude || '',
    province: province || user.info.province,
  })
}


