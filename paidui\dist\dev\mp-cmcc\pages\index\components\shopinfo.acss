/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.shop-title.data-v-c6dd0a0d {
  display: flex;
  align-items: center;
  height: 56rpx;
  line-height: 56rpx;
}
.shop-title .shop-title-left.data-v-c6dd0a0d {
  display: flex;
  width: 600rpx;
  align-items: center;
}
.shop-title .span-tag.data-v-c6dd0a0d {
  width: 120rpx;
  height: 42rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(270deg, #FEAB74 0%, #F9382B 100%);
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  margin-right: 6rpx;
}
.shop-title .span-name.data-v-c6dd0a0d {
  flex: 1;
  color: #000000;
  font-size: 40rpx;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.shop-title .span-btn-con.data-v-c6dd0a0d {
  display: flex;
  align-items: center;
}
.shop-title .span-toggle.data-v-c6dd0a0d {
  font-size: 24rpx;
  color: #000;
  margin-right: 6rpx;
}
.icon-arrow-icon.data-v-c6dd0a0d {
  width: 30rpx;
  height: 30rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.shop-address.data-v-c6dd0a0d {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  margin-top: 25rpx;
}
.shop-address .span-address.data-v-c6dd0a0d {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.shop-address .span-distance.data-v-c6dd0a0d {
  background: #E5F2FF;
  border-radius: 90rpx;
  font-size: 24rpx;
  min-width: 80rpx;
  height: 42rpx;
  display: flex;
  align-items: center;
  color: #007EFF;
  justify-content: center;
}
.shop-address .span-distance .icon-navigation-icon.data-v-c6dd0a0d {
  width: 26rpx;
  height: 26rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.shop-address .icon-address-icon.data-v-c6dd0a0d {
  width: 18rpx;
  height: 23rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
  margin-right: 4rpx;
}
.shop-busi-hour.data-v-c6dd0a0d {
  margin-top: 25rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.6);
  line-height: 34rpx;
}
.shop-busi-hour .span.data-v-c6dd0a0d:last-child {
  height: 36rpx;
  border-radius: 6rpx;
  border: 1rpx solid #007EFF;
  padding: 0 14rpx;
  font-size: 22rpx;
  color: #007EFF;
  font-weight: bold;
  margin-left: 12rpx;
}
.shop-busi-scope.data-v-c6dd0a0d {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
}
.shop-busi-scope .label.data-v-c6dd0a0d {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.6);
}
.shop-busi-scope .scope-list.data-v-c6dd0a0d {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.shop-busi-scope .scope-list .scope-item.data-v-c6dd0a0d {
  border-radius: 12rpx;
  font-size: 22rpx;
  line-height: 34rpx;
  color: rgba(0, 0, 0, 0.6);
  padding: 6rpx 12rpx;
  margin-left: 12rpx;
  background: #fff;
}
.shop-tips.data-v-c6dd0a0d {
  min-width: 670rpx;
  height: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 22rpx;
  background: linear-gradient(270deg, #2892FF 0%, #007EFF 100%);
  border-radius: 90rpx;
  position: relative;
  margin: 27rpx auto 0;
}
.shop-tips .icon-tips.data-v-c6dd0a0d {
  font-size: 28rpx;
}
.shop-tips.data-v-c6dd0a0d::before {
  content: "";
  width: 16rpx;
  height: 16rpx;
  background: #007EFF;
  transform: rotate(45deg);
  position: absolute;
  left: 35rpx;
  top: -6rpx;
}
.care-con .shop-title.data-v-c6dd0a0d {
  height: auto !important;
  min-height: 30rpx;
  align-items: flex-start;
}
.care-con .shop-title .shop-title-left.data-v-c6dd0a0d {
  display: block;
}
.care-con .shop-title .span-tag-con.data-v-c6dd0a0d {
  display: inline-block;
  vertical-align: bottom;
  height: 60rpx;
  padding-top: 6rpx;
  box-sizing: border-box;
}
.care-con .shop-title .span-tag.data-v-c6dd0a0d {
  padding: 2rpx 10rpx;
  background: linear-gradient(90deg, #FEAB74 1%, #F9382B 100%);
  font-size: 28rpx;
  float: left;
}
.care-con .shop-title .span-btn-con.data-v-c6dd0a0d {
  width: 125rpx;
  border-radius: 90rpx;
  line-height: 61rpx;
  background: linear-gradient(266deg, #07B7FF 3%, #0380FF 94%);
  padding-left: 20rpx;
  box-sizing: border-box;
}
.care-con .shop-title .span-btn-con .span-toggle.data-v-c6dd0a0d {
  font-size: 30rpx;
  color: #fff;
  margin-right: 0px;
}
.care-con .shop-title .span-btn-con .icon-arrow-icon.data-v-c6dd0a0d {
  width: 30rpx;
  height: 30rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
}
.care-con .span-name.data-v-c6dd0a0d, .care-con .span-address.data-v-c6dd0a0d {
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  display: inline;
  min-height: 30rpx;
}
.care-con .shop-address.data-v-c6dd0a0d {
  font-size: 36rpx;
  margin-top: 32rpx;
  font-weight: bold;
  align-items: flex-start;
}
.care-con .shop-address .span-distance.data-v-c6dd0a0d {
  margin-left: 26rpx;
  font-size: 26rpx;
  min-width: 130rpx;
  padding: 0 10rpx;
}
.care-con .shop-address .icon-address-icon.data-v-c6dd0a0d {
  width: 26rpx;
  height: 33rpx;
  background: url("../../../static/home/<USER>") center no-repeat;
  background-size: contain;
  margin: 5rpx 6rpx 0 0;
}
.care-con .shop-busi-hour.data-v-c6dd0a0d {
  margin-top: 30rpx;
  font-size: 36rpx;
  line-height: 37rpx;
  font-weight: bold;
}
.care-con .shop-busi-hour .span.data-v-c6dd0a0d:last-child {
  padding: 0 14rpx;
  font-size: 28rpx;
  margin-left: 17rpx;
  height: auto;
  line-height: 38rpx;
}
.care-con .shop-busi-scope.data-v-c6dd0a0d {
  margin-top: 34rpx;
}
.care-con .shop-busi-scope .label.data-v-c6dd0a0d {
  width: auto;
  font-size: 36rpx;
  font-weight: bold;
}
.care-con .shop-busi-scope .scope-list .scope-item.data-v-c6dd0a0d {
  font-size: 28rpx;
  line-height: 34rpx;
  font-weight: bold;
  padding: 6rpx 17rpx;
}
.care-con .shop-tips.data-v-c6dd0a0d {
  width: 690rpx;
  height: 135rpx;
  font-size: 32rpx;
  border-radius: 18rpx;
  margin: 30rpx auto 0;
  line-height: 54rpx;
  padding: 12rpx 35rpx;
  box-sizing: border-box;
  align-items: baseline;
}
.care-con .shop-tips .icon-tips.data-v-c6dd0a0d {
  font-size: 32rpx;
  display: block;
  margin-right: 12rpx;
}
.care-con .shop-tips.data-v-c6dd0a0d::before {
  content: "";
  width: 20rpx;
  height: 20rpx;
  left: 50rpx;
  top: -10rpx;
}
.en-con .shop-tips.data-v-c6dd0a0d {
  font-size: 20rpx;
  height: 74rpx;
  width: 670rpx;
}
.en-con .shop-title.data-v-c6dd0a0d {
  flex-wrap: wrap;
  height: auto;
  width: auto;
}
.en-con .shop-title .span-tag.data-v-c6dd0a0d {
  width: 324rpx;
  background: linear-gradient(90deg, #FEAB74 1%, #F9382B 100%);
  margin-bottom: 10rpx;
}
.en-con .shop-title-left.data-v-c6dd0a0d {
  width: auto;
  display: block;
}
.en-con .shop-address.data-v-c6dd0a0d {
  align-items: flex-start;
}
.en-con .shop-address .icon-address-icon.data-v-c6dd0a0d {
  margin-top: 4rpx;
}
.en-con .span-name.data-v-c6dd0a0d, .en-con .span-address.data-v-c6dd0a0d {
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  flex: 577rpx;
}
.en-con .shop-tips.data-v-c6dd0a0d, .en-con .shop-tips.data-v-c6dd0a0d::before {
  background: #585C66;
}
.en-con .shop-tips-text.data-v-c6dd0a0d {
  padding: 0px 16rpx;
  text-indent: 30rpx;
}