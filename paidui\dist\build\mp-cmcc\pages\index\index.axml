<view hidden="{{!s}}" class="{{('home') + ' ' + 'data-v-105cfec5' + ' ' + t}}"><searchbar a:if="{{a}}" class="data-v-105cfec5" u-i="105cfec5-0" onVI="__l" u-p="{{b}}"/><view class="container data-v-105cfec5"><shop-info class="data-v-105cfec5" onGoStoreList="{{c}}" onGdpTrack="{{d}}" u-i="105cfec5-1" onVI="__l" u-p="{{e}}"/><view class="card-con data-v-105cfec5"><base-card a:if="{{f}}" class="data-v-105cfec5" onChagneTab="{{g}}" onGdpTrack="{{h}}" u-i="105cfec5-2" onVI="__l" u-p="{{i}}"/><pickupnumber a:if="{{j}}" class="data-v-105cfec5" onChagneTab="{{k}}" u-i="105cfec5-3" onVI="__l" u-p="{{l}}"/><appointment a:if="{{m}}" class="data-v-105cfec5" onChagneTab="{{n}}" u-i="105cfec5-4" onVI="__l" u-p="{{o}}"/></view></view><view a:if="{{p}}" class="result-con data-v-105cfec5"><result-com class="data-v-105cfec5" onChagneTab="{{q}}" u-i="105cfec5-5" onVI="__l" u-p="{{r}}"/></view></view><e-m-p-t-y a:if="{{v}}" class="data-v-105cfec5" onRefresh="{{w}}" u-i="105cfec5-6" onVI="__l"/>