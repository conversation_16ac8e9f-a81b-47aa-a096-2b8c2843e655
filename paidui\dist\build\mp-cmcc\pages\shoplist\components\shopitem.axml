<view a:if="{{a}}" class="{{('shop-item') + ' ' + 'data-v-eac7f877' + ' ' + A}}"><view class="shop-info data-v-eac7f877" onTap="{{l}}"><view class="img-con data-v-eac7f877"><image class="data-v-eac7f877" src="{{b}}" alt=""></image></view><view class="shop-info-con data-v-eac7f877"><view class="shop-title data-v-eac7f877"><label class="span span-name data-v-eac7f877">{{c}}</label><view a:if="{{d}}" class="span span-distance data-v-eac7f877" catchTap="{{f}}">{{e}}</view><view a:if="{{g}}" class="span span-map data-v-eac7f877" catchTap="{{h}}"><view class="iconfont icon-daohang data-v-eac7f877"></view></view></view><view class="shop-address data-v-eac7f877">{{i}}</view><view class="shop-busi-hour data-v-eac7f877"><label class="span data-v-eac7f877">营业时间：{{j}}</label><label class="span data-v-eac7f877">{{k}}</label></view></view></view><view a:if="{{m}}" class="shop-busi-scope data-v-eac7f877" onTap="{{r}}"><view class="label data-v-eac7f877">业务范围</view><view class="scope-list data-v-eac7f877" catchTap="{{o}}"><view a:for="{{n}}" a:for-item="service" a:key="b" class="scope-item data-v-eac7f877">{{service.a}}</view></view><view hidden="{{!p}}" class="iconfont icon-arrow1 data-v-eac7f877" catchTap="{{q}}"></view></view><view class="shop-btn data-v-eac7f877" onTap="{{y}}"><view class="btn-item-label data-v-eac7f877"> 当前排队{{s}}人 </view><view class="{{('btn-item') + ' ' + 'btn-quhao' + ' ' + 'data-v-eac7f877' + ' ' + (t && 'disabled')}}" catchTap="{{v}}"> 预约取号 </view><view class="{{('btn-item') + ' ' + 'btn-yuyue' + ' ' + 'data-v-eac7f877' + ' ' + (w && 'disabled')}}" catchTap="{{x}}"> 在线预约 </view></view><servicepopup class="data-v-eac7f877" ref="__r" u-r="serPopup" u-i="eac7f877-0" onVI="__l" u-p="{{z}}"/></view>