<template>
	<view class="navi-selector">
		<view class="navi-selector-title">{{titleText}}</view>
		<view class="navi-selector-content">
			<view class="navi-selector-content-box">
				<view class="navi-selector-content-item" v-for="(item,index) in mapData" :key="index" @click.stop="select(item,index)">
					{{item.text}}
				</view>
			</view>
		</view>
    <view class="close" @click="close"></view>
	</view>
</template>

<script>
	export default {
		name: 'NaviSelector',
		emits:['select', 'close'],
		props: {
			title: {
				type: String,
				default: '导航到目的地'
			},
      cancel: {
        type: String,
        default: '取消'
      },
      mapList: {
        type: Array,
        default: () => null
      }
		},
		data() {
			return {
				bottomData: [
					{
						text: '高德导航',
						name: 'amap'
					},
					{
						text: '百度导航',
						name: 'baidu'
					},
				]
			}
		},
		created() {},
		computed: {
			cancelText() {
				return this.cancel
			},
		  titleText() {
				return this.$t('mapSelTitle')
			},
      mapData(){
        return this.mapList ? this.mapList : this.bottomData
      }
		},
		methods: {
			/**
			 * 选择内容
			 */
			select(item, index) {
				this.$emit('select', {
					item,
					index
				})

			},
			/**
			 * 关闭窗口
			 */
			close() {
				this.$emit('close')
			}
		}
	}
</script>
<style lang="scss" scoped>
	.navi-selector {
		background-color: #fff;
		border-top-left-radius: 11px;
		border-top-right-radius: 11px;
    padding:36px 31px 37px;
	}
	.navi-selector-title {
		font-size: 32px;
    color: #000000;
    text-align: center;
    height: 44px;
    line-height: 44px;
    font-weight: bold;
    margin-bottom: 36px;
	}
	.navi-selector-content-item {
		height:104px;
    line-height: 104px;
		font-size: 32px;
    color: #000000;
    text-align: center;
    border-bottom: 1px solid #DDDDDD;
    &:last-child{
      border-bottom: 0;
    }
	}
  .close{
    width:40px;
    height:40px;
    background: url("@/static/home/<USER>") center no-repeat;
    position: absolute;
    right:30px;
    top:38px;
    background-size: contain;
  }
</style>
