"use strict";const store_options=require("../store/options.js");const common_vendor=require("../common/vendor.js");const _sfc_main={data(){return{}},props:{serviceInfo:{type:Object,default(){return null}}},computed:{...common_vendor.mapState(store_options.useOptionsStore,["verType"]),careClass:function(){return this.verType=="care"?"care-con":""},scopeList:function(){if(!this.serviceInfo){return[]}let list=[],list1=[];this.serviceInfo.forEach((element=>{if(element.serviceName&&element.serviceName.length>4){element.both=1;list.push(element)}else{list1.push(element)}}));return list.concat(list1)}},methods:{showServiceModel(){this.$refs.popup.open("center")},closePopup(){this.$refs.popup.close()}}};if(!Array){const _easycom_uni_popup2=common_vendor.resolveComponent("uni-popup");_easycom_uni_popup2()}const _easycom_uni_popup=()=>"../node-modules/npm-scope-dcloudio/uni-ui/lib/uni-popup/uni-popup.js";if(!Math){_easycom_uni_popup()}function _sfc_render(_ctx,_cache,$props,$setup,$data,$options){return{a:common_vendor.t(_ctx.$t("servicHallTitle")),b:common_vendor.f($options.scopeList,((service,k0,i0)=>({a:common_vendor.t(service.serviceName),b:service.serviceCode,c:service.both==1?1:""}))),c:common_vendor.o(((...args)=>$options.closePopup&&$options.closePopup(...args))),d:common_vendor.n($options.careClass),e:common_vendor.p({["border-radius"]:"10px",["background-color"]:"#fff"})}}const Component=common_vendor._export_sfc(_sfc_main,[["render",_sfc_render],["__scopeId","data-v-c87abc3a"]]);my.createComponent(Component);
