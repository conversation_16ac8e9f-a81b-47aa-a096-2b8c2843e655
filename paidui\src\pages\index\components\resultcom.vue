<template>
  <view class="container">
    <view class="img"></view>
    <view class="end-title">
      {{ endTitle }}
    </view>
    <Pickupnumber v-if="cardType=='pickupnumber'" :hall-detail='hallDetail' :hide-title="true"/>
    <Appointment v-if="cardType=='appointment'" :hall-detail='hallDetail' :hide-title="true" @cancleAppointment="cancleAppointment" @queueHandle="queueHandle" />
  </view>
</template>
<script>
import Appointment from "@/pages/index/components/appointment.vue";
import Pickupnumber from "@/pages/index/components/pickupnumber.vue";
import { useUserStore } from '@/store/user'
import { mapState } from 'pinia'
export default {
  props:{
    endType:{
      type:String,
      default:'appointment'
    },
    hallDetail:{
      type:Object,
      default:()=>({})
    },
    cardType:{
      type:String,
      default:'appointment'
    }
  },
  data() {
    return {
    }
  },
  components:{
    Appointment,
    Pickupnumber,
  },
  computed:{
    ...mapState(useUserStore, ['info', 'isLogined','geo']),
    endTitle:function(){
      return this.cardType=='appointment'?this.$t('appointSec'):this.$t('takeSec')
    }
  },
  watch:{
    isLogined: {
      handler(newVal) {
        if(newVal){
          // this.logined()
        }
      },
      immediate: true
    }
  },
  methods: {
    cancleAppointment(){
      this.$emit('chagneTab','cancleAppointment')
    },
    queueHandle(){
      this.$emit('chagneTab','pickupnumberSec')
    }
  }
}
</script>
<style lang="scss" scoped>
.container{
  min-height:calc(100vh - 96px);
  background: url('@/static/home/<USER>') no-repeat;
  background-size: cover;
  padding:30px;
  box-sizing: border-box;
  ::before{
    content: "";
    display: table;
  }
  .img{
    width:265px;
    height:187px;
    background: url('@/static/payend/success.png') no-repeat;
    background-size: contain;
    margin:70px auto 18px;
  }
  .end-title{
    font-size: 36px;
    color: #3D3D3D;
    line-height: 50px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 36px;
  }
}
</style>
