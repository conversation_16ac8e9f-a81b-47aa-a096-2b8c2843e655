<template>
  <view class="tabs" :class="[careClass]">
    <view v-for="item in items" class="tab-item" :class="{'active':item.basetyle==basetyle}" @click="handleChange(item)">
      {{ $t(item.title) }}
    </view>
  </view>
  <view class="record-list" :class="[careClass]">
    <view v-for="items in currentContentInfo" class="record-item">
      <view class="record-item-title">
        <view class="record-item-title-time">
          <view>{{ timeLabel }}</view>
          <view>{{ items.appTime || items.ticketTime}}</view>
        </view>
        <view class="record-status" :class="[items.statusTextColor]">
          {{ items.statusText }}
        </view>
      </view>
      <view class="content">
        <view v-for="item in currentContent" class="content-item">
          <view class="label-con">
            {{ $t(item.label)}}
          </view>
          <view class="value">
            {{ item.getEn && stationEnInfo[items.stationCode] ? stationEnInfo[items.stationCode][item.value] : items[item.value]}}
          </view>
        </view>
      </view>
    </view>
    <view v-if="showEmpty">
      <view class="empty-img"></view>
      <view class="empty-text" >
        {{ $t('noRecord') }}
      </view>
    </view>
  </view>
</template>
<script>
import paiduiApi from "@/api/paidui.js"
import { useUserStore } from '@/store/user'
import { mapState } from 'pinia'
import { useShopInfoStore } from "@/store/shopdetail"
import { APPOINTMENTSTATUS, ENUM_VERTYPE,QUEUESTATUS } from '@/const/index.js'
import { useOptionsStore } from '@/store/options.js'
import { gdpDcsMultiTrack,gdpSetGeneralProps } from "@/utils/gio-track.js"
import yundianApi from "@/api/yundian.js"
const shopInfo = useShopInfoStore()

export default {
  data(){
    return {
      basetyle:"appointment",
      timeLabel:"",
      appointmentContent:[
        {
          label:'appointTime',
          value:"appointmentTime",
        },
        {
          label:'serviceName',
          value:"serviceName",
        },
        {
          label:'stationCode',
          value:"stationName",
        },
      ],
      appointmentContentEn:[
        {
          label:'appointTime',
          value:"appointmentTime",
        },
        {
          label:'stationCode',
          value:"stationName",
          getEn:true
        },
        {
          label:'stationAddress',
          value:"stationAddress",
          getEn:true
        }
      ],
      pickupnumberContent:[
        {
          label:'takeNumber',
          value:"ticketNo",
        },
        {
          label:'stationCode',
          value:"stationName",
          getEn:true
        },
        {
          label:'stationAddress',
          value:"stationAddress",
          getEn:true
        }
      ],
      items: [
        {
          title: 'takeRecord',
          basetyle: 'picknumber',
        },
        {
          title: 'appointRecord',
          basetyle: 'appointment',
        }
      ],
      recordSendData:{
        mobile:null,
        currentPage:1,
        pageSize:10
      },
      currentContentInfo:{},
      currentTips:"",
      finished:false,
      showEmpty:false,
      stationEnInfo:{}
    }
  },
  computed:{
    ...mapState(useUserStore, ['info', 'isLogined']),
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='care'){
        return 'care-con'
      }
      if(this.verType=='en'){
        return 'en-con'
      }
      return  ''
    },
    currentContent:function(){
      if(this.basetyle=='appointment'){
        this.timeLabel = this.$t('reservedTime')
        if(this.verType==='en'){
          return this.appointmentContentEn
        }else{
          return this.appointmentContent
        }
      }
      if(this.basetyle=='picknumber'){
        this.timeLabel = this.$t('takeTime')
        return this.pickupnumberContent
      }
      return []
    }
  },
  onLoad(options){
    this.basetyle = options.basetyle || 'picknumber'
    const optionsStore = useOptionsStore()
    optionsStore.setVerType(options)
    my.setNavigationBar({
      reset: true,
      title: this.$t('mpappTitle'),
    });
    this.getData()
  },
  methods:{
    handleChange(current) {
      this.recordSendData.currentPage = 1
      this.finished = false
      this.showEmpty = false
      this.basetyle = current.basetyle
      this.getData()
    },
    getData(){
      if(this.basetyle=='appointment'){
        this.queryUserAppRecords()
      }
      if(this.basetyle=='picknumber'){
        this.queryUserTakeRecords()
      }
      this.multiTrack()
    },
    /**用户预约记录 */
    async queryUserAppRecords(){
      this.recordSendData.mobile = this.info.msisdn
      paiduiApi.queryUserAppRecords(this.recordSendData).then(async (res)=>{
        console.log("用户预约记录",res)
        if(res.bizCode=='0000' && res.resultData.appList && res.resultData.appList.list){
          if(this.recordSendData.currentPage === 1){
            this.currentContentInfo=[]
          }
          let textStr = this.verType=='en' ? 'TEXTEN' : 'TEXT'
          let stationCodes = [],recordList = res.resultData.appList.list
          recordList.forEach(item=>{
            switch(parseInt(item.appointmentStatus)){
              case APPOINTMENTSTATUS.SUCCESS.CODE:
                item.statusText = APPOINTMENTSTATUS.SUCCESS[textStr]
                break;
              case APPOINTMENTSTATUS.FAIL.CODE:
                item.statusText = APPOINTMENTSTATUS.FAIL[textStr]
                break;
              case APPOINTMENTSTATUS.CANCELED.CODE:
                item.statusText = APPOINTMENTSTATUS.CANCELED[textStr]
                break;
              case APPOINTMENTSTATUS.TICKETED.CODE:
                item.statusText = APPOINTMENTSTATUS.TICKETED[textStr]
                break;
              case APPOINTMENTSTATUS.ABANDONED.CODE:
                item.statusText = APPOINTMENTSTATUS.ABANDONED[textStr]
                break;
            }
            item.statusTextColor = item.appointmentStatus==APPOINTMENTSTATUS.SUCCESS.CODE?'blue':'grey'
            item.appointmentTime = item.appDate+' '+item.startTime+ '-' +item.endTime
            item.address = shopInfo.info.stationAddress
            if(!this.stationEnInfo[item.stationCode]){
              stationCodes.push(item.stationCode)
            }
          })
          if(this.verType==='en' && stationCodes.length>0){
            let enList = await this.queryShopInfo({
              stationCodeList:stationCodes
            })
            enList.forEach(item=>{
              this.stationEnInfo[item.stationCode] = item
            })
          }
          this.currentContentInfo = this.currentContentInfo.concat(recordList)
          if(this.currentContentInfo.length==0){
            this.showEmpty = true
          }
          if(res.resultData.total>res.resultData.pageSize*res.resultData.pageNo){
            this.recordSendData.currentPage++
          }else{
            this.finished = true
          }
        }else{

        }
      })
    },
    /**用户取号记录 */
    queryUserTakeRecords(){
      this.recordSendData.mobile = this.info.msisdn
      paiduiApi.queryUserTakeRecords(this.recordSendData).then(async(res)=>{
        console.log("用户取号记录",res)
        if(res.bizCode=='0000' && res.resultData && res.resultData.list){
          // this.currentContentInfo = res.resultData.list
          if(this.recordSendData.currentPage === 1){
            this.currentContentInfo=[]
          }
          let stationCodes = [],recordList = res.resultData.list
          let textStr = this.verType=='en' ? 'TEXTEN' : 'TEXT'
          recordList.forEach(item=>{
            if(parseInt(item.status)===QUEUESTATUS.CANCELED.CODE){
              item.statusText = QUEUESTATUS.CANCELED[textStr]
              item.statusTextColor = 'grey'
            }
            if(!this.stationEnInfo[item.stationCode]){
              stationCodes.push(item.stationCode)
            }
          })
          if(this.verType==='en' && stationCodes.length>0){
            let enList = await this.queryShopInfo({
              stationCodeList:stationCodes
            })
            enList.forEach(item=>{
              this.stationEnInfo[item.stationCode] = item
            })
          }
          this.currentContentInfo = this.currentContentInfo.concat(recordList)
          if(this.currentContentInfo.length==0){
            this.showEmpty = true
          }
          if(res.resultData.total>res.resultData.pageSize*res.resultData.pageNo){
            this.recordSendData.currentPage++
          }else{
            this.finished = true
          }
        }
      })
    },
     /**插码 */
    async multiTrack(){
      await gdpSetGeneralProps({
        WT_page_type: '排队取号_' + ENUM_VERTYPE[this.verType],
      })
      gdpDcsMultiTrack('pageView',{
        "WT_et" : "pageview",
        "WT_event" : "MPXPageShow",
        "WT_ti" : "我的记录"
      })
    },
    queryShopInfo({stationCodeList}){
      return new Promise((resolve) => {
        yundianApi.queryShopInfo({stationCodeList}).then(res=> {
          if(res && res.data && res.data.shopEnInfoList && res.data.shopEnInfoList.length>0 && this.verType==='en') {
            resolve(res.data.shopEnInfoList)
          }
          resolve([])
        }).catch(()=> {
          resolve([])
        })
      })
    }
  },
  // 页面处理函数--监听用户上拉触底
  onReachBottom() {
    if(!this.finished){
      if(current.basetyle == 'picknumber'){
        this.queryUserTakeRecords()
      }
      if(current.basetyle == 'appointment'){
        this.queryUserAppRecords()
      }
    }
  },
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/home/<USER>";
.record-item{
  width:690px;
  border-radius: 18px;
  box-shadow: 0px 3px 8px 0px rgba(0,0,0,0.16);
  margin:10px auto 24px;
  .blue{
    color:#007EFF;
  }
  .grey{
    color:rgba(0,0,0,0.4);
  }
  .record-item-title{
    height: 64px;
    display: flex;
    align-items: center;
    font-size: 28px;
    line-height: 40px;
    padding:0 30px;
    background: #E0E9FF;
    border-radius: 18px 18px 0px 0px;
    .record-item-title-time{
      flex:1;
      color: rgba(0,0,0,0.6);
      display: flex;
    }
  }
  .content{
    padding:0 30px 30px;
  }
  .content-item{
    align-items: center;
    .label-con{
      min-width: 198px;
      color: rgba(0,0,0,0.6);
    }
  }
}
.tabs{
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  .tab-item{
    font-size: 32px;
    color: #000000;
    line-height: 44px;
    &:first-child{
      margin-right: 150px;
    }
    &.active{
      font-weight: bold;
      position: relative;
      &::after{
        content:"";
        width: 60px;
        height: 6px;
        background: linear-gradient( 270deg, #2892FF 0%, #007EFF 100%);
        border-radius: 90px;
        position: absolute;
        bottom:-10px;
        left:50%;
        transform: translate(-50%,0);
      }
    }
  }
}
.empty-img{
  background: url('@/static/payend/empty.jpg') no-repeat center;
  height:452px;
  background-size: contain;
}
.empty-text{
  text-align: center;
}
.care-con{
  .record-item-title-time,.record-status{
    font-size: 32px!important;
    font-weight: bold;
  }
  .record-item-title-time:last-child{
    line-height: 42px;
  }
  .content-item{
    font-size: 36px!important;
    line-height:54px;
    align-items:baseline;
    .label-con{
      font-weight: bold!important;
    }
    .value{
      font-size: 36px!important;
    }
  }
  .tab-item{
    font-size:36px!important;
  }
}
.en-con{
  .record-item-title .record-item-title-time{
    display: block!important;
  }
  .tab-item{
    text-align: center;
    width:330px;
    margin-right: 0px!important;
    &.active{
      font-weight:bold;
    }
    &:last-child{
      width:280px;
    }
  }
  .record-item{
    margin-top:20px;
    .content-item{
      align-items: flex-start;
    }
    .label-con{
      font-size: 28px;
      width:280px;
    }
  }
  .record-item-title{
    height:80px;
    line-height: 32px;
  }
}
</style>
