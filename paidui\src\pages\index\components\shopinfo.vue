<template>
  <view :class="[careClass]">
    <view class="shop-title">
      <view class="shop-title-left">
        <view class="span-tag-con">
          <view v-if="showTag" class="span-tag">
            {{ $t('nearShop') }}
          </view>
        </view>
        <view class="span-name">
          {{ hallDetail.stationName }}
        </view>
      </view>
      <view v-if="careClass!=='en-con'" class="span-btn-con" @click="goStoreList">
        <view class="span-toggle" >{{ $t('toogle') }}</view>
        <view class="iconfont icon-arrow-icon" @click="goStoreList"></view>
      </view>
    </view>
    <view class="shop-address">
      <view class="iconfont icon-address-icon"></view>
      <view class="span-address">
        {{ hallDetail.stationAddress }}
      </view>
      <view class="span-distance" @click="goToMap(hallDetail)">
        <view class="iconfont icon-navigation-icon"></view>
        <view v-if="careClass==='care-con'" class="" >
          {{ hallDetail.distance }}
        </view>
      </view>
    </view>
    <view class="shop-busi-hour">
      <view class="span">{{ $t('workPeriod') }}：{{ hallDetail.workPeriod }}</view>
      <view class="span">{{ hallDetail.isBusiness? $t('working'): $t('huisinessClose') }}</view>
    </view>
    <view class="shop-busi-scope">
      <view class="label">{{ $t('serviceInfo') }}</view>
      <view class="scope-list" @click="showServiceModel">
        <view v-for="service in hallDetail.serviceInfoShort" :key="service.serviceCode" class="scope-item">
          {{ service.serviceName }}
        </view>
      </view>
      <view v-show="hallDetail.newServiceInfo && hallDetail.newServiceInfo.length>4" class="iconfont icon-arrow-icon" @click="showServiceModel"></view>
    </view>
    <view v-if="showTips" class="shop-tips">
    <!-- <view class="shop-tips"> -->
      <view v-if="careClass!=='en-con'" class="iconfont icon-tips"></view>
      <view class="shop-tips-text">
        {{ $t('tipsDes') }}
      </view>
    </view>
    <Servicepopup ref="serPopup" :service-info="hallDetail.newServiceInfo" />
    <NaviOpenApp ref="naviOpenApp" />
  </view>
</template>
<script>
import Servicepopup from '@/components/servicepopup.vue'
import NaviOpenApp from '@/components/navi-open-app.vue'
import { useOptionsStore } from '@/store/options.js'
import { mapState } from 'pinia'
export default {
  props:{
    hallDetail:{
      type:Object,
      default:()=>({})
    },
    showTag:{
      type:Boolean,
      default:false
    },
    showTips:{
      type:Boolean,
      default:false
    }
  },
  components:{
    Servicepopup,
    NaviOpenApp
  },
  computed:{
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='care'){
        return 'care-con'
      }
      if(this.verType=='en'){
        return 'en-con'
      }
      return  ''
    },
  },
  data(){
    return {}
  },
  methods:{
    goStoreList(){
      this.$emit('gdpTrack','clk','切换营业厅')
      this.$emit('goStoreList')
    },
    showServiceModel(){
      this.$refs.serPopup.showServiceModel()
    },
    goToMap(item){
      this.$emit('gdpTrack','clk','地图导航')
      if(this.verType=='en'){
        this.$refs.naviOpenApp.itemMarkertap(item)
        return false
      }
      let url = `/pages/map/index?stationCode=${item.stationCode}&latitude=${item.latitude}&longitude=${item.longitude}`
      uni.navigateTo({ url: url })
    },

  }
}
</script>
<style lang="scss" scoped>
$blue: #007EFF;
.shop-title{
  display: flex;
  align-items: center;
  height:56px;
  line-height: 56px;
  .shop-title-left{
    display: flex;
    width: 600px;
    align-items: center;
  }
  .span-tag{
    width:120px;
    height:42px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient( 270deg, #FEAB74 0%, #F9382B 100%);
    border-radius: 6px;
    font-size: 24px;
    color: #FFFFFF;
    margin-right: 6px;
  }
  .span-name{
    flex:1;
    color: #000000;
    font-size: 40px;
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .span-btn-con{
    display: flex;
    align-items: center;
  }
  .span-toggle{
    font-size: 24px;
    color: #000;
    margin-right: 6px;
  }
}
.icon-arrow-icon{
  width:30px;
  height:30px;
  background: url("@/static/home/<USER>") center no-repeat;
  background-size: contain;
}
.shop-address{
  display: flex;
  align-items: center;
  font-size: 28px;
  margin-top:25px;
  .span-address{
    flex:1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .span-distance{
    background: #E5F2FF;
    border-radius: 90px;
    font-size: 24px;
    min-width: 80px;
    height:42px;
    display: flex;
    align-items: center;
    color: $blue;
    justify-content: center;
    .icon-navigation-icon{
      width:26px;
      height:26px;
      background: url("@/static/home/<USER>") center no-repeat;
      background-size: contain;
    }
  }
  .icon-address-icon{
    width:18px;
    height:23px;
    background: url("@/static/home/<USER>") center no-repeat;
    background-size: contain;
    margin-right: 4px;
  }
}
.shop-busi-hour{
  margin-top:25px;
  display: flex;
  align-items: center;
  font-size: 26px;
  color: rgba(0,0,0,0.6);
  line-height: 34px;
  .span:last-child{
    height: 36px;
    border-radius: 6px;
    border: 1px solid $blue;
    padding:0 14px;
    font-size: 22px;
    color:$blue;
    font-weight: bold;
    margin-left: 12px;

  }
}
.shop-busi-scope{
  margin-top:30px;
  display: flex;
  align-items: center;
  .label{
    font-size: 24px;
    color: rgba(0,0,0,0.6);
  }
  .scope-list{
    flex:1;
    display: flex;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .scope-item{
      border-radius: 12px;
      font-size: 22px;
      line-height: 34px;
      color: rgba(0,0,0,0.6);
      padding:6px 12px;
      margin-left: 12px;
      background: #fff;
    }
  }
}
.shop-tips{
  min-width:670px;
  height:45px;
  display: flex;
  align-items: center;
  justify-content: center;
  color:#fff;
  font-size: 22px;
  background: linear-gradient( 270deg, #2892FF 0%, $blue 100%);
  border-radius: 90px;
  position: relative;
  margin:27px auto 0;
  .icon-tips{
    font-size: 28px;
  }
  &::before{
    content: "";
    width:16px;
    height:16px;
    background: $blue;
    transform: rotate(45deg);
    position: absolute;
    left:35px;
    top:-6px;
  }
}
.care-con{
  .shop-title{
    height:auto!important;
    min-height:30px;
    align-items: flex-start;
    .shop-title-left{
      display: block;
    }
    .span-tag-con{
      display: inline-block;
      vertical-align: bottom;
      height: 60px;
      // border:2px solid red;
      padding-top:6px;
      box-sizing: border-box;
    }
    .span-tag{
      padding:2px 10px;
      background: linear-gradient(90deg, #FEAB74 1%, #F9382B 100%);
      font-size: 28px;
      float:left;
      // margin-top:6px;
    }
    .span-btn-con{
      width: 125px;
      border-radius: 90px;
      line-height: 61px;
      background: linear-gradient(266deg, #07B7FF 3%, #0380FF 94%);
      padding-left: 20px;
      box-sizing: border-box;
      .span-toggle{
        font-size: 30px;
        color: #fff;
        margin-right: 0px;
      }
      .icon-arrow-icon{
        width:30px;
        height:30px;
        background: url("@/static/home/<USER>") center no-repeat;
        background-size: contain;
      }
    }
  }
  .span-name,.span-address{
    overflow: hidden;
    white-space: normal!important;
    text-overflow: ellipsis;
    display: inline;
    min-height:30px;
  }
  .shop-address{
    font-size: 36px;
    margin-top:32px;
    font-weight: bold;
    align-items: flex-start;
    .span-distance{
      margin-left: 26px;
      font-size: 26px;
      min-width:130px;
      padding:0 10px;
    }
    .icon-address-icon{
      width:26px;
      height:33px;
      background: url("@/static/home/<USER>") center no-repeat;
      background-size: contain;
      margin: 5px 6px 0 0;
    }
  }
  .shop-busi-hour{
    margin-top:30px;
    font-size: 36px;
    line-height: 37px;
    font-weight: bold;
    .span:last-child{
      padding:0 14px;
      font-size: 28px;
      margin-left: 17px;
      height: auto;
      line-height:38px;
    }
  }
  .shop-busi-scope{
    margin-top:34px;
    .label{
      width: auto;
      font-size: 36px;
      font-weight: bold;
    }
    .scope-list{
      .scope-item{
        font-size: 28px;
        line-height: 34px;
        font-weight: bold;
        padding:6px 17px;
      }
    }
  }
  .shop-tips{
    width:690px;
    height:135px;
    font-size: 32px;
    border-radius: 18px;
    margin:30px auto 0;
    line-height: 54px;
    padding:12px 35px;
    box-sizing: border-box;
    align-items: baseline;
    .icon-tips{
      font-size: 32px;
      display: block;
      margin-right: 12px;
    }
    &::before{
      content: "";
      width:20px;
      height:20px;
      left:50px;
      top:-10px;
    }
  }
}
.en-con{
  .shop-tips{
    font-size:20px;
    height:74px;
    width:670px;
  }
  .shop-title{
    flex-wrap: wrap;
    height: auto;
    width: auto;
    .span-tag{
      width:324px;
      background: linear-gradient(90deg, #FEAB74 1%, #F9382B 100%);
      margin-bottom: 10px;
    }
  }
  .shop-title-left{
    width: auto;
    display: block;
  }
  .shop-address{
    align-items: flex-start;
    .icon-address-icon{
      margin-top:4px;
    }
  }
  .span-name,.span-address {
    overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
    flex:577px;
  }
  .shop-tips,.shop-tips::before{
    background: #585C66;
  }
  .shop-tips-text {
    padding: 0px 16px;
    text-indent: 30px;
  }
}
</style>
