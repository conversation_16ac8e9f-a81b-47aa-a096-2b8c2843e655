"use strict";const common_vendor=require("../common/vendor.js");const store_options=require("../store/options.js");const _sfc_main={data(){return{setTime:1e4,canRefresh:true}},computed:{...common_vendor.mapState(store_options.useOptionsStore,["verType"]),careClass:function(){if(this.verType=="care"){return"care-con"}if(this.verType=="en"){return"en-con"}return""}},methods:{refresh(){if(!this.canRefresh){common_vendor.index.showToast({title:this.$t("refreshTip"),duration:2e3});return false}this.$emit("refresh");this.canRefresh=false;setTimeout((()=>{this.canRefresh=true;clearTimeout()}),this.setTime)}}};function _sfc_render(_ctx,_cache,$props,$setup,$data,$options){return{a:common_vendor.t(_ctx.$t("emptyTip")),b:common_vendor.t(_ctx.$t("emptyBtnTip")),c:common_vendor.o(((...args)=>$options.refresh&&$options.refresh(...args))),d:common_vendor.n($options.careClass)}}const Component=common_vendor._export_sfc(_sfc_main,[["render",_sfc_render],["__scopeId","data-v-e28004db"]]);my.createComponent(Component);
