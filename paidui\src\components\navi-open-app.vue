<template>
  <div>
    <uni-popup ref="naviSelector" type="bottom" safeArea backgroundColor="#fff">
      <Navi :map-list="mapListEn" @select="goToNaviApp" @close="close"/>
    </uni-popup>
  </div>
</template>
<script>
import Navi from "@/components/navi.vue"
import { useUserStore } from '@/store/user';
import { useOptionsStore } from '@/store/options.js'
import { mapState } from 'pinia'
import cmcc from "@/sdk/cmcc.js"
export default {
  data() {
    return {
      naviTarget: null,
      mapListEn:[
        {
          text: 'Amap',
          name: 'amap',
          textName:'高德导航'
        },
        {
          text: 'Baidu map',
          name: 'baidu',
          textName:'百度导航'
        },
        {
          text: 'Tencent Maps',
          name: 'tencent',
          textName:'腾讯导航'
        },
        {
          text: 'Apple Maps',
          name: 'apple',
          textName:'苹果导航'
        },
      ]
    }
  },
  components: {
    Navi
  },
  computed:{
    ...mapState(useUserStore, ['geo']),
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='en'){
        return 'en-con'
      }
      if(this.verType=='care'){
        return 'care-con'
      }
      return ''

    }
  },
  methods: {
    itemMarkertap(item){
      this.naviTarget = item
			this.$refs.naviSelector.open()
    },
    goToNaviApp({item}) {
      cmcc.openNaviApp({
        app: item.name,
        address: this.naviTarget.stationAddress,
        latitude : this.naviTarget.latitude,
        longitude: this.naviTarget.longitude,
        name:this.naviTarget.stationName
      }).catch(e => {
        uni.showToast({
          title:this.$t('mapErrorTip'),
          duration:2000
        })
      })
    },
    close(){
      this.$refs.naviSelector.close()
    }
  }
}
</script>
