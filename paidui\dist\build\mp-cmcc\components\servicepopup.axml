<uni-popup class="data-v-c87abc3a" u-s="{{['d']}}" ref="__r" u-r="popup" u-i="c87abc3a-0" onVI="__l" u-p="{{e}}"><view class="{{('popup-container') + ' ' + 'data-v-c87abc3a' + ' ' + d}}"><view class="popup-title data-v-c87abc3a">{{a}}</view><view class="scope-list data-v-c87abc3a"><view a:for="{{b}}" a:for-item="service" a:key="b" class="{{('scope-item') + ' ' + 'data-v-c87abc3a' + ' ' + (service.c && 'scope-item-both')}}">{{service.a}}</view></view><view class="close data-v-c87abc3a" onTap="{{c}}"></view></view></uni-popup>