"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_paidui = require("../../../api/paidui.js");
const store_options = require("../../../store/options.js");
const store_user = require("../../../store/user.js");
const const_index = require("../../../const/index.js");
const BaseInfo = () => "./baseinfo.js";
const user = store_user.useUserStore();
const _sfc_main = {
  components: {
    BaseInfo
  },
  props: {
    hideTitle: {
      type: Boolean,
      default: false
    },
    hallDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {};
  },
  computed: {
    ...common_vendor.mapState(store_options.useOptionsStore, ["verType"]),
    careClass: function() {
      if (this.verType == "care") {
        return "care-con";
      }
      if (this.verType == "en") {
        return "en-con";
      }
      return "";
    }
  },
  methods: {
    cancelAppointment() {
      let data = {
        mobile: user.info.msisdn,
        //手机号
        appNo: this.hallDetail.appNo,
        //预约序列号
        stationCode: this.hallDetail.stationCode
        //店铺编码
      };
      api_paidui.paiduiApi.cancelAppointment(data).then((res) => {
        if (res.bizCode === "0000") {
          this.$emit("cancleAppointment");
          this.$emit("chagneTab", "cancleAppointment");
        }
      });
    },
    /**立即取号 */
    userTakeNumber() {
      let data = {
        stationCode: this.hallDetail.stationCode,
        //  营业厅编码
        takeType: 3,
        // 1代表现场扫码取号；2代表在线实时取号，3代表签到取号
        mobile: user.info.msisdn,
        // 手机号
        serviceCode: "H",
        // 业务类型编码
        carrierOperator: "002",
        // 异网标识 001中国电信，002中国移动，003中国联通
        takeChannel: 1
        // 预约渠道 预约渠道：1为一级手厅,2为一级微厅（公众号）,3为二级手厅,4为二级微厅（公众号）,5为小程序渠道
      };
      if (this.verType == "en") {
        data.queueVersion = "en";
        data.enStationName = this.hallDetail.stationName;
        data.enStationAddress = this.hallDetail.stationAddress;
      }
      console.log("立即取号参数", data, this.hallDetail);
      api_paidui.paiduiApi.userTakeNumber(data, this.hallDetail.provinceCode).then((res) => {
        console.log("签到取号结果", res);
        if (res.bizCode == "0000") {
          this.$emit("queueHandle", "cancleAppointment");
          this.$emit("chagneTab", "pickupnumberSec");
        } else {
          common_vendor.index.showToast({
            title: this.getErrorMsg(res.bizDesc)
          });
        }
      });
    },
    getErrorMsg(msg) {
      if (this.verType == "en") {
        return const_index.ERRORINFO[msg] || "The system is busy. Please try again later";
      }
      return msg;
    },
    toAppRecord() {
      let url = "/pages/recordlist/index?basetyle=appointment";
      common_vendor.index.navigateTo({ url });
    }
  }
};
if (!Array) {
  const _component_BaseInfo = common_vendor.resolveComponent("BaseInfo");
  _component_BaseInfo();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$props.hideTitle
  }, !$props.hideTitle ? {
    b: common_vendor.t(_ctx.$t("appoint")),
    c: common_vendor.t(_ctx.$t("appointRecord")),
    d: common_vendor.o(($event) => $options.toAppRecord()),
    e: common_vendor.n($options.careClass == "care-con" ? "icon-arrow-blue" : "icon-arrow-blue-little"),
    f: common_vendor.o(($event) => $options.toAppRecord())
  } : {}, {
    g: common_vendor.p({
      basetyle: "appointment",
      ["hall-detail"]: $props.hallDetail
    }),
    h: !$props.hideTitle
  }, !$props.hideTitle ? {
    i: common_vendor.t(_ctx.$t("wxAppointCon1Tip")),
    j: common_vendor.t(_ctx.$t("wxAppointCon2Tip"))
  } : {}, {
    k: common_vendor.t(_ctx.$t("cancelAppoint")),
    l: common_vendor.o(($event) => $options.cancelAppointment()),
    m: common_vendor.t(_ctx.$t("signTake")),
    n: common_vendor.o(($event) => $options.userTakeNumber()),
    o: $props.hideTitle
  }, $props.hideTitle ? {
    p: common_vendor.t(_ctx.$t("wxTip")),
    q: common_vendor.t(_ctx.$t("wxAppointSec1Tip")),
    r: common_vendor.t(_ctx.$t("wxAppointSec2Tip"))
  } : {}, {
    s: common_vendor.n($options.careClass)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b8440f9a"]]);
my.createComponent(Component);
