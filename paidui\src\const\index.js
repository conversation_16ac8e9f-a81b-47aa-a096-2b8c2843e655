/**0成功 1失败 2已取消 3 已出票 4 已废弃 */

export const APPOINTMENTSTATUS = {
  SUCCESS:{
    CODE:0,
    TEXT:"预约成功",
    TEXTEN:"Reserve Success",
  },
  FAIL:{
    CODE:1,
    TEXT:"预约失败",
    TEXTEN:"Appointment Failed",
  },
  CANCELED:{
    CODE:2,
    TEXT:"已取消",
    TEXTEN:"Cancelled",
  },
  TICKETED:{
    CODE:3,
    TEXT:"已出票",
    TEXTEN:"Appointment Signed-in",
  },
  ABANDONED:{
    CODE:4,
    TEXT:"已废弃",
    TEXTEN:"Appointment Missed",
  }
}

/**1代表已取号，2代表已叫号，3代表已办理，4代表已废弃，5代表失败，6代表已过期，7代表超时未处理,8已取消 */

export const QUEUESTATUS = {
  CANCELED:{
    CODE:8,
    TEXT:"已取消",
    TEXTEN:"Cancelled",
  }
}

export const ENUM_VERTYPE = {
  cn:'普通版',
  en:'英文版',
  care:'适老版'
}
/**业务类型中转英 */
export const QUEUEINFO = {
  "普通用户": "Regular Users",
  "VIP用户": "VIP Users",
  "预约用户": "Reserved Users",
  "过户/挂失/解挂/销号复装/停复机":"Ownership Transfering/Reporting the loss/Relieve the Loss Reporting/Reactivate the Cancelled Number/Service Suspension and Resumption",
  "销户队列": "Queue for Account Cancellation",
  "军警队列": "Queue for Military and Police",
  "咨询": "Consulation",
  "靓号业务(补卡/过户/入网)": "Good Mobile Number Services(SIM Card Replacement /Ownership Transfering/Mobile Access)",
  "宽带拆机业务": "Broadband Dismantling Services",
  "跨省业务(补换卡/销号)": "Interprovincial Services( SIM Card Replacement / Account Cancellation)",
  "代办补卡/证件消磁业务": "Agent-assisted SIM Card Replacement/ ID Card Demagnetization",
  "宽带销户/实名登记": "Broadband Account Cancellation / Real-name Registration",
  "军人用户": "Military Users",
  "老人优先服务": "Priority Services for Seniors",
  "老人优先": "Priority for Seniors",
  "老年人优先服务": "Priority Services for Seniors",
  "老人队列": "Queue for Seniors",
  "老年人优先服务用户": "Senior Users with Priority Services",
  "拥军队列": "Queue for Veterans",
  "再次实人验证开机": "Resume Mobile Service after Secondary Real-person Verification",
  // "老人优先服务": "Priority Services for Seniors",
  "增值税发票":"Value-Added Tax Invoice",
  "测试湖南":"Test for Hunan",
  "携入专用(测试)":"Port-in Only(For Test)",
  "全球通业务":"GoTone Services",
  "普通业务":"Regular Services",
  "风险复机业务":"Risk Number Reactivation Services",
  "解约业务":"Contract Termination Services",
  "军人警察队列":"Military and Police Queue",
  "家客关怀":"Family Customer Care",
  "个客关怀":"Individual Customer Care",
  "VIP队列":"VIP Queue",
  "预约队列":"Appointment Queue",
  "普通队列":"General Queue"
}
/**业务类型 */
export const QUEUEINFOCN = [
  {queueName:"普通用户",queueNameEN:"Regular Users"},
  {queueName:"VIP用户",queueNameEN:"VIP Users"},
  {queueName:"预约用户",queueNameEN:"Reserved Users"},
  {queueName:"销户队列",queueNameEN:"Queue for Account Cancellation"},
  {queueName:"军警队列",queueNameEN:"Queue for Military and Police"},
  {queueName:"咨询",queueNameEN:"Consulation"},
  {queueName:"靓号业务(补卡/过户/入网)",queueNameEN:"Good Mobile Number Services(SIM Card Replacement /Ownership Transfering/Mobile Access)"},
  {queueName:"宽带拆机业务",queueNameEN:"Broadband Dismantling Services"},
  {queueName:"跨省业务(补换卡/销号)",queueNameEN:"Interprovincial Services( SIM Card Replacement / Account Cancellation)"},
  {queueName:"代办补卡/证件消磁业务",queueNameEN:"Agent-assisted SIM Card Replacement/ ID Card Demagnetization"},
  {queueName:"宽带销户/实名登记",queueNameEN:"Broadband Account Cancellation / Real-name Registration"},
  {queueName:"军人用户",queueNameEN:"Military Users"},
  {queueName:"老人优先服务",queueNameEN:"Priority Services for Seniors"},
  {queueName:"老人优先",queueNameEN:"Priority for Seniors"},
  {queueName:"老年人优先服务",queueNameEN:"Priority Services for Seniors"},
  {queueName:"老人队列",queueNameEN:"Queue for Seniors"},
  {queueName:"老年人优先服务用户",queueNameEN:"Senior Users with Priority Services"},
  {queueName:"拥军队列",queueNameEN:"Queue for Veterans"},
  {queueName:"再次实人验证开机",queueNameEN:"Resume Mobile Service after Secondary Real-person Verification"},
  {queueName:"老人优先服务",queueNameEN:"Priority Services for Seniors"},
  {queueName:"增值税发票",queueNameEN:"Value-Added Tax Invoice"},
  {queueName:"测试湖南",queueNameEN:"Test for Hunan"},
  {queueName:"携入专用(测试)",queueNameEN:"Port-in Only(For Test)"},
  {queueName:"全球通业务",queueNameEN:"GoTone Services"},
  {queueName:"普通业务",queueNameEN:"Regular Services"},
  {queueName:"风险复机业务",queueNameEN:"Risk Number Reactivation Services"},
  {queueName:"解约业务",queueNameEN:"Contract Termination Services"},
  {queueName:"军人警察队列",queueNameEN:"Military and Police Queue"},
  {queueName:"家客关怀",queueNameEN:"Family Customer Care"},
  {queueName:"个客关怀",queueNameEN:"Individual Customer Care"},
  {queueName:"VIP队列",queueNameEN:"VIP Queue"},
  {queueName:"预约队列",queueNameEN:"Appointment Queue"},
  {queueName:"普通队列",queueNameEN:"General Queue"}
]
/**错误提示翻译 */
export const ERRORINFO = {
  "预约信息不存在或已过期":"The appointment information does not exist or has expired.",
  "您已预约,请勿重复预约":"You have already made an appointment. Please do not make another one.",
  "预约人数已满,请选择其他时段！":"No slots available. Please choose another time!",
  "手机号格式不符":"Invalid Phone Number Format",
  "查询不到今天该服务号码的预约资料,请核对预约信息！":"No appointment records found for the service number today. Please confirm your appointment details!",
  "最早可在预约时间段前30分钟签到！":"You can sign in no earlier than 30 minutes before your scheduled appointment time",
  "您已超出当天最大预约取号操作次数“x”次！":"You've hit the daily cap of 'x' appointments!",
  "您已取号,业务办理中,请耐心等待!":"You have taken a number, please wait patiently for your turn to be processed!",
  "您已取号,待叫号,请耐心等待!":"You have taken a number, please wait patiently for your turn to be processed!",
  "查询无记录":"No Record Found",
  "验证码错误":"Incorrect Verification Code",
  "手机号码不正确！":"Incorrect phone number!",
  "异网用户不能发送验证码":"Non-China Mobile users cannot receive verification codes.",
  "营业厅信息维护中,暂不能取号":"Number issuance is temporarily unavailable due to the maintenance of our business hall system.",
  "该厅店未开通线上预约取号功能,请直接到厅店取号":"Online appointment pickup is not available at this business hall. Please visit the hall directly to get a number.",
  "营业厅当前已停止取号":"The business hall has stopped issuing numbers.",
  "营业厅已停用":"The business hall is currently closed.",
  "营业厅为测试状态,无法取号":"The business hall is in a test status and number issuance is unavailable.",
  "预约逾期无法进行签到,请珍惜预约名额,按时签到！":"Sign-in is not allowed after the appointment time. Please make sure to sign in on time!",
  "取号失败":"Number Acquisition Failed",
  "签到失败":"Sign-in Failed",
}
export default {
  APPOINTMENTSTATUS,
  QUEUESTATUS,
  ENUM_VERTYPE,
  QUEUEINFO,
  ERRORINFO
}
