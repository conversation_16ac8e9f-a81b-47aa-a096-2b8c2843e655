"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "NaviSelector",
  emits: ["select", "close"],
  props: {
    title: {
      type: String,
      default: "导航到目的地"
    },
    cancel: {
      type: String,
      default: "取消"
    },
    mapList: {
      type: Array,
      default: () => null
    }
  },
  data() {
    return {
      bottomData: [
        {
          text: "高德导航",
          name: "amap"
        },
        {
          text: "百度导航",
          name: "baidu"
        }
      ]
    };
  },
  created() {
  },
  computed: {
    cancelText() {
      return this.cancel;
    },
    titleText() {
      return this.$t("mapSelTitle");
    },
    mapData() {
      return this.mapList ? this.mapList : this.bottomData;
    }
  },
  methods: {
    /**
     * 选择内容
     */
    select(item, index) {
      this.$emit("select", {
        item,
        index
      });
    },
    /**
     * 关闭窗口
     */
    close() {
      this.$emit("close");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($options.titleText),
    b: common_vendor.f($options.mapData, (item, index, i0) => {
      return {
        a: common_vendor.t(item.text),
        b: index,
        c: common_vendor.o(($event) => $options.select(item, index))
      };
    }),
    c: common_vendor.o((...args) => $options.close && $options.close(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2a6d8a09"]]);
my.createComponent(Component);
