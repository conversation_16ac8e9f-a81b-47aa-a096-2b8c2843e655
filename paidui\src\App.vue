<script>
import login from "@/utils/login.js"
import { useUserStore } from "@/store/user.js"
export default {
  onLaunch: async function () {
    console.log('App Launch')
  },
  onShow: async function () {
    console.log('App Show')
    const user = useUserStore()
    if(!user.isLogined) {
      await login.forceLogin()
    }
    return true
  },
  onHide: function () {
    console.log('App Hide')
  },
}
</script>

<style>
/*每个页面公共css */
page{
  background-color: #fff;
}
</style>
