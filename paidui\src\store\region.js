import { defineStore } from 'pinia'

const regionTmp = uni.getStorageSync('region') || {}

export const useRegionStore = defineStore('region', {
  state: () => {
    return {
      info: regionTmp.list || [],
      timestamp: regionTmp.timestamp || new Date().valueOf()
    }
  },
  actions: {
    setRegion(region) {
      this.info = region
      uni.setStorage({
        key: 'region',
        data: {
          list: region,
          timestamp: new Date().valueOf()
        },
        success: () => {}
      })
    }
  }
})
export default {
  useRegionStore
}
