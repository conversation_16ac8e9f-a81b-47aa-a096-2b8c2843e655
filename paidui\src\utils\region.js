import { getCityInfo } from '@/api/paidui.js'
import { useRegionStore } from '@/store/region.js'

export function initRegion(){
  const region = useRegionStore()
  if(region.info && region.info.length > 0){
    if(new Date().valueOf() - region.timestamp < 1000 * 60 * 60 * 24){
      return region.info
    }
  }
  getCityInfo().then(res=> {
    if(res.bizCode === '0000'){
      region.setRegion(res.resultData)
      return region.info
    } else {
      return []
    }
  }).catch(() => {
    console.log('请求异常', 'getCityInfo')
    return []
  })
}

export default {
  getCityInfo
}
