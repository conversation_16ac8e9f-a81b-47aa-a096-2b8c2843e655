import CryptoJS from "crypto-js"

const rsaKey = `MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI6lqRhSuXe7n2z4he+fMakiR94dJfoygPOn2uOyCL252AkV+2PSoXOYsuA/cMjQ7rboW6aWwrHoVOLme0rfw08CAwEAAQ==`

/**n加密**/
const __secretKey = "nnrE33BmyLw6JVjeTFGrNeFaPkA3aO8KZggeQHTYSls="
let __aeskey

let __appkey
export function encryptRsa(text,key) {
  return new Promise((resolve, reject) => {
    my.rsa({
      action: 'encrypt',
      text,
      // 设置公钥，需替换你自己的公钥
      key: key || rsaKey,
      success: result => {
        resolve(result.text)
      },
      fail(error) {
        reject(error)
      },
    })
  })
}

export function encryptAes(word,key = getAppKey()){
  const keyHex = CryptoJS.enc.Utf8.parse(key)
  let srcs = ''
  if (typeof word == "string") {
    srcs = CryptoJS.enc.Utf8.parse(word)
  } else if (typeof word == "object") {
    //对象格式的转成json字符串
    srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(word))
  }
  return CryptoJS.AES.encrypt(srcs, keyHex, {
    // iv: iv,
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  }).toString()
}
export function decryptAes(word,key = getAppKey()){
  const keyHex = CryptoJS.enc.Utf8.parse(key)
  let decrypted = CryptoJS.AES.decrypt(word, keyHex, {
    // iv: iv,
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })

  return decrypted.toString(CryptoJS.enc.Utf8)
}
function randomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}
function randomNumArray(min, max, bitNumber) {
  let result = []
  for (let i = 0; i < bitNumber; i++) {
    result.push(randomNumber(min, max))
  }
  // 默认字母小写，手动转大写
  return result
}
export function getRandomAesKey(){
  return randomNumArray(0, 15, 16).map(item=>{return item.toString(16)}).join('')
}
export function getPaiduiKey(){
  // if(!__aeskey){
  //   console.log(my.getAppIdSync().appId)
  //   __aeskey = decryptAes(__secretKey, my.getAppIdSync().appId.toString())
  // }
  // return __aeskey
  return getRandomAesKey()
}
export function getAppKey(){
  if(!__appkey) {
    __appkey =  my.getAppIdSync().appId.toString()
  }
  return __appkey
}
export default {
  encryptRsa,
  encryptAes,
  decryptAes,
  getRandomAesKey,
  getPaiduiKey
}
