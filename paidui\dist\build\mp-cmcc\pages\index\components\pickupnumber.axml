<view class="{{('card') + ' ' + 'data-v-88233ba9' + ' ' + r}}"><view a:if="{{a}}" class="title data-v-88233ba9"><view class="title-name data-v-88233ba9">{{b}}</view><view class="title-btn data-v-88233ba9" onTap="{{d}}">{{c}}</view><view class="{{('data-v-88233ba9') + ' ' + e}}" onTap="{{f}}"></view></view><view class="content data-v-88233ba9"><view class="number-box data-v-88233ba9"><view class="number-box-title data-v-88233ba9">{{g}}</view><view class="number-box-number data-v-88233ba9">{{h}}</view><view class="number-box-wait data-v-88233ba9"><view class="data-v-88233ba9">{{i}}  </view><view class="wait-number data-v-88233ba9">{{j}}</view></view><view class="btn-item data-v-88233ba9" catchTap="{{l}}">{{k}}</view></view><base-info class="data-v-88233ba9" onGetWaitCount="{{m}}" u-i="88233ba9-0" onVI="__l" u-p="{{n}}"/><view class="tips-title data-v-88233ba9">{{o}}</view><view class="tips-content data-v-88233ba9">{{p}}</view></view><tips class="data-v-88233ba9" ref="__r" u-r="tipsPopup" onCancelQueuing="{{q}}" u-i="88233ba9-1" onVI="__l"/><view class="card-footer data-v-88233ba9"></view></view>