<view class="{{('tabs') + ' ' + 'data-v-3d032b00' + ' ' + b}}"><view a:for="{{a}}" a:for-item="item" class="{{('tab-item') + ' ' + 'data-v-3d032b00' + ' ' + (item.b && 'active')}}" onTap="{{item.c}}">{{item.a}}</view></view><view class="{{('record-list') + ' ' + 'data-v-3d032b00' + ' ' + g}}"><view a:for="{{c}}" a:for-item="items" class="record-item data-v-3d032b00"><view class="record-item-title data-v-3d032b00"><view class="record-item-title-time data-v-3d032b00"><view class="data-v-3d032b00">{{d}}</view><view class="data-v-3d032b00">{{items.a}}</view></view><view class="{{('record-status') + ' ' + 'data-v-3d032b00' + ' ' + items.c}}">{{items.b}}</view></view><view class="content data-v-3d032b00"><view a:for="{{items.d}}" a:for-item="item" class="content-item data-v-3d032b00"><view class="label-con data-v-3d032b00">{{item.a}}</view><view class="value data-v-3d032b00">{{item.b}}</view></view></view></view><view a:if="{{e}}" class="data-v-3d032b00"><view class="empty-img data-v-3d032b00"></view><view class="empty-text data-v-3d032b00">{{f}}</view></view></view>