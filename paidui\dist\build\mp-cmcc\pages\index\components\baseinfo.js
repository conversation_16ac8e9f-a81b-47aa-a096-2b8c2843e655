"use strict";const api_paidui=require("../../../api/paidui.js");const store_user=require("../../../store/user.js");const store_options=require("../../../store/options.js");const common_vendor=require("../../../common/vendor.js");const const_index=require("../../../const/index.js");const user=store_user.useUserStore();const _sfc_main={props:{basetyle:{type:String,default:""},hallDetail:{type:Object,default:()=>({})}},data(){return{appointmentContent:[{label:"appointNumber",value:"number"},{label:"stationCode",value:"stationName"},{label:"stationAddress",value:"stationAddress"},{label:"appointTime",value:"appHandleDate"}],pickupnumberContent:[{label:"takeTime",value:"ticketTime"},{label:"stationCode",value:"stationName"},{label:"stationAddress",value:"stationAddress"}],isRotate:false,activeDetail:false,queueInfo:{}}},computed:{currentContent:function(){if(this.basetyle=="appointment"){let newObj={};this.appointmentContent.forEach((item=>{let label=this.$t(item.label);newObj[label]=this.hallDetail[item.value]}));return newObj}if(this.basetyle=="picknumber"){let newObj={};this.pickupnumberContent.forEach((item=>{let label=this.$t(item.label);newObj[label]=this.hallDetail[item.value]}));return newObj}return[]},...common_vendor.mapState(store_options.useOptionsStore,["verType"]),careClass:function(){if(this.verType=="care"){return"care-con"}if(this.verType=="en"){return"en-con"}return""}},mounted(){this.replay()},methods:{tabDetail(){this.activeDetail=!this.activeDetail;if(this.activeDetail){this.replay()}},async replay(){const result=await this.getQueueArr();if(result){this.$emit("replay")}},async getQueueArr(){if(this.isRotate)return false;const params={stationCode:this.hallDetail.stationCode,mobile:user.info.msisdn};this.starRotate();return api_paidui.paiduiApi.queryHallQueueInfo(params).then((res=>{if(res.bizCode=="0000"){this.stopRotate();this.queueInfo=res.resultData.queueInfo;let setNum=0;this.queueInfo.forEach((item=>{setNum+=parseInt(item.queueNum);if(this.verType=="en"){if(const_index.QUEUEINFO[item.queueName]){item.queueName=const_index.QUEUEINFO[item.queueName]}}}));this.$emit("getWaitCount",{isNew:true,waitCount:setNum});return true}else{this.stopRotate()}})).catch((e=>{this.stopRotate()}))},starRotate(){this.isRotate=true;const timer=setTimeout((()=>{if(timer){clearTimeout(timer)}this.isRotate=false}),2e4)},stopRotate(){const timer=setTimeout((()=>{if(timer){clearTimeout(timer)}this.isRotate=false}),1e3)}}};function _sfc_render(_ctx,_cache,$props,$setup,$data,$options){return common_vendor.e({a:common_vendor.f($options.currentContent,((item,key,i0)=>({a:common_vendor.t(key),b:common_vendor.t(item)}))),b:common_vendor.n($options.careClass),c:common_vendor.t(_ctx.$t("waitDetail")),d:$data.activeDetail?1:"",e:common_vendor.o(((...args)=>$options.tabDetail&&$options.tabDetail(...args))),f:common_vendor.n($options.careClass),g:$data.activeDetail},$data.activeDetail?{h:common_vendor.t(_ctx.$t("waitNum")),i:$data.isRotate?1:"",j:common_vendor.o(((...args)=>$options.replay&&$options.replay(...args))),k:common_vendor.f($data.queueInfo,((item,k0,i0)=>({a:common_vendor.t(item.queueName),b:common_vendor.t(item.queueNum||0)}))),l:common_vendor.n($options.careClass)}:{})}const Component=common_vendor._export_sfc(_sfc_main,[["render",_sfc_render],["__scopeId","data-v-d95251e7"]]);my.createComponent(Component);
