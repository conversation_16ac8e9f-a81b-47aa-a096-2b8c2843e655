"use strict";
const store_options = require("../store/options.js");
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  props: {
    serviceInfo: {
      type: Object,
      default() {
        return null;
      }
    }
  },
  computed: {
    ...common_vendor.mapState(store_options.useOptionsStore, ["verType"]),
    careClass: function() {
      if (this.verType == "care") {
        return "care-con";
      }
      if (this.verType == "en") {
        return "en-con";
      }
      return "";
    }
  },
  methods: {
    showTips() {
      this.$refs.popup.open("center");
    },
    closePopup() {
      this.$refs.popup.close();
    },
    confirmPopup() {
      this.$emit("cancelQueuing");
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../node-modules/npm-scope-dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t(_ctx.$t("wxTip1")),
    b: common_vendor.t(_ctx.$t("wxCancelTip")),
    c: common_vendor.t(_ctx.$t("cancelBtn")),
    d: common_vendor.o((...args) => $options.closePopup && $options.closePopup(...args)),
    e: common_vendor.t(_ctx.$t("confirmBtn1")),
    f: common_vendor.o((...args) => $options.confirmPopup && $options.confirmPopup(...args)),
    g: common_vendor.n($options.careClass),
    h: common_vendor.p({
      ["border-radius"]: "10px",
      ["background-color"]: "#fff"
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-324e86e6"]]);
my.createComponent(Component);
