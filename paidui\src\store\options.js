import { defineStore } from 'pinia'
import { ENUM_VERTYPE } from '@/const/index.js'
const verType = uni.getStorageSync('verType') || 'cn'
function setLocale(verType) {
  const $pages = getCurrentPages()
  const $page = $pages[$pages.length - 1]
  if($page){
    const $vm = $page.$vm
    $vm.$set($vm.$i18n, 'locale', verType)
  }
}
export const useOptionsStore = defineStore('options', {
  state: () => {
    return {
      verType: verType
    }
  },
  actions: {
    setVerType(options={}) {
      if(!options.verType){
        setLocale(this.verType)
        return false
      }
      this.verType = options.verType
      if(this.verType=='care'){
        this.careClass = 'care-con'
      }
      if (!ENUM_VERTYPE[this.verType]) {
        this.verType = 'cn'
      }
      setLocale(this.verType)
      uni.setStorage({
        key: 'verType',
        data: this.verType,
        success: () => {}
      })
    }
  }
})
export default {
  useOptionsStore
}
