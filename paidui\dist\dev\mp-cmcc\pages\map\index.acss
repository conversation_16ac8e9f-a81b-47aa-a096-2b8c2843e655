/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.shop-container.data-v-ebfe0c06 {
  width: 750rpx;
  position: fixed;
  bottom: 0;
  top: 750rpx;
  overflow-y: scroll;
  background-color: #fff;
}
.search-con.data-v-ebfe0c06 {
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 690rpx;
  margin: 0 auto;
}
.search-con .toogle-city.data-v-ebfe0c06 {
  font-size: 28rpx;
  color: #000;
}
.search-con .toogle-city .icon-arrow1.data-v-ebfe0c06 {
  transform: rotate(90deg);
  margin-right: 21rpx;
}
.search-con .search-bar.data-v-ebfe0c06 {
  height: 60rpx;
  background: #f2f2f2;
  border-radius: 30rpx;
  align-items: center;
  position: relative;
  flex: 1;
}
.search-con .search-bar .icon-search-icon.data-v-ebfe0c06 {
  width: 33rpx;
  height: 33rpx;
  background: url("../../static/home/<USER>") center no-repeat;
  background-size: contain;
  position: absolute;
  left: 18rpx;
  top: 14rpx;
}
.search-con .search-bar input.data-v-ebfe0c06 {
  background: transparent;
  font-size: 24rpx;
  color: #000;
  padding-left: 60rpx;
  height: 60rpx;
  box-sizing: border-box;
  width: 95%;
}
.care-con input.data-v-ebfe0c06 {
  font-size: 36rpx !important;
  font-weight: bold !important;
}
.search-con.data-v-ebfe0c06 {
  position: fixed;
  top: 20rpx;
  padding: 0 30rpx;
  background-color: transparent;
}
.search-con .search-bar.data-v-ebfe0c06 {
  background-color: #fff;
}