"use strict";const common_vendor=require("../../../common/vendor.js");const store_user=require("../../../store/user.js");const store_options=require("../../../store/options.js");const Servicepopup=()=>"../../../components/servicepopup.js";const _sfc_main={data(){return{}},props:{item:{type:Object,default(){return null}},isMap:{type:Boolean,default:false},maxNumLength:{type:Number,default:3}},components:{Servicepopup:Servicepopup},computed:{...common_vendor.mapState(store_user.useUserStore,["geo"]),...common_vendor.mapState(store_options.useOptionsStore,["verType"]),careClass:function(){if(this.verType=="en"){return"en-con"}if(this.verType=="care"){return"care-con"}return""}},methods:{goPickupnumber(item,isDisabled){common_vendor.index.navigateTo({url:`/pages/index/index?stationCode=${item.stationCode}&provinceCode=${item.provinceCode}&cityCode=${item.cityCode}`})},showServiceModel(item){if(item.newServiceInfo&&item.newServiceInfo.length>4){this.$refs.serPopup.showServiceModel()}else{this.goToShop(item)}},goToMap(item){common_vendor.index.navigateTo({url:`/pages/map/index?stationCode=${item.stationCode}&latitude=${item.latitude}&longitude=${item.longitude}`})},goNavi(item){this.$emit("goNavi",item)}}};function _sfc_render(_ctx,_cache,$props,$setup,$data,$options){return common_vendor.e({a:$props.item&&$props.item.stationCode},$props.item&&$props.item.stationCode?{b:common_vendor.t($props.item.stationName),c:common_vendor.t(_ctx.$t("workPeriod")),d:common_vendor.t($props.item.workPeriod),e:common_vendor.o(($event=>$options.goNavi($props.item))),f:common_vendor.t($props.item.stationAddress),g:common_vendor.t(_ctx.$t("apptake")),h:$props.item.isAppointment==0?1:"",i:common_vendor.o(($event=>$options.goPickupnumber($props.item,$props.item.isAppointment==0))),j:common_vendor.n($options.careClass)}:{})}const Component=common_vendor._export_sfc(_sfc_main,[["render",_sfc_render],["__scopeId","data-v-9c7e7a87"]]);my.createComponent(Component);
