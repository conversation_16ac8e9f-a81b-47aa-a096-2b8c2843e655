"use strict";
const utils_request = require("../utils/request.js");
const utils_utils = require("../utils/utils.js");
const utils_login = require("../utils/login.js");
const utils_transitEncrypt = require("../utils/transit-encrypt.js");
const store_user = require("../store/user.js");
let ORIGIN, BASEURL;
ORIGIN = "https://aibizhallts.it.10086.cn:31017";
BASEURL = "https://aibizhallts.it.10086.cn:31017/paidui";
function handleData(apiCode, bizData, provinceCode) {
  const user = store_user.useUserStore();
  const params = {
    encryptData: {
      basicData: {
        "provinceCode": provinceCode || user.geo.province || "",
        // store.state.commonData.provinceCode ||
        "timeStamp": utils_utils.parseTime(/* @__PURE__ */ new Date(), "{y}{m}{d}{h}{i}{s}"),
        "signFlag": "0",
        "apiCode": apiCode,
        "messageId": utils_utils.getUUID(),
        "sysCode": "01"
      },
      bizData
    }
  };
  return params;
}
async function _request(url, data, method = "GET", config = {}) {
  const aeskey = utils_transitEncrypt.transitEncrypt.getPaiduiKey();
  let _data = data;
  if (method === "POST" && (data == null ? void 0 : data.encryptData)) {
    _data = {
      auth: await utils_transitEncrypt.transitEncrypt.encryptRsa(aeskey),
      encryptData: utils_transitEncrypt.transitEncrypt.encryptAes(JSON.stringify(data.encryptData), aeskey)
    };
  }
  const res = await utils_request.request({
    url: BASEURL + url,
    method,
    data: _data,
    // enableCookie:true,
    header: {
      Origin: ORIGIN
      // cookie: "JSESSIONID=F79DBE0A08214A885701D7BF030BD29Casfsa; accesstoken=F79DBE0A08214A885701D7BF030BD29Casf"
    },
    ...config
  });
  if (res.statusCode === 401) {
    const user = store_user.useUserStore();
    user.setUser({});
    utils_login.login.forceLogin();
    return { bizCode: "401" };
  }
  if (res == null ? void 0 : res.encryptData) {
    res.data = JSON.parse(utils_transitEncrypt.transitEncrypt.decryptAes(res.encryptData, aeskey));
    if (res.data.resultData && typeof res.data.resultData === "string") {
      res.data.resultData = JSON.parse(res.data.resultData);
    }
  }
  console.log(url, data, res);
  return res.data;
}
const $http = {
  get(url, data, config) {
    return _request(url, data, "GET", config);
  },
  async post(url, data, config) {
    return _request(url, data, "POST", config);
  }
};
function queryHallList(bizData) {
  return $http.post("/web/v1/queryHallList", handleData("P00001", bizData));
}
function queryHallDetail(bizData) {
  return $http.post("/web/v1/queryHallDetail", handleData("P00002", bizData));
}
function queryUserQueueInfo(bizData) {
  return $http.post("/web/v1/queryUserQueueInfo", handleData("P00003", bizData));
}
function queryUserAppRecords(bizData) {
  return $http.post("/web/v1/queryUserAppRecords", handleData("P00004", bizData));
}
function queryUserTakeRecords(bizData) {
  return $http.post("/web/v1/queryUserTakeRecords", handleData("P00005", bizData));
}
function queryAppTime(bizData) {
  return $http.post("/web/v1/queryAppTime", handleData("P00006", bizData));
}
function userAppointment(bizData) {
  return $http.post("/web/v1/userAppointment", handleData("P00007", bizData));
}
function cancelAppointment(bizData) {
  return $http.post("/web/v1/cancelAppointment", handleData("P00008", bizData));
}
function cancelTakeNumber(bizData) {
  return $http.post("/web/v1/cancelTakeNumber", handleData("P00008", bizData));
}
function userTakeNumber(bizData, province) {
  return $http.post("/web/v1/userTakeNumber", handleData("P00009", bizData, province), { timeout: 45e3 });
}
function agentTakeNumber(bizData) {
  return $http.post("/web/v1/agentTakeNumber", handleData("P00010", bizData));
}
function queryHallQueueInfo(bizData) {
  return $http.post("/web/v1/queryHallQueueInfo", handleData("P00011", bizData));
}
function checkWhite(bizData) {
  return $http.post("/web/v1/checkWhite", handleData("P00016", bizData));
}
function getAdvertisementInfo(bizData) {
  return $http.post("/web/v1/getAdvertisementInfo", handleData("P00038", bizData));
}
function getCityInfo(bizData) {
  return $http.post("/web/v1/getCityInfo", handleData("P00039", bizData));
}
function getCityInfoByCityName(bizData, provinceCode) {
  return $http.post("/web/v1/getCityInfoByCityName", handleData("P00062", bizData, provinceCode));
}
function validateToken(bizData) {
  return $http.post("/web/v1/uni/sso/validateToken", handleData("P00061", bizData));
}
function getEmpowermentInfo(bizData) {
  return $http.post("/web/v1/queryEmpInfo", handleData("P00045", bizData));
}
function getUserVoucher(bizData) {
  return $http.post("/web/v1/member/user/voucher/get", handleData("P00046", bizData));
}
function getClerkVoucher(bizData) {
  return $http.post("/web/v1/member/clerk/voucher/get", handleData("P00047", bizData));
}
function queryWorkPeriodInfo(bizData) {
  return $http.post("/web/v1/queryWorkPeriodInfo", handleData("P00055", bizData));
}
function queryHomeData(bizData) {
  return $http.post("/web/v1/queryHallHome", handleData("P00071", bizData));
}
function queryHomeDataWithQueueNumber(bizData) {
  return $http.post("/web/v1/queryHallHomeSc", handleData("P00072", bizData));
}
function getCityInfoByCode(bizData) {
  return $http.post("/web/v1/getCityInfoByCityCode", handleData("P00062", bizData));
}
const paiduiApi = {
  queryHomeData,
  queryHomeDataWithQueueNumber,
  queryHallList,
  queryHallDetail,
  queryUserQueueInfo,
  queryUserAppRecords,
  queryUserTakeRecords,
  queryAppTime,
  userAppointment,
  cancelAppointment,
  cancelTakeNumber,
  userTakeNumber,
  agentTakeNumber,
  queryHallQueueInfo,
  checkWhite,
  getAdvertisementInfo,
  getCityInfo,
  validateToken,
  getEmpowermentInfo,
  getUserVoucher,
  getClerkVoucher,
  queryWorkPeriodInfo,
  getCityInfoByCode,
  getCityInfoByCityName
  // placesearch
};
exports.getCityInfo = getCityInfo;
exports.paiduiApi = paiduiApi;
