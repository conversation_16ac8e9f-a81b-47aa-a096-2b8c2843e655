.content-item{
  display: flex;
  margin-top: 30px;
  line-height: 40px;
  .label-con{
    display: flex;
    .label{
      font-size: 28px;
      color: rgba(0,0,0,0.6);
      min-width: 168px;
      margin-left:12px;
    }
  }
  .value{
    flex:1;
    text-align: right;
    font-size: 28px;
    color: #000000;
    font-weight: bold;
  }
}
.left-box{
  width:6px;
  height:23px;
  background: linear-gradient( 360deg, #5DADFF 0%, #007EFF 100%);
  border-radius: 90px 90px 90px 90px;
  margin-top:7px;
}
.content-more{
  margin:53px auto 54px;
  height:2px;
  border-top: 2px dashed #CDCDCD;
  position: relative;
  .content-more-con{
    width:149px;
    height:47px;
    position:absolute;
    left:50%;
    top:0;
    transform: translate(-50%,-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    font-size: 24px;
    .content-more-text{
      margin-right:6px;
    }

  }
  .icon-cc-arrow-down-circle{
    font-size: 20px;
    font-weight: bold;
    &.arrow-up{
      transform: rotate(180deg);
    }
  }
}
.wait-detail{
  margin-bottom: 30px;
}
.wait-detail-title{
  color: #000000;
  font-size: 28px;
  line-height: 40px;
  display: flex;
  margin-top:-12px;
  margin-bottom: 18px;
  .wait-detail-title-label{
    margin:0 12px 0 18px;
    font-weight: bold;
  }
  .icon-refresh{
    color: #007EFF;
    font-size: 32px;
  }

}
.wait-detail-item{
  color: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  margin-bottom: 13px;
  line-height: 40px;
  .label{
    flex:1;
    color: rgba(0,0,0,0.6);
    font-size: 28px;
  }
  .value{
    color: #F3513B;
    font-size: 28px;
  }
}
